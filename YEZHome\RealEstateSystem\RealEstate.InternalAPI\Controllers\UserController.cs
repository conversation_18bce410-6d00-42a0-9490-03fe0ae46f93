﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using RealEstate.Application.DTO;
using RealEstate.Application.Interfaces;

namespace RealEstate.InternalAPI.Controllers
{
    
    public class UserController : BaseController
    {
        private readonly IUserService _userService;
        private readonly IUserDashboardService _dashboardService;

        public UserController(IUserService userService, IUserDashboardService dashboardService)
        {
            _userService = userService;
            _dashboardService = dashboardService;
        }

        [HttpPut("role")]
        public async Task<ActionResult<AddUserRoleDto>> AddUserRole(AddUserRoleDto addUserRoleDto)
        {
            try
            {
                bool isOk = await _userService.AddUserRoleAsync(addUserRoleDto);
                return Ok(isOk);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        [HttpGet("{id}")]
        public async Task<IActionResult> GetUser(Guid id)
        {
            try
            {
                var user = await _userService.GetUserByIdAsync(id);
                if (user == null)
                {
                    return NotFound();
                }
                return Ok(user);
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"An error occurred: {ex.Message}");
            }
        }

        [HttpGet("dashboard/{userId}")]
        public async Task<ActionResult<UserDashboardDto>> GetUserDashboardById(Guid userId)
        {
            try
            {
                var dashboard = await _dashboardService.GetUserDashboardAsync(userId);
                return Ok(dashboard);
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(ex.Message);
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"An error occurred: {ex.Message}");
            }
        }

        [HttpPost("reactivate/{userId}")]
        [Authorize(Policy = "AdminOnly")]
        public async Task<ActionResult> ReactivateAccount(Guid userId)
        {
            try
            {
                var result = await _userService.ReactivateUserAsync(userId);
                if (!result)
                {
                    return BadRequest("Failed to reactivate account. User not found.");
                }

                return Ok(new { Message = "Account has been reactivated successfully." });
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"An error occurred: {ex.Message}");
            }
        }        

        [HttpGet("{id}/invoice-info")]
        [Authorize(Policy = "AdminOnly")]
        public async Task<ActionResult<UserInvoiceInfoDto>> GetUserInvoiceInfo(Guid id)
        {
            try
            {
                var invoiceInfo = await _userService.GetUserInvoiceInfoAsync(id);
                if (invoiceInfo == null)
                {
                    return NotFound("User not found");
                }

                return Ok(invoiceInfo);
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"An error occurred: {ex.Message}");
            }
        }
    }
}
