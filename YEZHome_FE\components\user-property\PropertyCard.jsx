"use client";
import { memo, useState } from "react";
import {
  Building2,
  Clock,
  Edit2,
  LoaderCircle,
  Menu,
  Phone,
  Stamp,
  Trash2,
  CalendarDays,
  Zap,
  Share2,
  RefreshCcw,
} from "lucide-react";
import { updatePropertyHighlight } from "@/app/actions/server/property";
import { useToast } from "@/hooks/use-toast";
import { useTranslations } from "next-intl";

import ShareModal from "./ShareModal";
import HighlightConfirmDialog from "./HighlightConfirmDialog";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { Card, CardContent, CardFooter } from "@/components/ui/card";
import BadgeStatus from "@/components/layout/BadgeStatus";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { But<PERSON> } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { cn, formatCurrency, formatDate } from "@/lib/utils";
import PropertyReportCard from "./PropertyReportCard";
import { CAN_NOT_SEND_TO_REVIEW_STATUS } from "@/lib/enum";

// Helper function to calculate remaining days
const calculateRemainingDays = (expiresAt) => {
  if (!expiresAt) return null;
  const today = new Date();
  const expiryDate = new Date(expiresAt);
  const diffTime = expiryDate - today;
  if (diffTime <= 0) return 0; // Expired
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  return diffDays;
};

const PropertyCard = memo(
  ({
    property,
    onEdit,
    onDelete,
    onSendToReview,
    onShowContacts,
    onShowHistory,
    loadingId,
    isSelected,
    onCheckboxChange,
    canNotSendToReviewStatus = CAN_NOT_SEND_TO_REVIEW_STATUS,
  }) => {
    const placeData = JSON.parse(property?.placeData || "{}");
    const remainingDays = calculateRemainingDays(property.expiresAt);

    // Handlers for property actions
    const { toast } = useToast();
    const [isHighlightLoading, setIsHighlightLoading] = useState(false);
    const [isShareModalOpen, setIsShareModalOpen] = useState(false);
    const [isHighlightDialogOpen, setIsHighlightDialogOpen] = useState(false);

    // Get translations from hooks
    const t  = useTranslations("PropertyCard");
    const tCommon = useTranslations("Common");
    const status = property?.status || PropertyStatus.DRAFT;
    const statusText = property?.status ? tCommon(`propertyStatus_${property?.status}`) : tCommon(`propertyStatus_${PropertyStatus.DRAFT}`);

    // Open highlight confirmation dialog
    const handleHighlightClick = () => {
      // Only allow highlighting if the property is not already highlighted
      if (!property.isHighlighted) {
        setIsHighlightDialogOpen(true);
      }
    };

    // Handle highlight action after confirmation
    const handleHighlightConfirm = async () => {
      setIsHighlightLoading(true);
      try {
        // Always set isHighlighted to true - we don't support unhighlighting
        const result = await updatePropertyHighlight(property.id, true);
        if (result.success) {
          toast({
            description: t("highlightAddSuccess"),
            className: "bg-teal-600 text-white",
          });
          // Refresh the page to show updated property status
          window.location.reload();
        } else {
          toast({
            description: result.message || t("highlightUpdateError"),
            variant: "destructive",
          });
        }
      } catch (error) {
        console.error("Error updating highlight status:", error);
        toast({
          description: t("highlightGenericError"),
          variant: "destructive",
        });
      } finally {
        setIsHighlightLoading(false);
      }
    };

    // Handle renew action - currently just a placeholder
    const handleRenew = () => {
      toast({
        description: t("renewFeatureInDevelopment"),
        variant: "default",
      });
    };

    // Handle share action
    const handleShare = () => {
      setIsShareModalOpen(true);
    };

    return (
      <>
        {/* Share Modal */}
        {isShareModalOpen && (
          <ShareModal
            open={isShareModalOpen}
            onClose={() => setIsShareModalOpen(false)}
            property={property}
          />
        )}

        {/* Highlight Confirmation Dialog */}
        <HighlightConfirmDialog
          open={isHighlightDialogOpen}
          onClose={() => setIsHighlightDialogOpen(false)}
          onConfirm={handleHighlightConfirm}
        />

        <Card className="overflow-hidden flex flex-col md:flex-row w-full">
          {/* Checkbox + Image Section */}
          <div className="flex items-center p-2 md:p-4 border-b md:border-b-0 md:border-r">
            <Checkbox
              id={`checkbox-${property.id}`}
              checked={isSelected}
              onCheckedChange={(checked) => onCheckboxChange(property.id, checked)}
              aria-label={`Select property ${property.name}`}
              className="mr-4"
            />
            <div className="flex flex-col">
              <div className="h-24 w-24 md:h-32 md:w-32 relative overflow-hidden rounded-md">
                <img
                  src={
                    property.propertyMedia?.[0]?.mediaURL || "/placeholder.svg?height=100&width=100"
                  }
                  alt={property.name}
                  className="h-full w-full object-cover rounded"
                />
              </div>
            </div>
          </div>

          {/* Info + Report Container - Stacks vertically on mobile, row on md+ */}
          <div className="flex flex-col md:flex-row flex-grow">
            {/* Left Section: Property Info */}
            <CardContent className="grid gap-1 p-4 flex-grow border-b md:border-r md:border-b-0">
              <div className="flex">
                <BadgeStatus
                  rounded="full"
                  status={status}
                  statusText={statusText}
                />
                {property.isHighlighted && <BadgeStatus statusText={tCommon("highlight_status")} />}
              </div>

              <h4 className="line-clamp-2 font-semibold text-base md:text-lg text-charcoal">
                {property.name}
              </h4>
              {property.price && (
                <p className="font-medium text-red-600">{formatCurrency(property.price)}</p>
              )}
              {placeData?.result?.formatted_address && (
                <div className="flex items-start gap-1 text-xs text-gray-500">
                  <Building2 className="mt-0.5 h-3 w-3 shrink-0" />
                  <span className="line-clamp-1">{placeData.result.formatted_address}</span>
                </div>
              )}
              <div className="flex items-center gap-1 text-xs text-gray-500">
                <CalendarDays className="h-3 w-3 shrink-0" />
                <span>{t("postDate")}: {formatDate(property.createdAt)}</span>
              </div>
              <div className="flex items-center gap-1 text-xs text-gray-500">
                <CalendarDays className="h-3 w-3 shrink-0" />
                <span>{t("expiryDate")}: {formatDate(property.expiresAt)}</span>  
              </div>
              {remainingDays !== null && (
                <div
                  className={`flex items-center gap-1 text-xs ${
                    remainingDays <= 7 ? "text-orange-600 font-medium" : "text-gray-500"
                  }`}
                >
                  <Clock className="h-3 w-3 shrink-0" />
                  <span>{remainingDays > 0 ? t("daysRemaining", { days: remainingDays }) : t("expired")}</span>
                </div>
              )}
            </CardContent>

            {/* Right Section: Report Info - Use the new async component */}
            <PropertyReportCard propertyId={property.id} />
          </div>

          {/* Actions Section */}
          <CardFooter className="flex flex-col md:flex-col items-stretch md:items-center justify-center p-2 md:p-4 gap-2 border-t md:border-t-0 md:border-l">
            {/* Button group 1 - Stretches on mobile */}
            <div className="flex gap-2 justify-stretch w-full md:flex-col md:justify-center">
              {!property.isHighlighted ? (
                <Button
                  size="sm"
                  variant="outline"
                  className="flex-1 gap-1 text-xs px-2 py-1 h-auto"
                  onClick={handleHighlightClick}
                  disabled={isHighlightLoading || loadingId === property.id}
                >
                  {isHighlightLoading ? (
                    <LoaderCircle className="animate-spin h-3 w-3 mr-1" />
                  ) : (
                    <Zap className="h-3 w-3 text-yellow-500" />
                  )}
                  {tCommon("highlight_status")}
                </Button>
              ) : (
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        size="sm"
                        variant="outline"
                        className="flex-1 gap-1 text-xs px-2 py-1 h-auto"
                        disabled={true}
                      >
                        <Zap className="h-3 w-3 text-yellow-500" />
                        {tCommon("highlight_status")}
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>{t("alreadyHighlighted")}</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              )}
              <Button
                size="sm"
                variant="outline"
                className="flex-1 gap-1 text-xs px-2 py-1 h-auto"
                onClick={handleRenew}
              >
                <RefreshCcw className="h-3 w-3 text-blue-500" /> {t("renew")}
              </Button>
            </div>
            {/* Button group 2 - Stretches on mobile */}
            <div className="flex gap-2 justify-stretch w-full md:flex-col md:justify-center">
              <Button
                size="sm"
                variant="outline"
                className="flex-1 gap-1 text-xs px-2 py-1 h-auto"
                onClick={() => onEdit(property.id)}
              >
                <Edit2 className="h-3 w-3" /> {t("edit")}
              </Button>
            </div>
            {/* Button group 3 - Stretches on mobile */}
            <div className="flex gap-2 justify-stretch w-full md:flex-col md:justify-center">
              <Button
                size="sm"
                variant="outline"
                className="flex-1 gap-1 text-xs px-2 py-1 h-auto"
                onClick={handleShare}
              >
                <Share2 className="h-3 w-3 text-green-500" /> {t("share")}
              </Button>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    size="sm"
                    variant="outline"
                    className="flex-1 gap-1 text-xs px-2 py-1 h-auto"
                    aria-label="More actions"
                  >
                    <Menu className="h-3 w-3" /> {t("more")}
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent>
                  {/* Existing actions */}
                  <DropdownMenuItem
                    onClick={() => onSendToReview(property.id)}
                    disabled={
                      loadingId === property.id ||
                      canNotSendToReviewStatus.includes(property.status)
                    }
                    className={cn(
                      canNotSendToReviewStatus.includes(property.status) &&
                        "cursor-not-allowed opacity-50"
                    )}
                  >
                    {loadingId === property.id ? (
                      <span className="flex items-center">
                        <LoaderCircle className="animate-spin h-4 w-4 mr-2"></LoaderCircle>
                        {tCommon("loading")}
                      </span>
                    ) : (
                      <>
                        <Stamp className="h-4 w-4 mr-2" />
                        {canNotSendToReviewStatus.includes(property.status)
                          ? t("cannotRequestVerification")
                          : t("requestVerification")}
                      </>
                    )}
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => onShowContacts(property.id)}>
                    <Phone className="h-4 w-4 mr-2" />
                    {t("requestContact")}
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => onShowHistory(property.id)}>
                    <Clock className="h-4 w-4 mr-2" /> {t("activityHistory")}
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem
                    className="text-red-700"
                    onClick={() => onDelete(property.id)}
                    disabled={loadingId === property.id}
                  >
                    {loadingId === property.id ? (
                      <span className="flex items-center">
                        <LoaderCircle className="animate-spin h-4 w-4 mr-2"></LoaderCircle>
                        {t("deleting")}
                      </span>
                    ) : (
                      <>
                        <Trash2 className="h-4 w-4 mr-2" /> {t("delete")}
                      </>
                    )}
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </CardFooter>
        </Card>
      </>
    );
  }
);

PropertyCard.displayName = "PropertyCard";

export default PropertyCard;
