﻿using RealEstate.Application.DTO;

namespace RealEstate.Application.Interfaces
{
    public interface IOwnerReviewService
    {
        Task<IEnumerable<OwnerReviewDto>> GetAllReviewsAsync();
        Task<OwnerReviewDto> GetReviewByIdAsync(Guid id);
        Task<IEnumerable<OwnerReviewDto>> GetReviewsByOwnerIdAsync(Guid ownerId);
        Task<IEnumerable<OwnerReviewDto>> GetReviewsByBuyerIdAsync(Guid propertyId);
        Task<OwnerReviewDto> CreateReviewAsync(CreateOwnerReviewDto reviewDto, Guid userId);
        Task<bool> UpdateReviewAsync(Guid id, CreateOwnerReviewDto reviewDto, Guid userId);
        Task<bool> DeleteReviewAsync(Guid reviewId, Guid userId);
    }
}
