﻿using AutoMapper;
using RealEstate.Application.DTO;
using RealEstate.Application.Interfaces;
using RealEstate.Domain.Entities;
using RealEstate.Domain.Interfaces;

namespace RealEstate.Application.Services
{
    public class OwnerReviewService : IOwnerReviewService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly IMapper _mapper;

        public OwnerReviewService(IUnitOfWork unitOfWork, IMapper mapper)
        {
            _unitOfWork = unitOfWork;
            _mapper = mapper;
        }

        public async Task<IEnumerable<OwnerReviewDto>> GetReviewsByOwnerIdAsync(Guid ownerId)
        {
            var reviews = await _unitOfWork.OwnerReviews.FindAsync(r => r.OwnerID == ownerId);
            return _mapper.Map<IEnumerable<OwnerReviewDto>>(reviews);
        }

        public async Task<OwnerReviewDto> CreateReviewAsync(CreateOwnerReviewDto reviewDto)
        {
            var review = _mapper.Map<OwnerReview>(reviewDto);
            await _unitOfWork.OwnerReviews.AddAsync(review);
            await _unitOfWork.SaveChangesAsync();
            return _mapper.Map<OwnerReviewDto>(review);
        }

        public async Task<bool> DeleteReviewAsync(Guid reviewId)
        {
            var review = await _unitOfWork.OwnerReviews.GetByIdAsync(reviewId);
            if (review == null) return false;
            _unitOfWork.OwnerReviews.Remove(review);
            await _unitOfWork.SaveChangesAsync();
            return true;
        }

        public Task<IEnumerable<OwnerReviewDto>> GetAllReviewsAsync()
        {
            throw new NotImplementedException();
        }

        public Task<OwnerReviewDto> GetReviewByIdAsync(Guid id)
        {
            throw new NotImplementedException();
        }

        public Task<IEnumerable<OwnerReviewDto>> GetReviewsByBuyerIdAsync(Guid propertyId)
        {
            throw new NotImplementedException();
        }

        public Task<OwnerReviewDto> CreateReviewAsync(CreateOwnerReviewDto reviewDto, Guid userId)
        {
            throw new NotImplementedException();
        }

        public Task<bool> UpdateReviewAsync(Guid id, CreateOwnerReviewDto reviewDto, Guid userId)
        {
            throw new NotImplementedException();
        }

        public Task<bool> DeleteReviewAsync(Guid reviewId, Guid userId)
        {
            throw new NotImplementedException();
        }
    }
}
