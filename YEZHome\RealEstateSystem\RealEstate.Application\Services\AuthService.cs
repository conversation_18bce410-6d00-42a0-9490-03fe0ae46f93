﻿using RealEstate.Application.DTO;
using RealEstate.Application.Interfaces;
using RealEstate.Domain.Common;
using RealEstate.Domain.Entities;
using RealEstate.Domain.Interfaces;
using System.Security.Cryptography;
using System.Text;

namespace RealEstate.Application.Services
{
    public class AuthService : IAuthService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ITokenService _tokenService;

        public AuthService(IUnitOfWork unitOfWork, ITokenService tokenService)
        {
            _unitOfWork = unitOfWork;
            _tokenService = tokenService;
        }

        public async Task<UserDto> RegisterAsync(CreateUserDto registerDto)
        {
            if (await _unitOfWork.AppUsers.EmailExistsAsync(registerDto.Email))
                throw new InvalidOperationException("Email already exists");

            // Generate salt and hash
            var salt = GenerateRandomSalt();
            var hashedPassword = HashPassword(registerDto.Password, salt);

            var user = new AppUser
            {
                Email = registerDto.Email.ToLower(),
                FullName = registerDto.FullName,
                PasswordHash = hashedPassword,
                PasswordSalt = salt,
                CreatedAt = DateTime.UtcNow,
                CreatedBy = null,
                Phone = registerDto.Phone,
                UserType = registerDto.UserType ?? EnumValues.UserType.Buyer.ToString(),
            };

            await _unitOfWork.AppUsers.AddAsync(user);

            // Create default notification preferences for the user
            var notificationPreferences = new NotificationPreference
            {
                UserId = user.Id,
                ReceivePromotions = true,
                ReceiveWalletUpdates = true,
                ReceiveNews = true,
                ReceiveCustomerMessages = true,
                CreatedAt = DateTime.UtcNow
            };

            await _unitOfWork.NotificationPreferences.AddAsync(notificationPreferences);

            // Create default wallet for the user
            var wallet = new Wallet
            {
                UserId = user.Id,
                Balance = 0
            };

            await _unitOfWork.Wallets.AddAsync(wallet);

            await _unitOfWork.SaveChangesAsync();

            var accessToken = _tokenService.CreateToken(user);

            return new UserDto
            {
                Id = user.Id,
                Email = user.Email,
                FullName = user.FullName,
                Phone = user.Phone,
                LastLogin = null,
                Token = accessToken,
                IsActive = user.IsActive,
                UserType = user.UserType ?? EnumValues.UserType.Buyer.ToString(),
            };
        }

        public async Task<UserDto> LoginAsync(LoginDto loginDto)
        {
            var user = await _unitOfWork.AppUsers.GetByEmailAsync(loginDto.Email, isIncludeRole: true);
            if (user == null)
                throw new UnauthorizedAccessException("Thông tin đăng nhập không đúng");

            var hashedPassword = HashPassword(loginDto.Password, user.PasswordSalt);
            if (hashedPassword != user.PasswordHash)
                throw new UnauthorizedAccessException("Thông tin đăng nhập không đúng");

            // Check if the user account is active
            if (!user.IsActive)
                throw new UnauthorizedAccessException("Tài khoản của bạn đã bị vô hiệu hóa. Vui lòng liên hệ quản trị viên để kích hoạt lại.");

            var accessToken = _tokenService.CreateToken(user);

            // Update last login time
            user.LastLogin = DateTime.UtcNow;
            _unitOfWork.AppUsers.Update(user);
            await _unitOfWork.SaveChangesAsync();

            return new UserDto
            {
                Id = user.Id,
                Email = user.Email,
                FullName = user.FullName,
                Phone = user.Phone,
                LastLogin = user.LastLogin,
                Token = accessToken,
                IsActive = user.IsActive,
                UserType = user.UserType ?? EnumValues.UserType.Buyer.ToString(),
            };
        }

        public async Task<UserDto> ChangePassword(ChangePasswordDto changePasswordDto)
        {
            var user = await _unitOfWork.AppUsers.GetByEmailAsync(changePasswordDto.Email, asNoTracking: false);
            if (user == null)
                throw new UnauthorizedAccessException("Thông tin đăng nhập không đúng");

            var hashedOldPassword = HashPassword(changePasswordDto.OldPassword, user.PasswordSalt);
            if (hashedOldPassword != user.PasswordHash)
                throw new UnauthorizedAccessException("Mật khẩu cũ không đúng");

            // Generate new salt and hash the new password
            var newSalt = GenerateRandomSalt();
            var newHashedPassword = HashPassword(changePasswordDto.NewPassword, newSalt);

            user.PasswordSalt = newSalt;
            user.PasswordHash = newHashedPassword;

            _unitOfWork.AppUsers.Update(user);
            await _unitOfWork.SaveChangesAsync();

            return new UserDto
            {
                Id = user.Id,
                Email = user.Email,
                FullName = user.FullName,
                Phone=user.Phone,
                LastLogin = user.LastLogin,
                Token = _tokenService.CreateToken(user),
            };
        }

        public async Task<UserDto> RefreshToken(Guid userId)
        {
            var user = await _unitOfWork.AppUsers.GetByIdAsync(userId);
            if (user == null)
                throw new UnauthorizedAccessException("User not found");
            return new UserDto
            {
                Id = user.Id,
                Email = user.Email,
                FullName = user.FullName,
                Phone = user.Phone,
                LastLogin = user.LastLogin,
                Token = _tokenService.CreateToken(user),
            };
        }

        private static string GenerateRandomSalt()
        {
            byte[] salt = new byte[128 / 8];
            using (var rng = RandomNumberGenerator.Create())
            {
                rng.GetBytes(salt);
            }
            return Convert.ToBase64String(salt);
        }

        private static string HashPassword(string password, string salt)
        {
            using (var sha256 = SHA256.Create())
            {
                var saltedPassword = string.Concat(password, salt);
                var hashedBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(saltedPassword));
                return Convert.ToBase64String(hashedBytes);
            }
        }

        public async Task<bool> ValidateUserCredentialsAsync(string email, string password)
        {
            var user = await _unitOfWork.AppUsers.GetByEmailAsync(email);
            if (user == null)
                return false;

            var hashedPassword = HashPassword(password, user.PasswordSalt);
            return hashedPassword == user.PasswordHash;
        }
    }
}
