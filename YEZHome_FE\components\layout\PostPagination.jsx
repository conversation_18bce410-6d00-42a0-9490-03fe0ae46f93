import React from "react";
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "../ui/pagination";

export default function PostPagination({
  currentPage,
  totalPages,
  sortColumn,
  sortDescending,
  title,
}) {
  return (
    <Pagination>
      <PaginationContent>
        <PaginationItem>
          <PaginationPrevious
            href={`/blog?page=${currentPage - 1}${
              title ? `&title=${encodeURIComponent(title)}` : ""
            }`}
            isActive={currentPage > 1}
          />
        </PaginationItem>
        {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
          <PaginationItem key={page}>
            <PaginationLink
              href={`/blog?page=${page}${title ? `&title=${encodeURIComponent(title)}` : ""}`}
              isActive={currentPage === page}
            >
              {page}
            </PaginationLink>
          </PaginationItem>
        ))}
        <PaginationItem>
          <PaginationNext
            href={`/blog?page=${currentPage + 1}${
              title ? `&tag=${encodeURIComponent(title)}` : ""
            }`}
            isActive={currentPage < totalPages}
          />
        </PaginationItem>
      </PaginationContent>
    </Pagination>
  );
}
