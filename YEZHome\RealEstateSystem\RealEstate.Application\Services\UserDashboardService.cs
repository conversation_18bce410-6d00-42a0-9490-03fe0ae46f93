using AutoMapper;
using Microsoft.EntityFrameworkCore;
using RealEstate.Application.DTO;
using RealEstate.Application.Interfaces;
using RealEstate.Domain.Common;
using RealEstate.Domain.Entities;
using RealEstate.Domain.Interfaces;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace RealEstate.Application.Services
{
    public class UserDashboardService : IUserDashboardService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly IMapper _mapper;

        public UserDashboardService(IUnitOfWork unitOfWork, IMapper mapper)
        {
            _unitOfWork = unitOfWork;
            _mapper = mapper;
        }

        public async Task<UserDashboardDto> GetUserDashboardAsync(Guid userId)
        {
            var user = await _unitOfWork.AppUsers.GetByIdAsync(userId);
            if (user == null)
            {
                throw new KeyNotFoundException($"User with ID {userId} not found");
            }

            var dashboard = new UserDashboardDto
            {
                UserInfo = await GetUserInfoAsync(userId),
                WalletInfo = await GetUserWalletInfoAsync(userId),
                PropertyStats = await GetUserPropertyStatsAsync(userId),
                RecentTransactions = await GetUserTransactionsAsync(userId, 5),
                MemberRanking = await GetUserMemberRankingInfoAsync(userId)
            };

            return dashboard;
        }

        private async Task<UserInfoDto> GetUserInfoAsync(Guid userId)
        {
            var user = await _unitOfWork.AppUsers.GetByIdAsync(userId);
            var userRoles = await _unitOfWork.UserRoles.GetQueryable()
                .Where(ur => ur.UserID == userId)
                .Select(ur => ur.Role.RoleName)
                .ToListAsync();

            return new UserInfoDto
            {
                Id = user.Id,
                FullName = user.FullName,
                Email = user.Email,
                Phone = user.Phone,
                UserType = user.UserType,
                MemberRank = user.MemberRank,
                LastLogin = user.LastLogin,
                CreatedAt = user.CreatedAt,
                Roles = userRoles
            };
        }

        public async Task<WalletInfoDto> GetUserWalletInfoAsync(Guid userId)
        {
            var wallet = await _unitOfWork.Wallets.GetQueryable()
                .FirstOrDefaultAsync(w => w.UserId == userId);

            var user = await _unitOfWork.AppUsers.GetByIdAsync(userId);

            var transactions = await _unitOfWork.WalletTransactions.GetQueryable()
                .Where(wt => wt.UserId == userId)
                .ToListAsync();

            var lastMonthStart = DateTime.UtcNow.AddMonths(-1).Date;
            var lastMonthSpending = transactions
                .Where(t => t.CreatedAt >= lastMonthStart && t.Type == "spend")
                .Sum(t => t.Amount);

            return new WalletInfoDto
            {
                Balance = wallet?.Balance ?? 0,
                TotalSpent = user.TotalSpent,
                TotalTransactions = transactions.Count,
                LastMonthSpending = lastMonthSpending
            };
        }

        public async Task<PropertyStatsDto> GetUserPropertyStatsAsync(Guid userId)
        {
            var properties = await _unitOfWork.Properties.GetQueryable()
                .Where(p => p.OwnerID == userId && !p.IsDeleted)
                .ToListAsync();

            var favorites = await _unitOfWork.UserFavorites.GetQueryable()
                .CountAsync(f => f.UserID == userId);

            var now = DateTime.UtcNow;

            return new PropertyStatsDto
            {
                TotalProperties = properties.Count,
                ActiveProperties = properties.Count(p => p.Status == EnumValues.PropertyStatus.Approved.ToString() && p.ExpiresAt > now),
                ExpiredProperties = properties.Count(p => p.ExpiresAt <= now),
                DraftProperties = properties.Count(p => p.Status == EnumValues.PropertyStatus.Draft.ToString()),
                FavoriteProperties = favorites,
                // If you track property views, you could calculate this
                TotalViews = 0, // Placeholder
                AverageRating = properties.Count > 0 && properties.Any(p => p.PropertyReviews.Count > 0)
                    ? (decimal)properties.SelectMany(p => p.PropertyReviews).Average(r => r.Rating)
                    : 0
            };
        }

        public async Task<List<WalletTransactionDto>> GetUserTransactionsAsync(Guid userId, int count = 10)
        {
            var transactions = await _unitOfWork.WalletTransactions.GetQueryable()
                .Where(wt => wt.UserId == userId)
                .OrderByDescending(wt => wt.CreatedAt)
                .Take(count)
                .ToListAsync();

            return _mapper.Map<List<WalletTransactionDto>>(transactions);
        }

        public async Task<MemberRankingDto> GetUserMemberRankingInfoAsync(Guid userId)
        {
            var user = await _unitOfWork.AppUsers.GetByIdAsync(userId);
            var currentRanking = await _unitOfWork.MemberRankings.GetQueryable()
                .FirstOrDefaultAsync(r => r.RankName == user.MemberRank);

            // Find the next rank based on spending thresholds
            var nextRanking = await _unitOfWork.MemberRankings.GetQueryable()
                .Where(r => r.MinSpent > user.TotalSpent)
                .OrderBy(r => r.MinSpent)
                .FirstOrDefaultAsync();

            decimal progressPercentage = 0;
            decimal? spendingToNextRank = null;

            if (nextRanking != null && currentRanking != null && currentRanking.MinSpent.HasValue && nextRanking.MinSpent.HasValue)
            {
                spendingToNextRank = nextRanking.MinSpent - user.TotalSpent;

                var rankingRange = nextRanking.MinSpent.Value - currentRanking.MinSpent.Value;
                var userProgress = user.TotalSpent - currentRanking.MinSpent.Value;

                progressPercentage = rankingRange > 0
                    ? Math.Min(100, (userProgress / rankingRange) * 100)
                    : 100;
            }
            else if (currentRanking != null && currentRanking.MaxSpent.HasValue)
            {
                // User is at the maximum rank
                progressPercentage = 100;
            }

            return new MemberRankingDto
            {
                CurrentRank = user.MemberRank,
                NextRank = nextRanking?.RankName,
                SpendingToNextRank = spendingToNextRank,
                MinSpent = currentRanking?.MinSpent,
                MaxSpent = currentRanking?.MaxSpent,
                ProgressPercentage = (double)progressPercentage
            };
        }

        public async Task<MonthlySpendingDto> GetMonthlySpendingAsync(Guid userId, int year)
        {
            // Get all transactions for the user in the specified year
            var startDate = new DateTime(year, 1, 1);
            var endDate = new DateTime(year, 12, 31, 23, 59, 59);

            var transactions = await _unitOfWork.WalletTransactions.GetQueryable()
                .Where(wt => wt.UserId == userId &&
                       wt.CreatedAt >= startDate &&
                       wt.CreatedAt <= endDate)
                .ToListAsync();

            // Group transactions by month
            var groupedByMonth = transactions
                .GroupBy(t => t.CreatedAt.Month)
                .Select(g => new
                {
                    Month = g.Key,
                    Transactions = g.ToList()
                })
                .ToList();

            // Create monthly spending details
            var monthlyDetails = new List<MonthlySpendingDetailDto>();
            for (int month = 1; month <= 12; month++)
            {
                var monthData = groupedByMonth.FirstOrDefault(g => g.Month == month);
                var spendTransactions = monthData?.Transactions.Where(t => t.Type == "spend").ToList() ?? new List<WalletTransaction>();

                monthlyDetails.Add(new MonthlySpendingDetailDto
                {
                    Month = month,
                    Name = new DateTime(year, month, 1).ToString("MMMM"),
                    TotalSpent = spendTransactions.Sum(t => t.Amount),
                    TransactionCount = spendTransactions.Count
                });
            }

            // Calculate total yearly spending
            var totalYearlySpending = monthlyDetails.Sum(m => m.TotalSpent);

            return new MonthlySpendingDto
            {
                Year = year,
                Months = monthlyDetails,
                TotalYearlySpending = totalYearlySpending
            };
        }

        public async Task<decimal> GetHighlightFeeByUserIdAsync(Guid userId)
        {
            // Get the user to determine their membership rank
            var user = await _unitOfWork.AppUsers.GetByIdAsync(userId);
            if (user == null)
            {
                throw new KeyNotFoundException($"User with ID {userId} not found");
            }

            // Get the highlight fee based on the user's membership rank
            var highlightFee = await _unitOfWork.HighlightFees.GetQueryable()
                .FirstOrDefaultAsync(f => f.RankName == user.MemberRank);

            if (highlightFee == null)
            {
                // Fallback to default fee if no specific fee is found for the user's rank
                highlightFee = await _unitOfWork.HighlightFees.GetQueryable()
                    .FirstOrDefaultAsync(f => f.RankName == "default");

                if (highlightFee == null)
                {
                    throw new InvalidOperationException("Could not determine highlight fee");
                }
            }

            return highlightFee.Fee;
        }

        public async Task<PropertyPerformanceDto> GetPropertyPerformanceAsync(Guid userId)
        {
            var properties = await _unitOfWork.Properties.GetQueryable()
                .Where(p => p.OwnerID == userId && !p.IsDeleted)
                .Include(p => p.PropertyReviews)
                .ToListAsync();

            if (properties == null || !properties.Any())
            {
                return new PropertyPerformanceDto
                {
                    TotalProperties = 0,
                    PropertiesPerformance = new List<PropertyPerformanceDetailDto>(),
                    BestPerforming = null,
                    NeedsAttention = new List<PropertyAttentionDto>()
                };
            }

            // Get favorites count for each property
            var favorites = await _unitOfWork.UserFavorites.GetQueryable()
                .Where(f => properties.Select(p => p.Id).Contains(f.PropertyID))
                .GroupBy(f => f.PropertyID)
                .Select(g => new { PropertyId = g.Key, Count = g.Count() })
                .ToListAsync();

            // Get contact requests count for each property
            // Note: You would need to implement this based on your contact request model
            // This is a placeholder implementation
            var contactRequests = new Dictionary<Guid, int>();
            foreach (var property in properties)
            {
                contactRequests[property.Id] = 0; // Replace with actual contact request query
            }

            // Prepare property performance details
            var performanceDetails = new List<PropertyPerformanceDetailDto>();
            var needsAttention = new List<PropertyAttentionDto>();

            var now = DateTime.UtcNow;

            foreach (var property in properties)
            {
                var favoriteCount = favorites.FirstOrDefault(f => f.PropertyId == property.Id)?.Count ?? 0;
                var contactRequestCount = contactRequests.ContainsKey(property.Id) ? contactRequests[property.Id] : 0;

                var reviews = property.PropertyReviews.ToList();
                var rating = reviews.Any() ? (decimal)reviews.Average(r => r.Rating) : 0;

                // Add to performance details
                performanceDetails.Add(new PropertyPerformanceDetailDto
                {
                    PropertyId = property.Id,
                    PropertyName = property.Name,
                    Views = 0, // Replace with actual view count if you track it
                    Favorites = favoriteCount,
                    ContactRequests = contactRequestCount,
                    Status = property.Status,
                    ExpiresAt = property.ExpiresAt,
                    Rating = rating,
                    ReviewCount = reviews.Count
                });

                // Check if property needs attention
                if (property.ExpiresAt <= now.AddDays(7))
                {
                    needsAttention.Add(new PropertyAttentionDto
                    {
                        PropertyId = property.Id,
                        PropertyName = property.Name,
                        Issue = "Expires soon",
                        ExpiresAt = property.ExpiresAt
                    });
                }
            }

            // Find best performing property (based on a simple scoring mechanism)
            var bestProperty = performanceDetails
                .OrderByDescending(p => p.Views + p.Favorites * 2 + p.ContactRequests * 3 + (p.Rating * p.ReviewCount))
                .FirstOrDefault();

            var bestPerforming = bestProperty != null
                ? new BestPerformingPropertyDto
                {
                    PropertyId = bestProperty.PropertyId,
                    PropertyName = bestProperty.PropertyName
                }
                : null;

            return new PropertyPerformanceDto
            {
                TotalProperties = properties.Count,
                PropertiesPerformance = performanceDetails,
                BestPerforming = bestPerforming,
                NeedsAttention = needsAttention
            };
        }
    }
}