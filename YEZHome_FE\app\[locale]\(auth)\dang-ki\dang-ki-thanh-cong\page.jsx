import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import {Link} from '@/i18n/navigation';
import { Mail, CheckCircle } from "lucide-react"
import { useTranslations } from "next-intl"

export default function VerifyEmail() {
  const t = useTranslations('VerifyEmail');

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-b from-slate-50 to-slate-100 p-4">
      <Card className="max-w-md w-full shadow-lg">
        <CardContent className="p-6">
          <div className="flex flex-col items-center text-center space-y-6">
            <div className="relative w-40 h-40 mb-2">
              <div className="absolute inset-0 flex items-center justify-center">
                <Mail className="h-20 w-20 text-emerald-500 opacity-20" />
              </div>
              <div className="absolute inset-0 flex items-center justify-center">
                <CheckCircle className="h-4 w-4 text-emerald-600" />
              </div>
            </div>

            <h1 className="text-2xl font-bold text-emerald-700">{t('title')}</h1>

            <div className="space-y-4 text-slate-700">
              <p>
                {t('message')}
              </p>
              <p className="text-amber-600 bg-amber-50 p-3 rounded-lg border border-amber-200 text-sm">
                {t('spamNotice')}
              </p>
            </div>

            <div className="pt-4 flex flex-col sm:flex-row gap-3 w-full">
              <Button asChild variant="outline" className="w-full">
                <Link href="/dang-nhap">{t('loginButton')}</Link>
              </Button>
              <Button asChild className="w-full bg-emerald-600 hover:bg-emerald-700">
                <Link href="/">{t('homeButton')}</Link>
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
