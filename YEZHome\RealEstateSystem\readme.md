# Real Estate Management System Documentation

## Project Overview

This project is a Real Estate Management System built with .NET Core, following a clean architecture pattern. The system allows property owners to list properties, buyers to browse and review properties, and includes additional features like blog posts, user management, and notifications.

## Architecture

The project follows a layered architecture with clear separation of concerns:

1. **Domain Layer** - Contains business entities and interfaces
2. **Application Layer** - Contains business logic, DTOs, and services
3. **Infrastructure Layer** - Contains data access implementation and external services
4. **API Layer** - Exposes the functionality through RESTful endpoints

## Core Components

### Domain Entities

The system includes several key entities:

- **AppUser** - Represents users in the system (buyers, owners, admins)
- **Property** - Real estate properties listed in the system
- **PropertyReview** - Reviews for properties submitted by buyers
- **OwnerReview** - Reviews for property owners submitted by buyers
- **BlogPost** - Blog content with author information
- **BlogComment** - Comments on blog posts
- **Notification** - System notifications for users

### Data Access

The project uses the Repository and Unit of Work patterns:

- **IRepository<T>** - Generic repository interface for basic CRUD operations
- **IAuditableRepository<T>** - Extended repository for entities with audit fields
- **IUnitOfWork** - Coordinates work across multiple repositories

### Application Services

Services implement business logic and act as intermediaries between controllers and repositories:

- **PropertyReviewService** - Manages property reviews
- **OwnerReviewService** - Manages owner reviews
- **User Services** - Handles user management and authentication

### API Features

- **JWT Authentication** - Secure authentication using JSON Web Tokens
- **CORS Support** - Cross-Origin Resource Sharing configuration
- **Swagger Documentation** - API documentation and testing interface
- **Error Handling** - Global exception middleware
- **Rate Limiting** - Protection against excessive requests

## Database Schema

The database uses PostgreSQL with tables for:

- Users and roles
- Properties and property media
- Reviews (property and owner)
- Blog posts and comments
- Notifications and preferences

Key relationships include:
- One-to-many between users and properties
- One-to-many between properties and reviews
- One-to-many between users and reviews

## Key Features

### Property Management
- Property listing and details
- Property media management
- Property status tracking

### Review System
- Property reviews with ratings
- Owner reviews with ratings

### User Management
- User registration and authentication
- Role-based authorization
- User profiles

### Blog System
- Blog post creation and management
- Commenting functionality

### Notification System
- User notifications
- Notification preferences

## Technical Implementation

### AutoMapper
Used for object-to-object mapping between entities and DTOs.

### Authentication & Authorization
JWT-based authentication with custom authorization policies.

### Data Validation
Model validation with custom error responses.

### Error Handling
Global exception middleware for consistent error responses.

## Future Enhancements

Based on the codebase review, potential enhancements could include:

1. Implementing missing functionality in the OwnerReviewService
2. Adding more comprehensive notification features
3. Enhancing search and filtering capabilities for properties
4. Implementing a dashboard for analytics
5. Adding payment processing for property transactions

## Conclusion

This Real Estate Management System provides a solid foundation for managing properties, users, and related content. The clean architecture approach ensures maintainability and scalability as the system grows.
