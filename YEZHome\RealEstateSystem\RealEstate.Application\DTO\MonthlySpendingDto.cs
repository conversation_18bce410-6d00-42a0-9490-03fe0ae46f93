namespace RealEstate.Application.DTO
{
    public class MonthlySpendingDto
    {
        public int Year { get; set; }
        public List<MonthlySpendingDetailDto> Months { get; set; } = new List<MonthlySpendingDetailDto>();
        public decimal TotalYearlySpending { get; set; }
    }

    public class MonthlySpendingDetailDto
    {
        public int Month { get; set; }
        public string Name { get; set; }
        public decimal TotalSpent { get; set; }
        public int TransactionCount { get; set; }
    }
} 