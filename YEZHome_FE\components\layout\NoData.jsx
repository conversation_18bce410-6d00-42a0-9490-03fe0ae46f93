import { memo } from "react";
import { Button } from "@/components/ui/button";
import { Plus } from "lucide-react";
import { Link } from "@/i18n/navigation";
import Image from "next/image";
import { useTranslations } from "next-intl";

function NoData({
  message: messageProp,
  hasCreateButton = false,
  createMessage: createMessageProp,
  createPageRoute = "/user/bds/new",
  createButtonTitle: createButtonTitleProp,
}) {
  const t = useTranslations("NoData");

  const message = messageProp ?? t("defaultMessage");
  const createMessage = createMessageProp ?? t("defaultCreateMessage");
  const createButtonTitle = createButtonTitleProp ?? t("defaultCreateButtonTitle");

  return (
    <div className="flex flex-col items-center justify-center min-h-[400px] p-4">
      <Image
        src="/no_data.png"
        alt="Empty state illustration"
        width={256}
        height={256}
        className="mb-6 opacity-80"
        loading="lazy"
        quality={80}
      />
      <h2 className="text-lg text-gray-600 mb-2">{message}</h2>
      {hasCreateButton && (
        <>
          <p className="text-gray-500 mb-6">{createMessage}</p>
          <Button asChild className="gap-2 bg-coral-500 hover:bg-coral-600">
            <Link href="/user/bds/new">
              <Plus className="h-4 w-4" />
              {createButtonTitle}
            </Link>
          </Button>
        </>
      )}
    </div>
  );
}

// Memoize the component to prevent unnecessary re-renders
export default memo(NoData);
