"use client";
import React, { useState, useEffect } from "react";
import {
  DollarSign,
  BedDouble,
  Bath,
  Square,
  X,
  Home,
  MapPin,
  Filter,
  Compass,
  FileText,
  Car,
  RefreshCw,
  Search,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Checkbox } from "@/components/ui/checkbox";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { useTranslations } from "next-intl";

export default function SearchFilter({ onFilterChange }) {
  const t = useTranslations("SearchFilter");

  const [activeFilters, setActiveFilters] = useState([]);

  // Address search state
  const [addressSearch, setAddressSearch] = useState("");

  // Filter states
  const [transactionType, setTransactionType] = useState("");
  const [propertyTypes, setPropertyTypes] = useState([]);
  const [priceRange, setPriceRange] = useState({ min: "", max: "" });
  const [areaRange, setAreaRange] = useState({ min: "", max: "" });
  const [bedrooms, setBedrooms] = useState("");
  const [bathrooms, setBathrooms] = useState("");
  const [direction, setDirection] = useState("");
  const [legalStatus, setLegalStatus] = useState("");
  const [roadWidth, setRoadWidth] = useState("");

  // --- New Location State ---
  const [provinces, setProvinces] = useState([]); // [{ id: 'hcm', name: 'Hồ Chí Minh' }]
  const [districts, setDistricts] = useState([]); // [{ id: 'q2', name: 'Quận 2', provinceId: 'hcm' }]
  const [selectedProvince, setSelectedProvince] = useState("");
  const [selectedDistrict, setSelectedDistrict] = useState("");
  const [loadingProvinces, setLoadingProvinces] = useState(false);
  const [loadingDistricts, setLoadingDistricts] = useState(false);
  // --- End New Location State ---

  // Popover open states
  const [transactionPopoverOpen, setTransactionPopoverOpen] = useState(false);
  const [propertyTypePopoverOpen, setPropertyTypePopoverOpen] = useState(false);
  const [pricePopoverOpen, setPricePopoverOpen] = useState(false);
  const [advancedPopoverOpen, setAdvancedPopoverOpen] = useState(false);

  // Temporary states for popovers
  const [tempTransactionType, setTempTransactionType] = useState("");
  const [tempPropertyTypes, setTempPropertyTypes] = useState([]);
  const [tempPriceRange, setTempPriceRange] = useState({ min: "", max: "" });
  const [tempAreaRange, setTempAreaRange] = useState({ min: "", max: "" });
  const [tempBedrooms, setTempBedrooms] = useState("");
  const [tempBathrooms, setTempBathrooms] = useState("");
  const [tempDirection, setTempDirection] = useState("");
  const [tempLegalStatus, setTempLegalStatus] = useState("");
  const [tempRoadWidth, setTempRoadWidth] = useState("");

  // Property type options
  const propertyTypeOptions = [
    { id: "nha_rieng", label: t("propertyTypeHouse") },
    { id: "can_ho", label: t("propertyTypeApartment") },
    { id: "biet_thu", label: t("propertyTypeVilla") },
  ];

  // Initialize temp values when popovers open
  useEffect(() => {
    if (transactionPopoverOpen) setTempTransactionType(transactionType);
  }, [transactionPopoverOpen, transactionType]);

  useEffect(() => {
    if (propertyTypePopoverOpen) setTempPropertyTypes([...propertyTypes]);
  }, [propertyTypePopoverOpen, propertyTypes]);

  useEffect(() => {
    if (pricePopoverOpen) setTempPriceRange({ ...priceRange });
  }, [pricePopoverOpen, priceRange]);

  useEffect(() => {
    if (advancedPopoverOpen) {
      setTempAreaRange({ ...areaRange });
      setTempBedrooms(bedrooms);
      setTempBathrooms(bathrooms);
      setTempDirection(direction);
      setTempLegalStatus(legalStatus);
      setTempRoadWidth(roadWidth);
    }
  }, [advancedPopoverOpen, areaRange, bedrooms, bathrooms, direction, legalStatus, roadWidth]); 

  // Update active filters display
  useEffect(() => {
    const newActiveFilters = [];

    if (transactionType) {
      const typeLabels = {
        sell: t("transactionTypeButtonSell"),
        rent: t("transactionTypeButtonRent"),
      };
      newActiveFilters.push(`${t("filterLabelTransactionType")}: ${typeLabels[transactionType]}`);
    }

    if (propertyTypes.length > 0) {
      const typeLabels = propertyTypeOptions.reduce((acc, option) => {
        acc[option.id] = option.label;
        return acc;
      }, {});
      newActiveFilters.push(
        `${t("filterLabelPropertyType")}: ${propertyTypes.map((pt) => typeLabels[pt]).join(", ")}`
      );
    }

    if (priceRange.min || priceRange.max) {
      let minPriceDisplay = "";
      if (priceRange.min) {
        const minValue = parseFloat(priceRange.min);
        if (minValue >= 1000) {
          minPriceDisplay = (minValue / 1000).toLocaleString("vi-VN") + t("priceUnitBillion");
        } else {
          minPriceDisplay = minValue.toLocaleString("vi-VN") + t("priceUnitMillion");
        }
      } else {
        minPriceDisplay = "0";
      }

      let maxPriceDisplay = "";
      if (priceRange.max) {
        const maxValue = parseFloat(priceRange.max);
        if (maxValue >= 1000) {
          maxPriceDisplay = (maxValue / 1000).toLocaleString("vi-VN") + t("priceUnitBillion");
        } else {
          maxPriceDisplay = maxValue.toLocaleString("vi-VN") + t("priceUnitMillion");
        }
      } else {
        maxPriceDisplay = t("unlimited");
      }
      newActiveFilters.push(`${t("filterLabelPrice")}: ${minPriceDisplay} - ${maxPriceDisplay}`);
    }

    if (areaRange.min || areaRange.max) {
      newActiveFilters.push(
        `${t("filterLabelArea")}: ${areaRange.min || 0} - ${areaRange.max || t("unlimited")} m²`
      );
    }

    if (bedrooms) {
      newActiveFilters.push(`${t("filterLabelBedrooms")}: ${bedrooms}+`);
    }

    if (bathrooms) {
      newActiveFilters.push(`${t("filterLabelBathrooms")}: ${bathrooms}+`);
    }

    if (direction) {
      const directionLabels = {
        east: t("directionEast"),
        west: t("directionWest"),
        south: t("directionSouth"),
        north: t("directionNorth"),
        southeast: t("directionSouthEast"),
        southwest: t("directionSouthWest"),
        northeast: t("directionNorthEast"),
        northwest: t("directionNorthWest"),
      };
      newActiveFilters.push(`${t("filterLabelDirection")}: ${directionLabels[direction]}`);
    }

    if (legalStatus) {
      const legalLabels = {
        red_book: t("legalStatusRedBook"),
        pink_book: t("legalStatusPinkBook"),
        handwritten: t("legalStatusHandwritten"),
        other: t("legalStatusOther"),
      };
      newActiveFilters.push(`${t("filterLabelLegalStatus")}: ${legalLabels[legalStatus]}`);
    }

    if (roadWidth) {
      const widthLabels = {
        3: t("roadWidthOption1"),
        5: t("roadWidthOption2"),
        7: t("roadWidthOption3"),
        8: t("roadWidthOption4"),
      };
      newActiveFilters.push(`${t("filterLabelRoadWidth")}: ${widthLabels[roadWidth]}`);
    }

    if (addressSearch) {
      newActiveFilters.push(`${t("filterLabelAddress")}: ${addressSearch}`);
    }

    setActiveFilters(newActiveFilters);
    handleSearch();

  }, [
    transactionType,
    propertyTypes,
    priceRange,
    areaRange,
    bedrooms,
    bathrooms,
    direction,
    legalStatus,
    roadWidth,
    addressSearch,
  ]);

  // Apply filters
  const handleSearch = () => {
    const filterCriteria = {
      transactionType: transactionType ? [transactionType] : [],
      propertyType: propertyTypes,
      location: {
        address: addressSearch,
        province: "",
        district: "",
      },
      priceRange,
      areaRange,
      bedrooms,
      bathrooms,
      direction,
      legalStatus,
      roadWidth,
    };

    onFilterChange(filterCriteria);
  };

  // Reset all filters
  const resetAllFilters = () => {
    setTransactionType("");
    setPropertyTypes([]);
    setPriceRange({ min: "", max: "" });
    setAreaRange({ min: "", max: "" });
    setBedrooms("");
    setBathrooms("");
    setDirection("");
    setLegalStatus("");
    setRoadWidth("");
    setAddressSearch("");

    // Update filters after reset
    setTimeout(() => {
      onFilterChange({
        transactionType: [],
        propertyType: [],
        location: { address: "", province: "", district: "" },
        priceRange: { min: "", max: "" },
        areaRange: { min: "", max: "" },
        bedrooms: "",
        bathrooms: "",
        direction: "",
        legalStatus: "",
        roadWidth: "",
      });
    }, 0);
  };

  // Apply transaction type filter
  const applyTransactionType = () => {
    setTransactionType(tempTransactionType);
    setTransactionPopoverOpen(false);
  };

  // Reset transaction type filter
  const resetTransactionType = () => {
    setTransactionType("");
    setTransactionPopoverOpen(false);
  };

  // Apply property type filter
  const applyPropertyType = () => {
    setPropertyTypes([...tempPropertyTypes]);
    setPropertyTypePopoverOpen(false);
  };

  // Reset property type filter
  const resetPropertyType = () => {
    setPropertyTypes([]);
    setPropertyTypePopoverOpen(false);
  };

  // Apply price range filter
  const applyPriceRange = () => {
    setPriceRange({ ...tempPriceRange });
    setPricePopoverOpen(false);
  };

  // Format price for display
  const formatPriceDisplay = (price) => {
    if (!price) return "";

    const value = parseFloat(price);
    if (value >= 1000) {
      return (value / 1000).toLocaleString("vi-VN") + t("priceUnitBillion");
    } else {
      return value.toLocaleString("vi-VN") + t("priceUnitMillion");
    }
  };

  // Reset price range filter
  const resetPriceRange = () => {
    setPriceRange({ min: "", max: "" });
    setPricePopoverOpen(false);
  };

  // Apply advanced filters
  const applyAdvancedFilters = () => {
    setAreaRange({ ...tempAreaRange });
    setBedrooms(tempBedrooms);
    setBathrooms(tempBathrooms);
    setDirection(tempDirection);
    setLegalStatus(tempLegalStatus);
    setRoadWidth(tempRoadWidth);
    setAdvancedPopoverOpen(false);
  };

  // Reset advanced filters
  const resetAdvancedFilters = () => {
    setAreaRange({ min: "", max: "" });
    setBedrooms("");
    setBathrooms("");
    setDirection("");
    setLegalStatus("");
    setRoadWidth("");
    setAdvancedPopoverOpen(false);
  };

  // Toggle property type selection
  const togglePropertyType = (id) => {
    if (tempPropertyTypes.includes(id)) {
      setTempPropertyTypes(tempPropertyTypes.filter((type) => type !== id));
    } else {
      setTempPropertyTypes([...tempPropertyTypes, id]);
    }
  };

  // Remove a specific filter
  const removeFilter = (filter) => {
    const [filterLabel, _] = filter.split(":");

    switch (filterLabel.trim()) {
      case t("filterLabelTransactionType"):
        setTransactionType("");
        break;
      case t("filterLabelPropertyType"):
        setPropertyTypes([]);
        break;
      case t("filterLabelPrice"):
        setPriceRange({ min: "", max: "" });
        break;
      case t("filterLabelArea"):
        setAreaRange({ min: "", max: "" });
        break;
      case t("filterLabelBedrooms"):
        setBedrooms("");
        break;
      case t("filterLabelBathrooms"):
        setBathrooms("");
        break;
      case t("filterLabelDirection"):
        setDirection("");
        break;
      case t("filterLabelLegalStatus"):
        setLegalStatus("");
        break;
      case t("filterLabelRoadWidth"):
        setRoadWidth("");
        break;
      case t("filterLabelAddress"):
        setAddressSearch("");
        break;
      case t("filterLabelLocation"):
        setSelectedProvince("");
        setSelectedDistrict("");
        break;
      default:
        break;
    }

    // Update filters after removing one
    setTimeout(handleSearch, 0);
  };

  return (
    <section className="bg-white border-b border-b-stone-300 shadow-sm">
      <div className="mx-auto px-4 flex flex-col">
        {/* Address Search */}
        <div className="w-full mx-auto p-4">
          <div className="flex items-center w-full border border-gray-300 rounded-md overflow-hidden bg-gray-50 p-2">
            <div className="flex items-center flex-1">
              <div className="pl-3 pr-2 text-gray-500">
                <MapPin size={25} className="text-coral-600" />
              </div>
              <div className="relative flex-1 gap-2">
                <Input
                  type="text"
                  placeholder={t("addressSearchPlaceholder")}
                  value={addressSearch}
                  onChange={(e) => setAddressSearch(e.target.value)}
                  className="w-full py-5 px-3 outline-none text-sm mr-2 bg-white"
                />
                {/* Province Select */}
                {/* <Select
                  value={selectedProvince}
                  onValueChange={(value) => {
                    setSelectedProvince(value);
                  }}
                  disabled={loadingProvinces}
                >
                  <SelectTrigger className="w-full md:w-[180px] bg-white">
                    <SelectValue placeholder={loadingProvinces ? t('loadingProvinces') : t('provincePlaceholder')} />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">{t('provincePlaceholderAll')}</SelectItem>
                    {provinces.map((province) => (
                      <SelectItem key={province.id} value={province.id}>
                        {province.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select> */}

                {/* District Select */}
                {/* <Select
                  value={selectedDistrict}
                  onValueChange={setSelectedDistrict}
                  disabled={!selectedProvince || loadingDistricts || districts.length === 0}
                >
                  <SelectTrigger className="w-full md:w-[180px] bg-white">
                    <SelectValue placeholder={loadingDistricts ? t('loadingDistricts') : t('districtPlaceholder')} />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">{t('districtPlaceholderAll')}</SelectItem>
                    {districts.map((district) => (
                      <SelectItem key={district.id} value={district.id}>
                        {district.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select> */}
              </div>
            </div>
            <Button
              className="px-4 py-3 h-full bg-teal-500 hover:bg-teal-600 text-white"
              onClick={handleSearch}
            >
              <Search className="mr-2 h-4 w-4" />
              {t("searchButton")}
            </Button>
          </div>
        </div>

        {/* Filter Popovers */}
        <div className="flex flex-wrap items-center gap-2 mb-4 px-4">
          {/* Transaction Type Popover */}
          <Popover open={transactionPopoverOpen} onOpenChange={setTransactionPopoverOpen}>
            <PopoverTrigger asChild>
              <Button variant="outline" className="border-gray-300 bg-white">
                <Home className="mr-1 h-4 w-4" />
                {transactionType
                  ? transactionType === "sell"
                    ? t("transactionTypeButtonSell")
                    : t("transactionTypeButtonRent")
                  : t("transactionTypeButtonDefault")}
                {transactionType && (
                  <Badge rounded="full" className="ml-1 bg-teal-100 text-teal-800 hover:bg-teal-100">1</Badge>
                )}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-72">
              <div className="space-y-4">
                <h3 className="font-medium text-sm">{t("popoverTitleTransactionType")}</h3>
                <RadioGroup
                  value={tempTransactionType}
                  onValueChange={setTempTransactionType}
                  className="flex flex-col space-y-2"
                >
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="sell" id="sell" />
                    <Label htmlFor="sell">{t("transactionSellLabel")}</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="rent" id="rent" />
                    <Label htmlFor="rent">{t("transactionRentLabel")}</Label>
                  </div>
                </RadioGroup>
                <div className="flex justify-between pt-2 border-t">
                  <Button variant="outline" size="sm" onClick={resetTransactionType}>
                    {t("resetButton")}
                  </Button>
                  <Button
                    size="sm"
                    onClick={applyTransactionType}
                    className="bg-teal-500 hover:bg-teal-600 text-white"
                  >
                    {t("applyButton")}
                  </Button>
                </div>
              </div>
            </PopoverContent>
          </Popover>

          {/* Property Type Popover */}
          <Popover open={propertyTypePopoverOpen} onOpenChange={setPropertyTypePopoverOpen}>
            <PopoverTrigger asChild>
              <Button variant="outline" className="border-gray-300 bg-white">
                <Home className="mr-1 h-4 w-4" />
                {t("propertyTypeButton")}
                {propertyTypes.length > 0 && (
                  <Badge rounded="full" className="ml-1 bg-teal-100 text-teal-800 hover:bg-teal-100">
                    {propertyTypes.length}
                  </Badge>
                )}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-72">
              <div className="space-y-4">
                <h3 className="font-medium text-sm">{t("popoverTitlePropertyType")}</h3>
                <div className="flex flex-col space-y-2">
                  {propertyTypeOptions.map((option) => (
                    <div key={option.id} className="flex items-center space-x-2">
                      <Checkbox
                        id={option.id}
                        checked={tempPropertyTypes.includes(option.id)}
                        onCheckedChange={() => togglePropertyType(option.id)}
                      />
                      <Label htmlFor={option.id}>{option.label}</Label>
                    </div>
                  ))}
                </div>
                <div className="flex justify-between pt-2 border-t">
                  <Button variant="outline" size="sm" onClick={resetPropertyType}>
                    {t("resetButton")}
                  </Button>
                  <Button
                    size="sm"
                    onClick={applyPropertyType}
                    className="bg-teal-500 hover:bg-teal-600 text-white"
                  >
                    {t("applyButton")}
                  </Button>
                </div>
              </div>
            </PopoverContent>
          </Popover>

          {/* Price Range Popover */}
          <Popover open={pricePopoverOpen} onOpenChange={setPricePopoverOpen}>
            <PopoverTrigger asChild>
              <Button variant="outline" className="border-gray-300 bg-white">
                <DollarSign className="mr-1 h-4 w-4" />
                {t("priceButton")}
                {(priceRange.min || priceRange.max) && (
                  <Badge rounded="full" className="ml-1 bg-teal-100 text-teal-800 hover:bg-teal-100">1</Badge>
                )}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-80">
              <div className="space-y-4">
                <h3 className="font-medium text-sm">{t("popoverTitlePrice")}</h3>
                <div className="flex items-center gap-2">
                  <Input
                    type="number"
                    placeholder={t("pricePlaceholderFrom")}
                    className="w-full"
                    value={tempPriceRange.min}
                    onChange={(e) => setTempPriceRange({ ...tempPriceRange, min: e.target.value })}
                  />
                  <span>-</span>
                  <Input
                    type="number"
                    placeholder={t("pricePlaceholderTo")}
                    className="w-full"
                    value={tempPriceRange.max}
                    onChange={(e) => setTempPriceRange({ ...tempPriceRange, max: e.target.value })}
                  />
                </div>
                <div className="text-xs text-gray-500">
                  {tempPriceRange.min &&
                    `${t("priceDisplayFrom")}: ${formatPriceDisplay(tempPriceRange.min)}`}
                  {tempPriceRange.min && tempPriceRange.max && " - "}
                  {tempPriceRange.max &&
                    `${t("priceDisplayTo")}: ${formatPriceDisplay(tempPriceRange.max)}`}
                </div>
                <div className="flex justify-between pt-2 border-t">
                  <Button variant="outline" size="sm" onClick={resetPriceRange}>
                    {t("resetButton")}
                  </Button>
                  <Button
                    size="sm"
                    onClick={applyPriceRange}
                    className="bg-teal-500 hover:bg-teal-600 text-white"
                  >
                    {t("applyButton")}
                  </Button>
                </div>
              </div>
            </PopoverContent>
          </Popover>

          {/* Advanced Filters Popover */}
          <Popover open={advancedPopoverOpen} onOpenChange={setAdvancedPopoverOpen}>
            <PopoverTrigger asChild>
              <Button variant="outline" className="border-gray-300 bg-white">
                <Filter className="mr-2 h-4 w-4" />
                {t("advancedFilterButton")}
                {(areaRange.min ||
                  areaRange.max ||
                  bedrooms ||
                  bathrooms ||
                  direction ||
                  legalStatus ||
                  roadWidth) && (
                  <Badge rounded="full" className="ml-2 bg-teal-100 text-teal-800 hover:bg-teal-100">
                    {[
                      areaRange.min || areaRange.max ? 1 : 0,
                      bedrooms ? 1 : 0,
                      bathrooms ? 1 : 0,
                      direction ? 1 : 0,
                      legalStatus ? 1 : 0,
                      roadWidth ? 1 : 0,
                    ].reduce((a, b) => a + b, 0)}
                  </Badge>
                )}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-96">
              <div className="space-y-4">
                <h3 className="font-medium">{t("popoverTitleAdvanced")}</h3>

                {/* Area Range */}
                <div className="space-y-2">
                  <Label className="flex items-center gap-1">
                    <Square className="h-4 w-4 text-gray-500" />
                    {t("areaLabel")}
                  </Label>
                  <div className="flex items-center gap-2">
                    <Input
                      type="number"
                      placeholder={t("pricePlaceholderFrom")}
                      className="w-full"
                      value={tempAreaRange.min}
                      onChange={(e) => setTempAreaRange({ ...tempAreaRange, min: e.target.value })}
                    />
                    <span>-</span>
                    <Input
                      type="number"
                      placeholder={t("pricePlaceholderTo")}
                      className="w-full"
                      value={tempAreaRange.max}
                      onChange={(e) => setTempAreaRange({ ...tempAreaRange, max: e.target.value })}
                    />
                  </div>
                </div>

                <Separator />

                {/* Bedrooms */}
                <div className="space-y-2">
                  <Label className="flex items-center gap-1">
                    <BedDouble className="h-4 w-4 text-gray-500" />
                    {t("bedroomsLabel")}
                  </Label>
                  <Select value={tempBedrooms} onValueChange={setTempBedrooms}>
                    <SelectTrigger>
                      <SelectValue placeholder={t("bedroomsPlaceholder")} />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="1">{t("bedroomsOption1")}</SelectItem>
                      <SelectItem value="2">{t("bedroomsOption2")}</SelectItem>
                      <SelectItem value="3">{t("bedroomsOption3")}</SelectItem>
                      <SelectItem value="4">{t("bedroomsOption4")}</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* Bathrooms */}
                <div className="space-y-2">
                  <Label className="flex items-center gap-1">
                    <Bath className="h-4 w-4 text-gray-500" />
                    {t("bathroomsLabel")}
                  </Label>
                  <Select value={tempBathrooms} onValueChange={setTempBathrooms}>
                    <SelectTrigger>
                      <SelectValue placeholder={t("bathroomsPlaceholder")} />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="1">{t("bathroomsOption1")}</SelectItem>
                      <SelectItem value="2">{t("bathroomsOption2")}</SelectItem>
                      <SelectItem value="3">{t("bathroomsOption3")}</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* Direction */}
                <div className="space-y-2">
                  <Label className="flex items-center gap-1">
                    <Compass className="h-4 w-4 text-gray-500" />
                    {t("directionLabel")}
                  </Label>
                  <Select value={tempDirection} onValueChange={setTempDirection}>
                    <SelectTrigger>
                      <SelectValue placeholder={t("directionPlaceholder")} />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="east">{t("directionEast")}</SelectItem>
                      <SelectItem value="west">{t("directionWest")}</SelectItem>
                      <SelectItem value="south">{t("directionSouth")}</SelectItem>
                      <SelectItem value="north">{t("directionNorth")}</SelectItem>
                      <SelectItem value="southeast">{t("directionSouthEast")}</SelectItem>
                      <SelectItem value="southwest">{t("directionSouthWest")}</SelectItem>
                      <SelectItem value="northeast">{t("directionNorthEast")}</SelectItem>
                      <SelectItem value="northwest">{t("directionNorthWest")}</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* Legal Status */}
                <div className="space-y-2">
                  <Label className="flex items-center gap-1">
                    <FileText className="h-4 w-4 text-gray-500" />
                    {t("legalStatusLabel")}
                  </Label>
                  <Select value={tempLegalStatus} onValueChange={setTempLegalStatus}>
                    <SelectTrigger>
                      <SelectValue placeholder={t("legalStatusPlaceholder")} />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="red_book">{t("legalStatusRedBook")}</SelectItem>
                      <SelectItem value="pink_book">{t("legalStatusPinkBook")}</SelectItem>
                      <SelectItem value="handwritten">{t("legalStatusHandwritten")}</SelectItem>
                      <SelectItem value="other">{t("legalStatusOther")}</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* Road Width */}
                <div className="space-y-2">
                  <Label className="flex items-center gap-1">
                    <Car className="h-4 w-4 text-gray-500" />
                    {t("roadWidthLabel")}
                  </Label>
                  <Select value={tempRoadWidth} onValueChange={setTempRoadWidth}>
                    <SelectTrigger>
                      <SelectValue placeholder={t("roadWidthPlaceholder")} />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="3">{t("roadWidthOption1")}</SelectItem>
                      <SelectItem value="5">{t("roadWidthOption2")}</SelectItem>
                      <SelectItem value="7">{t("roadWidthOption3")}</SelectItem>
                      <SelectItem value="8">{t("roadWidthOption4")}</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="flex justify-between pt-2 border-t">
                  <Button variant="outline" size="sm" onClick={resetAdvancedFilters}>
                    {t("resetButton")}
                  </Button>
                  <Button
                    size="sm"
                    onClick={applyAdvancedFilters}
                    className="bg-teal-500 hover:bg-teal-600 text-white"
                  >
                    {t("applyButton")}
                  </Button>
                </div>
              </div>
            </PopoverContent>
          </Popover>

          {/* Active Filters */}
          {activeFilters.length > 0 && (
            <>
              <Separator orientation="vertical" className="h-6 mx-2" />
              <div className="flex flex-wrap items-center gap-2">
                <span className="text-sm font-medium text-gray-700">{t("activeFiltersLabel")}</span>
                {activeFilters.map((filter) => (
                  <span
                    key={filter}
                    className="bg-[#F5F5F5] text-gray-700 px-3 py-1 rounded-full text-sm flex items-center"
                  >
                    {filter}
                    <button
                      onClick={() => removeFilter(filter)}
                      className="ml-2 text-gray-500 hover:text-gray-700"
                    >
                      <X size={14} />
                    </button>
                  </span>
                ))}
              </div>
            </>
          )}

          {/* Reset All Filters */}
          {activeFilters.length > 0 && (
            <Button
              variant="link"
              className="text-red-600 hover:text-red-800 p-0 h-auto ml-4"
              onClick={resetAllFilters}
            >
              <RefreshCw className="mr-1 h-4 w-4" />
              {t("resetAllButton")}
            </Button>
          )}
        </div>
      </div>
    </section>
  );
}
