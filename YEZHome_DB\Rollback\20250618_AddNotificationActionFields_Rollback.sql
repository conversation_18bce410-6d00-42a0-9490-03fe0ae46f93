-- Rollback Migration: Remove action fields from Notification table
-- Date: 2025-01-19
-- Description: Rollback script to remove RelatedEntityId, RelatedPropertyId, and ActionUrl fields

-- Drop foreign key constraint if it was added
-- ALTER TABLE "Notification" DROP CONSTRAINT IF EXISTS "FK_Notification_Property_RelatedPropertyId";

-- Drop indexes
DROP INDEX IF EXISTS "IX_Notification_RelatedEntityId";
DROP INDEX IF EXISTS "IX_Notification_RelatedPropertyId";

-- Remove columns from Notification table
ALTER TABLE "Notification" 
DROP COLUMN IF EXISTS "RelatedEntityId",
DROP COLUMN IF EXISTS "RelatedPropertyId",
DROP COLUMN IF EXISTS "ActionUrl";

COMMIT;
