using AutoMapper;
using RealEstate.Application.DTO;
using RealEstate.Application.Interfaces;
using RealEstate.Domain.Entities;
using RealEstate.Domain.Interfaces;
using System;
using System.Threading.Tasks;

namespace RealEstate.Application.Services
{
    public class UserAvatarService : IUserAvatarService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly IMapper _mapper;

        public UserAvatarService(IUnitOfWork unitOfWork, IMapper mapper)
        {
            _unitOfWork = unitOfWork;
            _mapper = mapper;
        }

        public async Task<UserAvatarDto> GetUserAvatarByIdAsync(Guid id)
        {
            var userAvatar = await _unitOfWork.UserAvatars.GetByIdAsync(id);
            return _mapper.Map<UserAvatarDto>(userAvatar);
        }

        public async Task<UserAvatarDto> GetUserAvatarByUserIdAsync(Guid userId)
        {
            var userAvatar = await _unitOfWork.UserAvatars.FindAsync(ua => ua.UserID == userId);
            var avatarList = _mapper.Map<IEnumerable<UserAvatarDto>>(userAvatar);
            return avatarList.FirstOrDefault();
        }

        public async Task<UserAvatarDto> CreateUserAvatarAsync(CreateUserAvatarDto userAvatarDto)
        {
            // Check if user already has an avatar and delete it
            var existingAvatars = await _unitOfWork.UserAvatars.FindAsync(ua => ua.UserID == userAvatarDto.UserID);
            foreach (var existingAvatar in existingAvatars)
            {
                _unitOfWork.UserAvatars.Remove(existingAvatar);
            }
            
            var userAvatar = _mapper.Map<UserAvatar>(userAvatarDto);
            await _unitOfWork.UserAvatars.AddAsync(userAvatar);
            await _unitOfWork.SaveChangesAsync();
            
            // Update the user's AvatarImage field
            var user = await _unitOfWork.AppUsers.GetByIdAsync(userAvatarDto.UserID);
            if (user != null)
            {
                user.AvatarImage = userAvatar.Id.ToString();
                _unitOfWork.AppUsers.Update(user);
                await _unitOfWork.SaveChangesAsync();
            }
            
            return _mapper.Map<UserAvatarDto>(userAvatar);
        }

        public async Task<UserAvatarDto> UpdateUserAvatarAsync(Guid id, CreateUserAvatarDto userAvatarDto)
        {
            var userAvatar = await _unitOfWork.UserAvatars.GetByIdAsync(id);
            if (userAvatar == null) throw new FileNotFoundException();

            _mapper.Map(userAvatarDto, userAvatar);

            _unitOfWork.UserAvatars.Update(userAvatar);
            await _unitOfWork.SaveChangesAsync();
            return _mapper.Map<UserAvatarDto>(userAvatar);
        }

        public async Task<bool> DeleteUserAvatarAsync(Guid id)
        {
            var userAvatar = await _unitOfWork.UserAvatars.GetByIdAsync(id);
            if (userAvatar == null) return false;

            // Update the user's AvatarImage field to null
            var user = await _unitOfWork.AppUsers.GetByIdAsync(userAvatar.UserID);
            if (user != null)
            {
                user.AvatarImage = null;
                _unitOfWork.AppUsers.Update(user);
            }

            _unitOfWork.UserAvatars.Remove(userAvatar);
            await _unitOfWork.SaveChangesAsync();
            return true;
        }
    }
}
