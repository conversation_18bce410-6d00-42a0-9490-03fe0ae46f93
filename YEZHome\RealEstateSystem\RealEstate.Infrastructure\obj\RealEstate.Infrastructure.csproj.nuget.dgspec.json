{"format": 1, "restore": {"D:\\Project\\YEZ_Tech\\YEZ_Home\\YEZHome\\RealEstateSystem\\RealEstate.Infrastructure\\RealEstate.Infrastructure.csproj": {}}, "projects": {"D:\\Project\\YEZ_Tech\\YEZ_Home\\YEZHome\\RealEstateSystem\\RealEstate.Domain\\RealEstate.Domain.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Project\\YEZ_Tech\\YEZ_Home\\YEZHome\\RealEstateSystem\\RealEstate.Domain\\RealEstate.Domain.csproj", "projectName": "RealEstate.Domain", "projectPath": "D:\\Project\\YEZ_Tech\\YEZ_Home\\YEZHome\\RealEstateSystem\\RealEstate.Domain\\RealEstate.Domain.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Project\\YEZ_Tech\\YEZ_Home\\YEZHome\\RealEstateSystem\\RealEstate.Domain\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}, "D:\\Project\\YEZ_Tech\\YEZ_Home\\YEZHome\\RealEstateSystem\\RealEstate.Infrastructure\\RealEstate.Infrastructure.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Project\\YEZ_Tech\\YEZ_Home\\YEZHome\\RealEstateSystem\\RealEstate.Infrastructure\\RealEstate.Infrastructure.csproj", "projectName": "RealEstate.Infrastructure", "projectPath": "D:\\Project\\YEZ_Tech\\YEZ_Home\\YEZHome\\RealEstateSystem\\RealEstate.Infrastructure\\RealEstate.Infrastructure.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Project\\YEZ_Tech\\YEZ_Home\\YEZHome\\RealEstateSystem\\RealEstate.Infrastructure\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"D:\\Project\\YEZ_Tech\\YEZ_Home\\YEZHome\\RealEstateSystem\\RealEstate.Domain\\RealEstate.Domain.csproj": {"projectPath": "D:\\Project\\YEZ_Tech\\YEZ_Home\\YEZHome\\RealEstateSystem\\RealEstate.Domain\\RealEstate.Domain.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Npgsql.EntityFrameworkCore.PostgreSQL": {"target": "Package", "version": "[9.0.2, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}}}