Real Estate Management System - User Dashboard APIs
This document provides details of all API endpoints available for the user analytics dashboard.
Authentication Requirements
All endpoints require authentication via JWT token. Include the token in the Authorization header:
Authorization: Bearer {your_jwt_token}

API Endpoints
1. Get Complete User Dashboard
Endpoint: GET /api/user/dashboard
Description: Retrieves all dashboard data for the authenticated user in a single call.
Response Format:
{
  "userInfo": {
    "id": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
    "fullName": "<PERSON> Doe",
    "email": "<EMAIL>",
    "phone": "0123456789",
    "userType": "Owner",
    "memberRank": "Gold",
    "lastLogin": "2023-08-15T14:30:45.123Z",
    "createdAt": "2023-01-10T08:15:30.000Z",
    "roles": ["User", "Owner"]
  },
  "walletInfo": {
    "balance": 1500.50,
    "totalSpent": 3200.75,
    "totalTransactions": 42,
    "lastMonthSpending": 800.25
  },
  "propertyStats": {
    "totalProperties": 15,
    "activeProperties": 8,
    "expiredProperties": 3,
    "draftProperties": 4,
    "favoriteProperties": 7,
    "totalViews": 1250,
    "averageRating": 4.2
  },
  "recentTransactions": [
    {
      "id": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
      "amount": 55000.00,
      "type": "spend",
      "description": "Property listing #12345",
      "createdAt": "2023-08-10T12:30:45.123Z"
    },
    {
      "id": "4fa85f64-5717-4562-b3fc-2c963f66afa7",
      "amount": 100000.00,
      "type": "deposit",
      "description": "Account funding",
      "createdAt": "2023-08-01T10:15:20.567Z"
    }
  ],
  "memberRanking": {
    "currentRank": "Gold",
    "nextRank": "Platinum",
    "spendingToNextRank": 1800.25,
    "minSpent": 3000.00,
    "maxSpent": 5000.00,
    "progressPercentage": 65.5
  }
}

2. Get User Dashboard By ID (Admin Only)
Endpoint: GET /api/user/dashboard/{userId}
Description: Retrieves dashboard data for a specific user (admin access required).
Authorization: Requires "AdminOnly" policy
Response Format: Same as /api/user/dashboard

3. Get User Wallet Information
Endpoint: GET /api/user/wallet
Description: Retrieves wallet information for the authenticated user.
Response Format:
{
  "balance": 1500.50,
  "totalSpent": 3200.75,
  "totalTransactions": 42,
  "lastMonthSpending": 800.25
}

4. Get User Property Statistics
Endpoint: GET /api/user/properties/stats
Description: Retrieves property-related statistics for the authenticated user.
Response Format:
{
  "totalProperties": 15,
  "activeProperties": 8,
  "expiredProperties": 3,
  "draftProperties": 4,
  "favoriteProperties": 7,
  "totalViews": 1250,
  "averageRating": 4.2
}

5. Get User Transactions
Endpoint: GET /api/user/transactions
Description: Retrieves recent wallet transactions for the authenticated user.
Query Parameters:
count (optional): Number of recent transactions to retrieve (default: 10)
Example: GET /api/user/transactions?count=10
Response Format:
[
  {
    "id": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
    "amount": 55000.00,
    "type": "spend",
    "description": "Property listing #12345",
    "createdAt": "2023-08-10T12:30:45.123Z"
  },
  {
    "id": "4fa85f64-5717-4562-b3fc-2c963f66afa7",
    "amount": 100000.00,
    "type": "deposit",
    "description": "Account funding",
    "createdAt": "2023-08-01T10:15:20.567Z"
  },
  {
    "id": "5fa85f64-5717-4562-b3fc-2c963f66afa8",
    "amount": 25000.00,
    "type": "spend",
    "description": "Highlight property #54321",
    "createdAt": "2023-07-25T09:45:10.890Z"
  }
]

6. Get User Ranking Information
Endpoint: GET /api/user/ranking
Description: Retrieves membership ranking information for the authenticated user.
Response Format:
{
  "currentRank": "Gold",
  "nextRank": "Platinum",
  "spendingToNextRank": 1800.25,
  "minSpent": 3000.00,
  "maxSpent": 5000.00,
  "progressPercentage": 65.5
}

7. Get Monthly Spending (Optional)
Endpoint: GET /api/user/spending/monthly
Description: Retrieves monthly spending data for a given year.
Query Parameters:
year (required): Year for which to retrieve monthly spending data
Example: GET /api/user/spending/monthly?year=2023
Response Format:
{
  "year": 2023,
  "months": [
    {
      "month": 1,
      "name": "January",
      "totalSpent": 350.75,
      "transactionCount": 3
    },
    {
      "month": 2,
      "name": "February",
      "totalSpent": 425.00,
      "transactionCount": 4
    },
    "...",
    {
      "month": 8,
      "name": "August",
      "totalSpent": 800.25,
      "transactionCount": 5
    }
  ],
  "totalYearlySpending": 3200.75
}

8. Get Property Performance (Optional)
Endpoint: GET /api/user/properties/performance
Description: Retrieves performance metrics for the user's properties.
Response Format:
{
  "totalProperties": 15,
  "propertiesPerformance": [
    {
      "propertyId": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
      "propertyName": "Luxury apartment in District 1",
      "views": 320,
      "favorites": 18,
      "contactRequests": 12,
      "status": "Approved",
      "expiresAt": "2023-10-15T00:00:00.000Z",
      "rating": 4.8,
      "reviewCount": 5
    },
    {
      "propertyId": "4fa85f64-5717-4562-b3fc-2c963f66afa7",
      "propertyName": "Villa with garden",
      "views": 180,
      "favorites": 9,
      "contactRequests": 6,
      "status": "Approved",
      "expiresAt": "2023-09-30T00:00:00.000Z",
      "rating": 4.2,
      "reviewCount": 3
    }
  ],
  "bestPerforming": {
    "propertyId": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
    "propertyName": "Luxury apartment in District 1"
  },
  "needsAttention": [
    {
      "propertyId": "5fa85f64-5717-4562-b3fc-2c963f66afa8",
      "propertyName": "Studio apartment",
      "issue": "Expires soon",
      "expiresAt": "2023-08-25T00:00:00.000Z"
    }
  ]
}

Error Responses
The API may return the following error responses:
401 Unauthorized
If the user is not authenticated:
{
  "message": "User not authenticated"
}


403 Forbidden
If the user doesn't have permission to access the resource:
{
  "message": "User with ID 3fa85f64-5717-4562-b3fc-2c963f66afa6 not found"
}

500 Internal Server Error
If an unexpected error occurs:
{
  "message": "An error occurred: [error details]"
}

###Implementation Details
These APIs are implemented in the UserController.cs file using the IUserDashboardService service interface. The service queries the database for user information, wallet data, property statistics, and other analytics.
Database Entities
The API relies on the following database entities:
AppUser - User information and statistics
Wallet - User wallet balance
WalletTransaction - History of wallet deposits and spending
MemberRanking - Membership rank thresholds and benefits
Property - Real estate properties
UserFavorite - User's favorite properties


###DTOs
The API uses the following DTOs to transfer data:
UserDashboardDto - Complete dashboard data
UserInfoDto - User profile information
WalletInfoDto - Wallet balance and spending
PropertyStatsDto - Property statistics
WalletTransactionDto - Transaction details
MemberRankingDto - Membership rank information