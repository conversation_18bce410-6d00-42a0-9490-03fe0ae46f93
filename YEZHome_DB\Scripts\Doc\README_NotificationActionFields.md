# Notification Action Fields Migration

## Overview
This migration adds actionable fields to the Notification table to support creating clickable notifications that can navigate users to specific pages or actions.

## Changes Made

### Database Schema Changes
- **RelatedEntityId** (UUID, nullable): Stores the ID of the related entity (contact request, transaction, etc.)
- **RelatedPropertyId** (UUID, nullable): Stores the property ID for property-related notifications
- **ActionUrl** (VARCHAR(500), nullable): Stores the URL for direct navigation when notification is clicked

### Application Changes
1. **Domain Entity**: Updated `Notification.cs` with new fields
2. **DTOs**: Updated `CreateNotificationDto.cs` and `NotificationDto.cs`
3. **Service**: Updated `NotificationService.cs` with new field mapping and helper method
4. **Controller**: Updated `ContactRequestController.cs` to populate new fields

## Usage Examples

### Creating Contact Notifications
```csharp
var notificationDto = new CreateNotificationDto
{
    UserId = propertyOwnerId,
    Type = "Contact",
    Title = "New Contact Request",
    Message = "You have a new contact request...",
    RelatedEntityId = contactRequestId,     // Contact request ID
    RelatedPropertyId = propertyId,         // Property ID
    ActionUrl = "/bds/{propertyId}"         // Direct link to property
};
```

### Frontend Integration
```javascript
// When notification is clicked
if (notification.ActionUrl) {
    // Navigate to the action URL
    router.push(notification.ActionUrl);
    
    // Or open in new tab for external links
    window.open(notification.ActionUrl, '_blank');
}

// Access related IDs for custom actions
if (notification.RelatedPropertyId) {
    // Load property details
    loadPropertyDetails(notification.RelatedPropertyId);
}

if (notification.RelatedEntityId && notification.Type === 'Contact') {
    // Load contact request details
    loadContactRequest(notification.RelatedEntityId);
}
```

## URL Patterns

The `NotificationService.ConstructActionUrl()` helper method creates URLs based on notification type:

- **Contact**: `/bds/{propertyId}` (property detail page)
- **Transaction**: `/dashboard/transactions/{transactionId}`
- **WalletUpdate**: `/dashboard/wallet`
- **System**: `/dashboard/notifications`
- **Promotion**: `/promotions`
- **News**: `/news`

## Migration Instructions

1. **Apply Migration**: Run `002_AddNotificationActionFields.sql`
2. **Update Application**: Deploy updated application code
3. **Test**: Verify notifications are created with proper action fields
4. **Rollback** (if needed): Run `002_AddNotificationActionFields_Rollback.sql`

## Benefits

1. **Better UX**: Users can click notifications to go directly to relevant pages
2. **Context Preservation**: Related IDs help maintain context when navigating
3. **Flexible Actions**: Different notification types can have different actions
4. **Analytics**: Track which notifications are most engaging
5. **Deep Linking**: Support for direct navigation to specific content

## Future Enhancements

- Add notification click tracking
- Support for multiple action buttons per notification
- Rich notification content with embedded actions
- Push notification integration with deep links
