export async function GET(req) {
  const { searchParams } = new URL(req.url);
  const input = searchParams.get("input");

  if (!input) {
    return Response.json({ error: "Missing input query" }, { status: 400 });
  }

  try {

    const apiURl = `https://rsapi.goong.io/Place/AutoComplete?api_key=${process.env.GOONG_GEO_API_KEY}&input=${encodeURIComponent(input)}&limit=15&more_compound=true`;
    const response = await fetch(
      apiURl
    );

    if (!response.ok) {
      throw new Error("Failed to fetch address suggestions");
    }

    const data = await response.json();
    return Response.json(data, { status: 200 });
  } catch (error) {
    console.error(error);
    return Response.json({ error: error.message }, { status: 500 });
  }
}
