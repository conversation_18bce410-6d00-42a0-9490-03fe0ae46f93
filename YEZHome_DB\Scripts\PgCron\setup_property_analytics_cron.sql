-- ===================================================
-- Property Analytics pg-cron Setup
-- ===================================================

-- Enable pg_cron extension (run as superuser)
CREATE EXTENSION IF NOT EXISTS pg_cron;

-- ===================================================
-- 1. Create stored procedures for analytics updates
-- ===================================================

-- Update recent property analytics (last 24 hours)
CREATE OR REPLACE FUNCTION update_recent_property_analytics()
RETURNS void AS $$
BEGIN
    -- Update summaries for properties with recent activity
    UPDATE "PropertyEngagementSummary" 
    SET 
        "TotalViews" = (
            SELECT COUNT(*) 
            FROM "PropertyViewLog" 
            WHERE "PropertyId" = "PropertyEngagementSummary"."PropertyId"
        ),
        "UniqueViews" = (
            SELECT COUNT(DISTINCT "ViewerId") 
            FROM "PropertyViewLog" 
            WHERE "PropertyId" = "PropertyEngagementSummary"."PropertyId" 
            AND "ViewerId" IS NOT NULL
        ),
        "TotalClicksPhone" = (
            SELECT COUNT(*) 
            FROM "PropertyEngagementEvents" 
            WHERE "PropertyId" = "PropertyEngagementSummary"."PropertyId" 
            AND "EventType" = 'click_phone'
        ),
        "TotalClicksChat" = (
            SELECT COUNT(*) 
            FROM "PropertyEngagementEvents" 
            WHERE "PropertyId" = "PropertyEngagementSummary"."PropertyId" 
            AND "EventType" = 'chat'
        ),
        "LastViewedAt" = (
            SELECT MAX("ViewedAt") 
            FROM "PropertyViewLog" 
            WHERE "PropertyId" = "PropertyEngagementSummary"."PropertyId"
        ),
        "LastUpdatedAt" = CURRENT_TIMESTAMP
    WHERE "PropertyId" IN (
        SELECT DISTINCT "PropertyId" 
        FROM "PropertyViewLog" 
        WHERE "ViewedAt" >= CURRENT_TIMESTAMP - INTERVAL '24 hours'
        
        UNION
        
        SELECT DISTINCT "PropertyId" 
        FROM "PropertyEngagementEvents" 
        WHERE "CreatedAt" >= CURRENT_TIMESTAMP - INTERVAL '24 hours'
        
        UNION
        
        SELECT DISTINCT "PropertyId" 
        FROM "PropertySpendingLog" 
        WHERE "SpentAt" >= CURRENT_TIMESTAMP - INTERVAL '24 hours'
    );
    
    RAISE NOTICE 'Updated recent property analytics for % properties', ROW_COUNT;
END;
$$ LANGUAGE plpgsql;

-- Update all property analytics
CREATE OR REPLACE FUNCTION update_all_property_analytics()
RETURNS void AS $$
DECLARE
    updated_count INTEGER;
BEGIN
    -- Insert or update all summaries
    INSERT INTO "PropertyEngagementSummary" (
        "Id", "PropertyId", "TotalViews", "UniqueViews", "TotalFavorites",
        "TotalSpent", "ExtensionSpent", "HighlightSpent", "TotalClicksPhone",
        "TotalClicksChat", "TotalSearchImpressions", "TotalClickThroughs",
        "ConversionCount", "LastViewedAt", "LastUpdatedAt", "CreatedAt"
    )
    SELECT 
        gen_random_uuid(),
        p."Id",
        COALESCE(v.view_count, 0),
        COALESCE(v.unique_views, 0),
        COALESCE(f.favorite_count, 0),
        COALESCE(s.total_spent, 0),
        COALESCE(s.extension_spent, 0),
        COALESCE(s.highlight_spent, 0),
        COALESCE(e.phone_clicks, 0),
        COALESCE(e.chat_clicks, 0),
        COALESCE(e.search_impressions, 0),
        COALESCE(e.click_throughs, 0),
        COALESCE(e.conversions, 0),
        v.last_viewed,
        CURRENT_TIMESTAMP,
        CURRENT_TIMESTAMP
    FROM "Property" p
    LEFT JOIN (
        SELECT 
            "PropertyId",
            COUNT(*) as view_count,
            COUNT(DISTINCT "ViewerId") as unique_views,
            MAX("ViewedAt") as last_viewed
        FROM "PropertyViewLog"
        GROUP BY "PropertyId"
    ) v ON p."Id" = v."PropertyId"
    LEFT JOIN (
        SELECT 
            "PropertyID",
            COUNT(*) as favorite_count
        FROM "UserFavorite"
        GROUP BY "PropertyID"
    ) f ON p."Id" = f."PropertyID"
    LEFT JOIN (
        SELECT 
            "PropertyId",
            SUM("Amount") as total_spent,
            SUM(CASE WHEN "SpendingType" ILIKE '%extension%' THEN "Amount" ELSE 0 END) as extension_spent,
            SUM(CASE WHEN "SpendingType" ILIKE '%highlight%' THEN "Amount" ELSE 0 END) as highlight_spent
        FROM "PropertySpendingLog"
        GROUP BY "PropertyId"
    ) s ON p."Id" = s."PropertyId"
    LEFT JOIN (
        SELECT 
            "PropertyId",
            COUNT(CASE WHEN "EventType" = 'click_phone' THEN 1 END) as phone_clicks,
            COUNT(CASE WHEN "EventType" = 'chat' THEN 1 END) as chat_clicks,
            COUNT(CASE WHEN "EventType" = 'search_impression' THEN 1 END) as search_impressions,
            COUNT(CASE WHEN "EventType" = 'click_through' THEN 1 END) as click_throughs,
            COUNT(CASE WHEN "EventType" = 'conversion' THEN 1 END) as conversions
        FROM "PropertyEngagementEvents"
        GROUP BY "PropertyId"
    ) e ON p."Id" = e."PropertyId"
    ON CONFLICT ("PropertyId") DO UPDATE SET
        "TotalViews" = EXCLUDED."TotalViews",
        "UniqueViews" = EXCLUDED."UniqueViews",
        "TotalFavorites" = EXCLUDED."TotalFavorites",
        "TotalSpent" = EXCLUDED."TotalSpent",
        "ExtensionSpent" = EXCLUDED."ExtensionSpent",
        "HighlightSpent" = EXCLUDED."HighlightSpent",
        "TotalClicksPhone" = EXCLUDED."TotalClicksPhone",
        "TotalClicksChat" = EXCLUDED."TotalClicksChat",
        "TotalSearchImpressions" = EXCLUDED."TotalSearchImpressions",
        "TotalClickThroughs" = EXCLUDED."TotalClickThroughs",
        "ConversionCount" = EXCLUDED."ConversionCount",
        "LastViewedAt" = EXCLUDED."LastViewedAt",
        "LastUpdatedAt" = EXCLUDED."LastUpdatedAt";
    
    GET DIAGNOSTICS updated_count = ROW_COUNT;
    RAISE NOTICE 'Updated analytics for % properties', updated_count;
END;
$$ LANGUAGE plpgsql;

-- ===================================================
-- 2. Schedule cron jobs
-- ===================================================

-- Recent updates every 5 minutes
SELECT cron.schedule(
    'update-recent-property-analytics',
    '*/5 * * * *',
    'SELECT update_recent_property_analytics();'
);

-- Full updates every hour
SELECT cron.schedule(
    'update-all-property-analytics',
    '0 * * * *',
    'SELECT update_all_property_analytics();'
);

-- ===================================================
-- 3. View scheduled jobs
-- ===================================================
SELECT 
    jobname,
    schedule,
    command,
    active
FROM cron.job
WHERE jobname LIKE '%property-analytics%';

-- ===================================================
-- 4. Management commands (for later use)
-- ===================================================

-- Disable jobs
-- SELECT cron.unschedule('update-recent-property-analytics');
-- SELECT cron.unschedule('update-all-property-analytics');

-- Re-enable jobs
-- SELECT cron.schedule('update-recent-property-analytics', '*/5 * * * *', 'SELECT update_recent_property_analytics();');
-- SELECT cron.schedule('update-all-property-analytics', '0 * * * *', 'SELECT update_all_property_analytics();');

-- Check job execution history
-- SELECT * FROM cron.job_run_details WHERE jobname LIKE '%property-analytics%' ORDER BY start_time DESC LIMIT 10; 