"use client";

import { useState } from "react";
import { Eye, EyeOff, CircleAlert, Mail, Lock } from "lucide-react";
import ButtonLoading from "@/components/ui/ButtonLoading";
import { useActionState } from "react";
import { loginUser } from "@/app/actions/server/authenticate";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Link } from "@/i18n/navigation";
import { useTranslations } from "next-intl";
import { Input } from "../ui/input";

const initialState = {
  errors: {},
  message: null,
  fieldValues: {
    email: "",
    password: "",
  },
};

export default function LoginForm() {
  const t = useTranslations("LoginPage");
  const [showPassword, setShowPassword] = useState(false);
  const [state, formAction, isPending] = useActionState(loginUser, initialState);

  return (
    <form className="space-y-6" action={formAction}>
      {state?.message && (
        <Alert variant="destructive" className="bg-red-600 text-white border-red-800">
          <CircleAlert className="h-4 w-4 text-white" />
          <AlertTitle className="text-white">{t("loginErrorTitle")}</AlertTitle>
          <AlertDescription className="text-white">{state.message}</AlertDescription>
        </Alert>
      )}
      <div className="space-y-2">
        <label htmlFor="email" className="sr-only">
          {t("emailLabel")}
        </label>
        <div className="relative">
          <Mail className="absolute left-3 top-1/2 -translate-y-1/2 h-5 w-5 text-gray-400" />
          <Input
            id="email"
            name="email"
            type="email"
            autoComplete="email"
            required
            defaultValue={state.fieldValues?.email}
            className="pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-teal-500 focus:border-teal-500"
            placeholder={t("emailPlaceholder")}
          />
          {state.errors?.email && <p className="mt-1 text-xs text-red-500">{state.errors.email[0]}</p>}
        </div>
      </div>
      <div className="space-y-2">
        <label htmlFor="password" className="sr-only">
          {t("passwordLabel")}
        </label>
        <div className="relative">
          <Lock className="absolute left-3 top-1/2 -translate-y-1/2 h-5 w-5 text-gray-400" />
          <Input
            id="password"
            name="password"
            type={showPassword ? "text" : "password"}
            autoComplete="current-password"
            required
            defaultValue={state.fieldValues?.password}
            className="pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-teal-500 focus:border-teal-500"
            placeholder={t("passwordPlaceholder")}
          />
          <button
            type="button"
            className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-400 hover:text-gray-600"
            onClick={() => setShowPassword(!showPassword)}
          >
            {showPassword ? <EyeOff className="h-5 w-5 text-gray-400" /> : <Eye className="h-5 w-5 text-gray-400" />}
          </button>
          {state.errors?.password && <p className="mt-1 text-xs text-red-500">{state.errors.password[0]}</p>}
        </div>
      </div>

      <div className="flex items-center justify-between text-sm">
        <div className="flex items-center">
          <input id="remember-me" name="remember-me" type="checkbox" className="h-4 w-4" />
          <label htmlFor="remember-me" className="ml-2 block text-sm">
            {t("rememberMeLabel")}
          </label>
        </div>

        <div className="text-sm">
          <Link href="/quen-mat-khau" className="text-teal-600 hover:underline">
            {t("forgotPasswordLink")}
          </Link>
        </div>
      </div>

      <div>
        <ButtonLoading type="submit" showLoading={isPending} title={t("loginButton")} />
      </div>
    </form>
  );
}
