'use client';

import { useState, useEffect, memo } from 'react';
import { LoaderCircle } from 'lucide-react';
import { getPropertyReportById } from '@/app/actions/server/property'; // Import the server action
import { formatCurrency } from '@/lib/utils';
import { CardContent } from '@/components/ui/card'; // Use CardContent for consistent styling

// Cache for report data to avoid duplicate API calls
const reportCache = new Map();

const PropertyReportCard = memo(function PropertyReportCard({ propertyId, reportData: preloadedReportData }) {
  const [reportData, setReportData] = useState(preloadedReportData || null);
  const [isLoading, setIsLoading] = useState(!preloadedReportData);
  const [error, setError] = useState(null);

  useEffect(() => {
    // If we already have preloaded data, don't fetch
    if (preloadedReportData) {
      setReportData(preloadedReportData);
      setIsLoading(false);
      return;
    }

    // Check cache first
    if (reportCache.has(propertyId)) {
      const cachedData = reportCache.get(propertyId);
      setReportData(cachedData);
      setIsLoading(false);
      return;
    }

    async function fetchReportData() {
      if (!propertyId) return;

      setIsLoading(true);
      setError(null);
      try {
        const result = await getPropertyReportById(propertyId);
        if (result && result?.success) {
          const data = result?.data;
          setReportData(data);
          // Cache the result
          reportCache.set(propertyId, data);
        } else {
          setError(result?.message || 'Lỗi tải báo cáo.');
          console.error("Error fetching report:", result?.message);
        }
      } catch (err) {
        setError('Lỗi kết nối.');
        console.error("Network or other error fetching report:", err);
      } finally {
        setIsLoading(false);
      }
    }

    fetchReportData();
  }, [propertyId, preloadedReportData]); // Re-fetch if propertyId changes

  if (isLoading) {
    return (
      <CardContent className="flex items-center justify-center p-4 text-xs text-gray-500 w-full md:w-1/4 lg:w-1/5 md:shrink-0 border-b md:border-b-0 md:border-r min-h-[150px]">
        <LoaderCircle className="animate-spin h-5 w-5 mr-2" />
        Đang tải báo cáo...
      </CardContent>
    );
  }

  if (error) {
    return (
      <CardContent className="flex items-center justify-center p-4 text-xs text-red-600 w-full md:w-1/4 lg:w-1/5 md:shrink-0 border-b md:border-b-0 md:border-r min-h-[150px]">
        {error}
      </CardContent>
    );
  }

  if (!reportData) {
    return (
      <CardContent className="flex items-center justify-center p-4 text-xs text-gray-500 w-full md:w-1/4 lg:w-1/5 md:shrink-0 border-b md:border-b-0 md:border-r min-h-[150px]">
        Không có dữ liệu báo cáo.
      </CardContent>
    );
  }

  // Render the full report details
  return (
    <CardContent className="grid gap-1 p-4 text-xs text-gray-600 w-full md:w-1/4 lg:w-1/5 md:shrink-0 border-b md:border-b-0 md:border-r">
      <p>Lượt xem: {reportData.views}</p>
      <p>Lượt hiển thị: {reportData.impressions}</p>
      {/* Requirement 4.2 items */}
      <p>Lượt bỏ vào giỏ hàng: {reportData.cartAdds ?? 'N/A'}</p>
      <p>Số lần highlight: {reportData.highlightsCount ?? 'N/A'}</p>
      <p>Số lần gia hạn: {reportData.renewalsCount ?? 'N/A'}</p>
      <hr className="my-1" />
      <p>Tiền bài đăng: {formatCurrency(reportData.postCost ?? 0)}</p>
      <p>Tổng tiền highlight: {formatCurrency(reportData.highlightCost ?? 0)}</p>
      <p>Tổng tiền gia hạn: {formatCurrency(reportData.renewalCost ?? 0)}</p>
      <p className="font-medium mt-1">Tổng số tiền: {formatCurrency(reportData.totalCost ?? 0)}</p>
    </CardContent>
  );
});

export default PropertyReportCard;