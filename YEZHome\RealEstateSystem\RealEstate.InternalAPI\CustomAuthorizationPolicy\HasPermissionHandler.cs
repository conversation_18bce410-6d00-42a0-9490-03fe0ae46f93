﻿using Microsoft.AspNetCore.Authorization;
using System.Security.Claims;
using System.Threading.Tasks;
using System.Linq;

namespace RealEstate.InternalAPI.CustomAuthorizationPolicy
{
    // Yêu cầu (Requirement) để kiểm tra một quyền cụ thể
    public class HasPermissionRequirement : IAuthorizationRequirement
    {
        public string PermissionCode { get; } // Sử dụng 'Code' của Permission trong DB

        public HasPermissionRequirement(string permissionCode)
        {
            PermissionCode = permissionCode;
        }
    }

    // Handler để xử lý yêu cầu HasPermissionRequirement
    public class HasPermissionHandler : AuthorizationHandler<HasPermissionRequirement>
    {
        protected override Task HandleRequirementAsync(AuthorizationHandlerContext context, HasPermissionRequirement requirement)
        {
            // Lấy tất cả các "permission codes" từ Claims của người dùng trong JWT
            // Đảm bảo khi bạn tạo JWT, bạn đã thêm các quyền này vào dưới dạng claims.
            // Ví dụ: claims.Add(new Claim("permission_code", permission.Code));
            var userPermissions = context.User.Claims
                .Where(c => c.Type == "permission_code") // "permission_code" là loại claim bạn tự định nghĩa
                .Select(c => c.Value)
                .ToList();

            // Kiểm tra xem người dùng có quyền cần thiết không
            if (userPermissions.Contains(requirement.PermissionCode))
            {
                context.Succeed(requirement); // Nếu có, ủy quyền thành công
            }

            return Task.CompletedTask;
        }
    }
}
