"use client";

import { FormField, FormItem, FormLabel, FormControl, FormMessage } from "@/components/ui/form";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { useTranslations } from "next-intl";
import { Separator } from "@/components/ui/separator";
import CollapseHeader from "@/components/ui/collapse";

const PropertyBasicInfoSection = ({ form, isFormDisabled }) => {
  const t = useTranslations("PropertyForm");

  return (
    <CollapseHeader title={t("basicInfo")} subTitle={t("requiredInfo")}>
      <Separator className="mb-6" />
      <FormField
        control={form.control}
        name="postType"
        render={({ field }) => (
          <FormItem>
            <FormLabel>{t("postType")}</FormLabel>
            <FormControl>
              <RadioGroup onValueChange={field.onChange} defaultValue={field.value} className="flex gap-4 mt-2">
                <FormItem className="flex items-center space-x-3 space-y-0">
                  <FormControl>
                    <RadioGroupItem value="sell" disabled={isFormDisabled} />
                  </FormControl>
                  <FormLabel className="font-normal">{t("sell")}</FormLabel>
                </FormItem>
                <FormItem className="flex items-center space-x-3 space-y-0">
                  <FormControl>
                    <RadioGroupItem value="rent" disabled={isFormDisabled} />
                  </FormControl>
                  <FormLabel className="font-normal">{t("rent")}</FormLabel>
                </FormItem>
              </RadioGroup>
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div>
          <FormField
            control={form.control}
            name="propertyType"
            render={({ field }) => (
              <FormItem className="mt-3">
                <FormLabel>{t("propertyType")}</FormLabel>
                <FormControl>
                  <Select onValueChange={field.onChange} defaultValue={field.value} disabled={isFormDisabled}>
                    <SelectTrigger className="mt-1">
                      <SelectValue placeholder={t("selectPropertyType")} />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="nha_rieng">{t("house")}</SelectItem>
                      <SelectItem value="can_ho">{t("apartment")}</SelectItem>
                      <SelectItem value="nha_tro">{t("motel")}</SelectItem>
                    </SelectContent>
                  </Select>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
        <div>
          <FormField
            control={form.control}
            name="price"
            render={({ field }) => (
              <FormItem className="mt-3">
                <FormLabel>{t("price")}</FormLabel>
                <FormControl>
                  <Input placeholder={t("pricePlaceholder")} {...field} type="number" suffix=" VND" disabled={isFormDisabled} />
                </FormControl>
                <FormMessage></FormMessage>
              </FormItem>
            )}
          />
        </div>
      </div>
    </CollapseHeader>
  );
};

export default PropertyBasicInfoSection;
