-- Add IsActive column to AppUser table
ALTER TABLE "AppUser"
    ADD COLUMN "IsActive" BOOLEAN NOT NULL DEFAULT TRUE;

-- Add index for faster queries on active status
CREATE INDEX "IDX_AppUser_IsActive" ON "AppUser"("IsActive");

-- Comment on column for better documentation
COMMENT ON COLUMN "AppUser"."IsActive" IS 'Indicates if the user account is active (true) or deactivated (false)';

ALTER TABLE IF EXISTS public."UserRole"
ADD CONSTRAINT "UserRole_Unique_UserID_RoleID" UNIQUE ("UserID", "RoleID");
