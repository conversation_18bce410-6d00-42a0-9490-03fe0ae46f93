using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using static RealEstate.Domain.Common.EnumValues;

namespace RealEstate.Application.DTO.Analytics
{
    public class LogPropertySpendingDto
    {
        [Required]
        public Guid PropertyId { get; set; }
        
        [Required]
        public Guid UserId { get; set; }
        
        [Required]
        [Range(0, double.MaxValue, ErrorMessage = "Amount must be greater than or equal to 0")]
        [Column(TypeName = "numeric(20,2)")]
        public decimal Amount { get; set; }
        
        [Required]
        public PropertySpendingType SpendingType { get; set; }
        
        public Guid? TransactionId { get; set; }
        
        [StringLength(500)]
        public string? Details { get; set; }
    }
} 