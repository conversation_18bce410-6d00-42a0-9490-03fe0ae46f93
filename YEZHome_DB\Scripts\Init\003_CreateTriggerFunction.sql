CREATE SEQUENCE transfer_code_seq
  START 1000
  INCREMENT 1
  MINVALUE 1000
  MAXVALUE 99999999
  CYCLE; -- nếu muốn quay lại sau khi vượt max


CREATE OR REPLACE FUNCTION generate_transfer_code()
RET<PERSON>NS TEXT AS $$
DECLARE
  nextval BIGINT;
  date_part TEXT;
BEGIN
  nextval := nextval('transfer_code_seq');
  date_part := TO_CHAR(NOW(), 'DDMMYY');
  RETURN 'YEZH' || LPAD(nextval::TEXT, 6, '0') || date_part;
END;
$$ LANGUAGE plpgsql;


CREATE OR REPLACE FUNCTION set_transfer_code_before_insert()
RETURNS TRIGGER AS $$
BEGIN
  -- Only set if not already provided
  IF NEW."TransferCode" IS NULL THEN
    NEW."TransferCode" := generate_transfer_code();
  END IF;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;


CREATE TRIGGER set_transfer_code_before_insert_trigger
BEFORE INSERT ON "AppUser"
FOR EACH ROW
EXECUTE FUNCTION set_transfer_code_before_insert();
