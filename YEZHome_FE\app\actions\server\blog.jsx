"use server";

const API_BASE_URL = `${process.env.API_URL}/api/Blog`;

export async function getBlogBySlug(slug) {
  try {
    const response = await fetch(`${API_BASE_URL}/slug/${slug}`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => null);
      return {
        success: false,
        message: errorData?.message || "Không thể lấy thông tin bất động sản",
      };
    }

    const data = await response.json();
    return {
      success: true,
      data,
    };
  } catch (error) {
    console.error("Lỗi khi lấy thông tin bất động sản:", error);
    return {
      success: false,
      message: "Đã xảy ra lỗi khi lấy thông tin bất động sản",
    };
  }
}

export async function getAllBlog() {
  try {
    const response = await fetch(API_BASE_URL, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => null);
      return {
        success: false,
        message: errorData?.message || "Không thể lấy danh sách bài viết",
      };
    }

    const data = await response.json();
    return {
      success: true,
      data,
    };
  } catch (error) {
    console.error("Lỗi khi lấy danh sách bài viết:", error);
    return {
      success: false,
      message: "Đã xảy ra lỗi khi lấy danh sách bài viết",
    };
  }
}

export async function getBlogPosts({
  PageNumber = 1,
  PageSize = 10,
  SortColumn = "publishedAt",
  SortDescending = true,
  title = "",
}) {
  try {
    const queryParams = new URLSearchParams({
      PageNumber,
      PageSize,
      SortColumn,
      SortDescending,
      title,
    }).toString();

    const response = await fetch(`${API_BASE_URL}/blog-posts?${queryParams}`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => null);
      return {
        success: false,
        message: errorData?.message || "Không thể lấy danh sách bài viết",
      };
    }

    const data = await response.json();
    return {
      success: true,
      data: {...data},
    };
  } catch (error) {
    console.error("Lỗi khi lấy danh sách bài viết:", error);
    return {
      success: false,
      message: "Đã xảy ra lỗi khi lấy danh sách bài viết",
    };
  }
}
