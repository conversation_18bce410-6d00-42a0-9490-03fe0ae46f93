using System.ComponentModel.DataAnnotations;

namespace RealEstate.Domain.Entities
{
    public class AdminRole : BaseEntity
    {
        [Required]
        public required string RoleName { get; set; }

        [Required]
        public required string Code { get; set; }

        // Navigation properties
        public ICollection<UserRole> UserRoles { get; set; } = new List<UserRole>();
        public ICollection<RolePermission> RolePermissions { get; set; } = new List<RolePermission>();
    }
}