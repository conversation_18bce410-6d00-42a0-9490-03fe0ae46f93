{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/components/layout/NoData.jsx"], "sourcesContent": ["import { memo } from \"react\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { Plus } from \"lucide-react\";\r\nimport { Link } from \"@/i18n/navigation\";\r\nimport Image from \"next/image\";\r\nimport { useTranslations } from \"next-intl\";\r\n\r\nfunction NoData({\r\n  message: messageProp,\r\n  hasCreateButton = false,\r\n  createMessage: createMessageProp,\r\n  createPageRoute = \"/user/bds/new\",\r\n  createButtonTitle: createButtonTitleProp,\r\n}) {\r\n  const t = useTranslations(\"NoData\");\r\n\r\n  const message = messageProp ?? t(\"defaultMessage\");\r\n  const createMessage = createMessageProp ?? t(\"defaultCreateMessage\");\r\n  const createButtonTitle = createButtonTitleProp ?? t(\"defaultCreateButtonTitle\");\r\n\r\n  return (\r\n    <div className=\"flex flex-col items-center justify-center min-h-[400px] p-4\">\r\n      <Image\r\n        src=\"/no_data.png\"\r\n        alt=\"Empty state illustration\"\r\n        width={256}\r\n        height={256}\r\n        className=\"mb-6 opacity-80\"\r\n        loading=\"lazy\"\r\n        quality={80}\r\n      />\r\n      <h2 className=\"text-lg text-gray-600 mb-2\">{message}</h2>\r\n      {hasCreateButton && (\r\n        <>\r\n          <p className=\"text-gray-500 mb-6\">{createMessage}</p>\r\n          <Button asChild className=\"gap-2 bg-coral-500 hover:bg-coral-600\">\r\n            <Link href=\"/user/bds/new\">\r\n              <Plus className=\"h-4 w-4\" />\r\n              {createButtonTitle}\r\n            </Link>\r\n          </Button>\r\n        </>\r\n      )}\r\n    </div>\r\n  );\r\n}\r\n\r\n// Memoize the component to prevent unnecessary re-renders\r\nexport default memo(NoData);\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;AAEA,SAAS,OAAO,EACd,SAAS,WAAW,EACpB,kBAAkB,KAAK,EACvB,eAAe,iBAAiB,EAChC,kBAAkB,eAAe,EACjC,mBAAmB,qBAAqB,EACzC;;IACC,MAAM,IAAI,CAAA,GAAA,yMAAA,CAAA,kBAAe,AAAD,EAAE;IAE1B,MAAM,UAAU,eAAe,EAAE;IACjC,MAAM,gBAAgB,qBAAqB,EAAE;IAC7C,MAAM,oBAAoB,yBAAyB,EAAE;IAErD,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,gIAAA,CAAA,UAAK;gBACJ,KAAI;gBACJ,KAAI;gBACJ,OAAO;gBACP,QAAQ;gBACR,WAAU;gBACV,SAAQ;gBACR,SAAS;;;;;;0BAEX,6LAAC;gBAAG,WAAU;0BAA8B;;;;;;YAC3C,iCACC;;kCACE,6LAAC;wBAAE,WAAU;kCAAsB;;;;;;kCACnC,6LAAC,8HAAA,CAAA,SAAM;wBAAC,OAAO;wBAAC,WAAU;kCACxB,cAAA,6LAAC,qHAAA,CAAA,OAAI;4BAAC,MAAK;;8CACT,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;gCACf;;;;;;;;;;;;;;;;;;;;AAOf;GAtCS;;QAOG,yMAAA,CAAA,kBAAe;;;KAPlB;2DAyCM,CAAA,GAAA,6JAAA,CAAA,OAAI,AAAD,EAAE", "debugId": null}}, {"offset": {"line": 124, "column": 0}, "map": {"version": 3, "file": "plus.js", "sources": ["file://D%3A/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/node_modules/lucide-react/src/icons/plus.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M5 12h14', key: '1ays0h' }],\n  ['path', { d: 'M12 5v14', key: 's699le' }],\n];\n\n/**\n * @component @name Plus\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNNSAxMmgxNCIgLz4KICA8cGF0aCBkPSJNMTIgNXYxNCIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/plus\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Plus = createLucideIcon('Plus', __iconNode);\n\nexport default Plus;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA,CAAA;IACzC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;CAC3C,CAAA;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAO,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,EAAQ,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 169, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/node_modules/next/src/shared/lib/image-external.tsx"], "sourcesContent": ["import type { ImageConfigComplete, ImageLoaderProps } from './image-config'\nimport type { ImageProps, ImageLoader, StaticImageData } from './get-img-props'\n\nimport { getImgProps } from './get-img-props'\nimport { Image } from '../../client/image-component'\n\n// @ts-ignore - This is replaced by webpack alias\nimport defaultLoader from 'next/dist/shared/lib/image-loader'\n\n/**\n * For more advanced use cases, you can call `getImageProps()`\n * to get the props that would be passed to the underlying `<img>` element,\n * and instead pass to them to another component, style, canvas, etc.\n *\n * Read more: [Next.js docs: `getImageProps`](https://nextjs.org/docs/app/api-reference/components/image#getimageprops)\n */\nexport function getImageProps(imgProps: ImageProps) {\n  const { props } = getImgProps(imgProps, {\n    defaultLoader,\n    // This is replaced by webpack define plugin\n    imgConf: process.env.__NEXT_IMAGE_OPTS as any as ImageConfigComplete,\n  })\n  // Normally we don't care about undefined props because we pass to JSX,\n  // but this exported function could be used by the end user for anything\n  // so we delete undefined props to clean it up a little.\n  for (const [key, value] of Object.entries(props)) {\n    if (value === undefined) {\n      delete props[key as keyof typeof props]\n    }\n  }\n  return { props }\n}\n\nexport default Image\n\nexport type { ImageProps, ImageLoaderProps, ImageLoader, StaticImageData }\n"], "names": ["getImageProps", "imgProps", "props", "getImgProps", "defaultLoader", "imgConf", "process", "env", "__NEXT_IMAGE_OPTS", "key", "value", "Object", "entries", "undefined", "Image"], "mappings": "AAoBaM,QAAQC,GAAG,CAACC,iBAAiB;;;;;;;;;;;;;;;;IAa1C,OAAoB,EAAA;eAApB;;IAjBgBR,aAAa,EAAA;eAAbA;;;;6BAbY;gCACN;sEAGI;AASnB,SAASA,cAAcC,QAAoB;IAChD,MAAM,EAAEC,KAAK,EAAE,GAAGC,CAAAA,GAAAA,aAAAA,WAAW,EAACF,UAAU;QACtCG,eAAAA,aAAAA,OAAa;QACb,4CAA4C;QAC5CC,OAAAA;IACF;IACA,uEAAuE;IACvE,wEAAwE;IACxE,wDAAwD;IACxD,KAAK,MAAM,CAACI,KAAKC,MAAM,IAAIC,OAAOC,OAAO,CAACV,OAAQ;QAChD,IAAIQ,UAAUG,WAAW;YACvB,OAAOX,KAAK,CAACO,IAA0B;QACzC;IACF;IACA,OAAO;QAAEP;IAAM;AACjB;MAEA,WAAeY,gBAAAA,KAAK", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 220, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/node_modules/next/image.js"], "sourcesContent": ["module.exports = require('./dist/shared/lib/image-external')\n"], "names": [], "mappings": "AAAA,OAAO,OAAO", "ignoreList": [0], "debugId": null}}]}