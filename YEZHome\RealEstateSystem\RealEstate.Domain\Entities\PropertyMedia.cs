namespace RealEstate.Domain.Entities
{
    public class PropertyMedia : BaseEntity
    {
        public Guid? PropertyID { get; set; }
        public Property Property { get; set; }
        public string? MediaType { get; set; }
        public string? MediaURL { get; set; }
        public string? FilePath { get; set; }
        public string? ThumbnailPath { get; set; }
        public string? SmallPath { get; set; }
        public string? MediumPath { get; set; }
        public string? LargePath { get; set; }
        public DateTime UploadedAt { get; set; }
        public string? Caption { get; set; }
        public bool? IsAvatar { get; set; }
    }
}