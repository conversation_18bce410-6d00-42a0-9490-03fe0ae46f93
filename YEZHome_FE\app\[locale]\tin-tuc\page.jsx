// app/blog/page.js
"use client";

import React, { useState, useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import Image from "next/image";
import {Link} from '@/i18n/navigation';;
import { useTranslations, useLocale } from "next-intl";
import { useDebouncedCallback } from "use-debounce";

// Import shadcn/UI components
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent } from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { DEFAULT_ITEM_PER_PAGE, DEFAULT_PAGE } from "@/lib/enum";
import { getBlogPosts } from "../../actions/server/blog";
import NoData from "@/components/layout/NoData";

export default function BlogListingPage() {
  const t = useTranslations("NewsPage");
  const locale = useLocale();
  const router = useRouter();
  const searchParams = useSearchParams();

  // Get initial values from URL search params
  const initialPageNumber = Number(searchParams.get("page")) || DEFAULT_PAGE;
  const initialPageSize = Number(searchParams.get("pageSize")) || DEFAULT_ITEM_PER_PAGE;
  const initialSearchTitle = searchParams.get("title") || "";
  const initialSortColumn = searchParams.get("sortColumn") || "publishedAt";
  const initialSortDescending = searchParams.get("sortDescending") !== "false";

  // State variables
  const [blogPosts, setBlogPosts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [totalCount, setTotalCount] = useState(0);

  // Pagination and sorting state
  const [pageNumber, setPageNumber] = useState(initialPageNumber);
  const [pageSize, setPageSize] = useState(initialPageSize);
  const [searchTitle, setSearchTitle] = useState(initialSearchTitle);
  const [draftSearchTitle, setDraftSearchTitle] = useState(initialSearchTitle);
  const [sortColumn, setSortColumn] = useState(initialSortColumn);
  const [sortDescending, setSortDescending] = useState(initialSortDescending);

  // Available page sizes for dropdown
  const pageSizeOptions = [5, 10, 20, 50];

  // Update URL with current filters and pagination
  const updateUrl = useDebouncedCallback(() => {
    const params = new URLSearchParams();
    params.set("page", pageNumber.toString());
    params.set("pageSize", pageSize.toString());
    params.set("sortColumn", sortColumn);
    params.set("sortDescending", sortDescending.toString());

    if (searchTitle) {
      params.set("title", searchTitle);
    }

    router.replace(`/tin-tuc?${params.toString()}`, { scroll: false });
  }, 300);

  // Fetch blog posts using server action
  const fetchBlogPosts = async () => {
    setLoading(true);
    setError(null);
    try {
      const response = await getBlogPosts({
        PageNumber: pageNumber,
        PageSize: pageSize,
        SortColumn: sortColumn,
        SortDescending: sortDescending,
        title: searchTitle || undefined,
      });
      setBlogPosts(response?.data?.items || []);
      setTotalCount(response?.data?.totalCount || 0);
    } catch (err) {
      setError(t("fetchError"));
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  // Effect to fetch blog posts whenever dependencies change
  useEffect(() => {
    fetchBlogPosts();
    updateUrl();
  }, [pageNumber, pageSize, sortColumn, sortDescending, searchTitle]);

  // Handle search submission
  const handleSearch = (e) => {
    e.preventDefault();
  };

  const debouncedSetSearchTitle = useDebouncedCallback((value) => {
    setSearchTitle(value);
    setPageNumber(1);
  }, 500);

  // Handle sort toggle
  const handleSortChange = (column) => {
    if (sortColumn === column) {
      setSortDescending(!sortDescending);
    } else {
      setSortColumn(column);
      setSortDescending(true);
    }
    setPageNumber(1);
  };

  // Format date for display
  const formatDate = (dateString) => {
    if (!dateString) return "";
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString(locale, {
        year: "numeric",
        month: "long",
        day: "numeric",
      });
    } catch (error) {
      console.error("Date format error:", error);
      return dateString;
    }
  };

  // Calculate total pages
  const totalPages = Math.ceil(totalCount / pageSize);

  const startItem = (pageNumber - 1) * pageSize + 1;
  const endItem = Math.min(pageNumber * pageSize, totalCount);

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-[800px] mx-auto mb-12 text-center">
        <h1 className="text-3xl md:text-4xl font-semibold mb-4 text-charcoal">
          {t("pageTitle")}
        </h1>
        <p className="text-muted-foreground">
          {t("pageDescription")}
        </p>
      </div>

      {/* Search Form */}
      <form onSubmit={handleSearch} className="mb-6">
        <div className="flex gap-2">
          <Input
            type="text"
            defaultValue={draftSearchTitle}
            onChange={(e) => {
              setDraftSearchTitle(e.target.value);
              debouncedSetSearchTitle(e.target.value);
            }}
            placeholder={t("searchPlaceholder")}
            className="flex-grow"
          />
        </div>
      </form>

      {/* Sort Controls and Page Size */}
      <div className="flex flex-wrap justify-between items-center mb-6 gap-4">
        <div className="flex space-x-4">
          <Button
            onClick={() => handleSortChange("publishedAt")}
            variant={sortColumn === "publishedAt" ? "secondary" : "ghost"}
            className="flex items-center gap-1"
          >
            <span>{t("sortByDate")}</span>
            {sortColumn === "publishedAt" && <span>{sortDescending ? "▼" : "▲"}</span>}
          </Button>
          <Button
            onClick={() => handleSortChange("title")}
            variant={sortColumn === "title" ? "secondary" : "ghost"}
            className="flex items-center gap-1"
          >
            <span>{t("sortByTitle")}</span>
            {sortColumn === "title" && <span>{sortDescending ? "▼" : "▲"}</span>}
          </Button>
        </div>

        <div className="flex items-center gap-2">
          <span className="text-sm text-muted-foreground">{t("pageSizeLabel")}</span>
          <Select
            value={pageSize.toString()}
            onValueChange={(value) => {
              setPageSize(Number(value));
              setPageNumber(1);
            }}
          >
            <SelectTrigger className="w-20">
              <SelectValue placeholder={pageSize} />
            </SelectTrigger>
            <SelectContent>
              {pageSizeOptions.map((size) => (
                <SelectItem key={size} value={size.toString()}>
                  {size}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Error State */}
      {error && <div className="bg-red-100 text-red-700 p-4 rounded mb-4 text-center">{error}</div>}

      {/* Loading State */}
      {loading ? (
        <div className="space-y-6">
          {[...Array(pageSize)].map((_, i) => (
            <Card key={i} className="overflow-hidden">
              <div className="flex flex-col md:flex-row">
                <div className="md:w-1/4 h-48 md:h-auto relative bg-gray-200">
                  <Skeleton className="w-full h-full absolute" />
                </div>
                <CardContent className="md:w-3/4 p-4">
                  <Skeleton className="h-6 w-3/4 mb-2" />
                  <Skeleton className="h-4 w-1/4 mb-4" />
                  <Skeleton className="h-4 w-full mb-2" />
                  <Skeleton className="h-4 w-full mb-2" />
                  <Skeleton className="h-4 w-2/3 mb-4" />
                  <Skeleton className="h-10 w-32" />
                </CardContent>
              </div>
            </Card>
          ))}
        </div>
      ) : (
        <>
          {/* Blog Post List */}
          {!blogPosts || blogPosts.length === 0 ? (
            <NoData message={t("noResults", { defaultMessage: "No blog posts found." })} />
          ) : (
            <div className="space-y-6">
              {blogPosts.map((post) => (
                <Card key={post.id} className="overflow-hidden">
                  <div className="flex flex-col md:flex-row">
                    {/* Featured Image */}
                    <div className="md:w-1/4 h-48 md:h-auto relative bg-gray-100">
                      <Image
                        src={post.featuredImage || "/api/placeholder/400/300"}
                        alt={post.title}
                        fill
                        sizes="(max-width: 768px) 100vw, (max-width: 1200px) 25vw, 25vw"
                        className="object-cover transition-transform duration-300 hover:scale-105"
                      />
                    </div>

                    {/* Content */}
                    <CardContent className="md:w-3/4 p-4">
                      <Link href={`/tin-tuc/${post.slug}`} className="hover:text-primary">
                        <h2 className="text-xl font-semibold mb-2 text-navy-blue line-clamp-2">{post.title}</h2>
                      </Link>
                      <div className="text-muted-foreground text-sm mb-2 flex items-center flex-wrap">
                        {post.authorName && (
                          <>
                            <span>{t("authorPrefix")} {post.authorName}</span>
                            <span className="mx-1.5">{t("dateConnector")}</span>
                          </>
                        )}
                        <span>{formatDate(post.publishedAt)}</span>
                      </div>

                      <p className="text-muted-foreground mb-4 line-clamp-3">
                        {post.excerpt || post.content}
                      </p>

                      {post.tags && (
                        <div className="flex flex-wrap gap-1 mb-4">
                          {post.tags.split(",").map((tag) => (
                            <Badge rounded="full" key={tag} variant="secondary">
                              {tag.trim()}
                            </Badge>
                          ))}
                        </div>
                      )}

                      <Button asChild size="sm">
                        <Link href={`/tin-tuc/${post.slug}`}>{t("readMoreButton")}</Link>
                      </Button>
                    </CardContent>
                  </div>
                </Card>
              ))}
            </div>
          )}

          {/* Pagination */}
          {totalCount > pageSize && !loading && blogPosts?.length > 0 && (
            <div className="flex flex-col sm:flex-row justify-between items-center gap-4 mt-8">
              <div className="text-sm text-muted-foreground">
                {t('paginationShowing', { start: startItem, end: endItem, total: totalCount })}
              </div>
              <div className="flex gap-1">
                <Button
                  onClick={() => setPageNumber((prev) => Math.max(prev - 1, 1))}
                  disabled={pageNumber === 1}
                  variant="outline"
                  size="sm"
                >
                  {t("paginationPrevious")}
                </Button>
                <span className="px-3 py-1.5 text-sm">{pageNumber} / {totalPages}</span>
                <Button
                  onClick={() => setPageNumber((prev) => Math.min(prev + 1, totalPages))}
                  disabled={pageNumber === totalPages}
                  variant="outline"
                  size="sm"
                >
                  {t("paginationNext")}
                </Button>
              </div>
            </div>
          )}
        </>
      )}
    </div>
  );
}
