"use client";
import { memo } from "react";
import { ChevronLeft, ChevronRight } from "lucide-react";
import { Button } from "@/components/ui/button";

const Pagination = memo(({ pagination, onPageChange }) => {
  const { currentPage, pageCount, hasNextPage, hasPreviousPage } = pagination;
  
  // Calculate page numbers to display (show 5 pages at most)
  const getPageNumbers = () => {
    const pages = [];
    let startPage = Math.max(1, currentPage - 2);
    let endPage = Math.min(pageCount, startPage + 4);
    
    // Adjust start page if end page is at maximum
    if (endPage === pageCount) {
      startPage = Math.max(1, endPage - 4);
    }
    
    for (let i = startPage; i <= endPage; i++) {
      pages.push(i);
    }
    return pages;
  };

  return (
    <div className="flex items-center justify-center mt-6 gap-1">
      {/* Previous page button */}
      <Button 
        variant="outline" 
        size="sm"
        disabled={!hasPreviousPage}
        onClick={() => onPageChange(currentPage - 1)}
        className="h-8 w-8 p-0"
      >
        <ChevronLeft className="h-4 w-4" />
        <span className="sr-only">Trang trước</span>
      </Button>
      
      {/* Page numbers */}
      {getPageNumbers().map(page => (
        <Button
          key={page}
          variant={page === currentPage ? "default" : "outline"}
          size="sm"
          onClick={() => onPageChange(page)}
          className={`h-8 w-8 p-0 ${page === currentPage ? 'bg-teal-500 hover:bg-teal-600' : ''}`}
        >
          {page}
        </Button>
      ))}
      
      {/* Next page button */}
      <Button 
        variant="outline" 
        size="sm"
        disabled={!hasNextPage}
        onClick={() => onPageChange(currentPage + 1)}
        className="h-8 w-8 p-0"
      >
        <ChevronRight className="h-4 w-4" />
        <span className="sr-only">Trang sau</span>
      </Button>
    </div>
  );
});

Pagination.displayName = "Pagination";

export default Pagination;