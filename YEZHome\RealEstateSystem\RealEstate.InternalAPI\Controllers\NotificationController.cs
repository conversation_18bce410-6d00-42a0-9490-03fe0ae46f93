﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using RealEstate.Application.DTO;
using RealEstate.Application.DTO.Notification;
using RealEstate.Application.Interfaces;
using RealEstate.Domain.Common;

namespace RealEstate.InternalAPI.Controllers
{
    [Route("api/notifications")]
    /// <summary>
    /// Controller for managing user notifications
    /// </summary>
    public class NotificationController : BaseController
    {
        private readonly INotificationService _notificationService;
        private readonly INotificationPreferenceService _notificationPreferenceService;
        private readonly ILogger<NotificationController> _logger;

        /// <summary>
        /// Initializes a new instance of the NotificationController
        /// </summary>
        /// <param name="notificationService">The notification service</param>
        /// <param name="notificationPreferenceService">The notification preference service</param>
        /// <param name="logger">The logger</param>
        public NotificationController(
            INotificationService notificationService,
            INotificationPreferenceService notificationPreferenceService,
            ILogger<NotificationController> logger)
        {
            _notificationService = notificationService;
            _notificationPreferenceService = notificationPreferenceService;
            _logger = logger;
        }

        /// <summary>
        /// Get notifications by type for a specific user with pagination and date filtering (Admin only)
        /// </summary>
        /// <param name="type">The notification type</param>
        /// <param name="userId">The user ID</param>
        /// <param name="fromDate">Optional start date filter</param>
        /// <param name="toDate">Optional end date filter</param>
        /// <param name="page">Page number (default: 1)</param>
        /// <param name="pageSize">Page size (default: 10)</param>
        /// <returns>Paged list of notifications of the specified type for the specified user</returns>
        /// <response code="200">Returns the notifications</response>
        /// <response code="400">If the notification type is invalid</response>
        /// <response code="403">If the user is not an admin</response>
        /// <response code="500">If there was an internal server error</response>
        [HttpGet("by-type/{type}/user/{userId}")]
        [Authorize(Policy = "AdminOnly")]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(PagedResultDto<NotificationDto>))]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<PagedResultDto<NotificationDto>>> GetByTypeAndUser(
            string type,
            Guid userId,
            [FromQuery] DateTime? fromDate = null,
            [FromQuery] DateTime? toDate = null,
            [FromQuery] int page = 1,
            [FromQuery] int pageSize = 10)
        {
            try
            {
                if (!FunctionHelper.IsValidNotificationType(type))
                {
                    return BadRequest("Invalid notification type");
                }

                var result = await _notificationService.GetNotificationsByTypeAndUserAsync(
                    type,
                    userId,
                    fromDate,
                    toDate,
                    page,
                    pageSize);

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving notifications of type {NotificationType} for user {UserId}", type, userId);
                return StatusCode(500, "An error occurred while retrieving notifications. Please try again later.");
            }
        } 

        /// <summary>
        /// Create a new notification
        /// </summary>
        /// <param name="createNotificationDto">The notification data</param>
        /// <returns>The created notification</returns>
        /// <response code="201">Returns the newly created notification</response>
        /// <response code="400">If the request data is invalid</response>
        /// <response code="403">If the user is not authorized to create notifications</response>
        /// <response code="500">If there was an internal server error</response>
        [HttpPost]
        [Authorize(Policy = "AdminOnly")]
        [ProducesResponseType(StatusCodes.Status201Created, Type = typeof(NotificationDto))]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<NotificationDto>> Create([FromBody] CreateNotificationDto createNotificationDto)
        {
            try
            {
                // Validate the input
                var validationResult = ValidateCreateNotificationRequest(createNotificationDto);
                if (validationResult != null)
                {
                    return validationResult;
                }

                // Create the notification
                var createdNotification = await _notificationService.CreateNotificationAsync(createNotificationDto);

                return CreatedAtAction(
                    nameof(Create),
                    new { id = createdNotification.Id },
                    createdNotification);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating notification");
                return StatusCode(500, "An error occurred while creating the notification. Please try again later.");
            }
        }

        /// <summary>
        /// Validates the create notification request
        /// </summary>
        /// <param name="createNotificationDto">The notification data to validate</param>
        /// <returns>BadRequest result if validation fails, null if validation passes</returns>
        private BadRequestObjectResult? ValidateCreateNotificationRequest(CreateNotificationDto createNotificationDto)
        {
            if (createNotificationDto == null)
                return BadRequest("Notification data is required");

            if (string.IsNullOrWhiteSpace(createNotificationDto.Type))
                return BadRequest("Notification type is required");

            if (string.IsNullOrWhiteSpace(createNotificationDto.Title))
                return BadRequest("Notification title is required");

            if (string.IsNullOrWhiteSpace(createNotificationDto.Message))
                return BadRequest("Notification message is required");

            if (!FunctionHelper.IsValidNotificationType(createNotificationDto.Type))
                return BadRequest("Invalid notification type. Valid types are: System, Transaction, Contact, Promotion, News, WalletUpdate, CustomerMessage");

            // Check if user has admin role for system-wide notification types
            if (RequiresAdminRole(createNotificationDto.Type) && !IsUserAdmin())
                return BadRequest("Only administrators can create System, Promotion, and News notifications");

            return null; // Validation passed
        }

        /// <summary>
        /// Checks if the current user has admin role
        /// </summary>
        /// <returns>True if user is admin, false otherwise</returns>
        private bool IsUserAdmin()
        {
            return User.IsInRole(EnumValues.UserType.Admin.ToString());
        }

        /// <summary>
        /// Checks if the notification type requires admin role
        /// </summary>
        /// <param name="notificationType">The notification type to check</param>
        /// <returns>True if admin role is required, false otherwise</returns>
        private bool RequiresAdminRole(string notificationType)
        {
            var adminRequiredTypes = new[]
            {
                EnumValues.NotificationType.System.ToString(),
                EnumValues.NotificationType.Promotion.ToString(),
                EnumValues.NotificationType.News.ToString()
            };

            return adminRequiredTypes.Contains(notificationType, StringComparer.OrdinalIgnoreCase);
        }
    }
}
