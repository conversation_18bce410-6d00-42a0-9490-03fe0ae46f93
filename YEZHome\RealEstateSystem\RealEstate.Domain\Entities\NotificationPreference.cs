﻿namespace RealEstate.Domain.Entities
{
    public class NotificationPreference : BaseEntity
    {
        public Guid UserId { get; set; }
        public bool ReceivePromotions { get; set; } = true;
        public bool ReceiveWalletUpdates { get; set; } = true;
        public bool ReceiveNews { get; set; } = true;
        public bool ReceiveCustomerMessages { get; set; } = true;
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        // Navigation Property
        public AppUser User { get; set; } = null!;
    }

}
