import { useEffect, useState, useCallback } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON>Title, Di<PERSON>Footer } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { ScrollArea } from "@/components/ui/scroll-area";
import { getContactRequestsByPropertyId, updateContactRequest } from "@/app/actions/server/contactRequest";
import { useToast } from "@/hooks/use-toast";
import { Badge } from "@/components/ui/badge";
import { format } from "date-fns";
import { CheckCircle2 } from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";
import { cn } from "@/lib/utils";
import { useTranslations } from "next-intl";
export default function ContactRequestModal({ propertyId, open, onClose }) {
  const { toast } = useToast();
  const [contactRequests, setContactRequests] = useState([]);
  const [selectedRequests, setSelectedRequests] = useState([]);
  const [loading, setLoading] = useState(false);
  const t = useTranslations("ContactRequestModal");
  useEffect(() => {
    if (open && propertyId) {
      loadContactRequests();
    } else {
      setSelectedRequests([]);
    }
  }, [open, propertyId]);

  const loadContactRequests = async () => {
    setLoading(true);
    try {
      const response = await getContactRequestsByPropertyId(propertyId);
      if (response.success) {
        setContactRequests(response.data);
      } else {
        toast({
          title: t("error"),
          description: response.message || t("cannotLoadContactRequestList"),
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error(t("errorLoadingContactRequests"), error);
    } finally {
      setLoading(false);
    }
  };

  const handleSelectAll = useCallback(
    (checked) => {
      if (checked) {
        setSelectedRequests(contactRequests.map((request) => request.id));
      } else {
        setSelectedRequests([]);
      }
    },
    [contactRequests]
  );

  const handleSelectRequest = useCallback((requestId, checked) => {
    setSelectedRequests((prev) => {
      if (checked) {
        return [...prev, requestId];
      } else {
        return prev.filter((id) => id !== requestId);
      }
    });
  }, []);

  const handleMarkAsRead = async (requestIds) => {
    setLoading(true);
    try {
      const formData = new FormData();
      const id = Array.isArray(requestIds) ? requestIds[0] : requestIds;
      formData.append("id", id);
      formData.append("status", "read");
      formData.append("note", "Đã đọc");

      const response = await updateContactRequest(null, formData);
      if (response.success) {
        setContactRequests((prevRequests) =>
          prevRequests.map((request) => (request.id === id ? { ...request, status: "read", note: "Đã đọc" } : request))
        );

        toast({
          title: "Thành công",
          description: "Đã đánh dấu đã đọc",
          className: "bg-teal-600 text-white",
        });
        setSelectedRequests((prev) => prev.filter((selectedId) => selectedId !== id));
      } else {
        toast({
          title: "Lỗi",
          description: response.message || "Không thể cập nhật trạng thái",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error(t("errorMarkingAsRead"), error);
    } finally {
      setLoading(false);
    }
  };

  const handleBulkMarkAsRead = async () => {
    if (selectedRequests.length === 0) {
      toast({
        title: t("notification"),
        description: t("pleaseSelectAtLeastOneRequest"),
        variant: "default",
      });
      return;
    }

    setLoading(true);
    try {
      const results = await Promise.all(
        selectedRequests.map(async (id) => {
          const formData = new FormData();
          formData.append("id", id);
          formData.append("status", "read");
          formData.append("note", t("read"));
          return await updateContactRequest(null, formData);
        })
      );

      const allSuccessful = results.every((result) => result.success);

      if (allSuccessful) {
        setContactRequests((prevRequests) =>
          prevRequests.map((request) => (selectedRequests.includes(request.id) ? { ...request, status: "read", note: t("read") } : request))
        );

        toast({
          title: t("success"),
          description: t("allRequestsMarkedAsRead"),
          className: "bg-teal-600 text-white",
        });
        setSelectedRequests([]);
      } else {
        toast({
          title: t("error"),
          description: t("cannotUpdateSomeRequests"),
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error(t("errorInBulkMarkAsRead"), error);
      toast({
        title: t("error"),
        description: t("errorOccurredWhenUpdating"),
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const isAllSelected = contactRequests.length > 0 && selectedRequests.length === contactRequests.length;

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-w-[800px] max-h-[85vh] overflow-y-auto p-6 md:p-8" onPointerDownOutside={(e) => e.preventDefault()}>
        <DialogHeader>
          <DialogTitle>{t("contactRequestList")}</DialogTitle>
        </DialogHeader>

        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-2">
            <Checkbox id="selectAll" checked={isAllSelected} onCheckedChange={handleSelectAll} disabled={loading || contactRequests.length === 0} />
            <label htmlFor="selectAll" className="text-sm font-medium">
              {t("selectAll")}
            </label>
          </div>
          <Button variant="outline" size="sm" className="ml-auto" onClick={handleBulkMarkAsRead} disabled={loading || selectedRequests.length === 0}>
            <CheckCircle2 className="h-4 w-4 mr-2" />
            {t("markAsRead")}
          </Button>
        </div>

        {loading ? (
          <div className="text-center text-gray-500 py-8">{t("loading")}</div>
        ) : (
          <div className="p-4 space-y-2">
            {contactRequests.map((request) => (
              <Card key={request.id} className={cn("transition-colors", request.status === "read" ? "bg-gray-50" : "")}>
                <CardContent className="p-4">
                  <div className="flex items-start gap-4">
                    <Checkbox
                      checked={selectedRequests.includes(request.id)}
                      onCheckedChange={(checked) => handleSelectRequest(request.id, checked)}
                      disabled={loading}
                    />
                    <div className="flex-1 space-y-1">
                      <div className="flex items-start justify-between">
                        <div>
                          <h5 className={cn("font-semibold text-base", request.status === "read" && "text-gray-500")}>{request.name}</h5>
                          <div className="text-sm text-gray-700 space-y-1 mt-1">
                            <p>
                              <Mail className="h-4 w-4" /> {request.email}
                            </p>
                            <p>
                              <Phone className="h-4 w-4" /> {request.phone}
                            </p>
                          </div>
                        </div>
                        <div className="flex flex-col items-end gap-2">
                          <Badge rounded="full" variant={request.status === "read" ? "secondary" : "default"}>
                            {request.status === "read" ? t("read") : t("unread")}
                          </Badge>
                          {request.status !== "read" && (
                            <Button variant="ghost" size="sm" onClick={() => handleMarkAsRead(request.id)} disabled={loading} className="shrink-0">
                              <CheckCircle2 className="h-4 w-4" />
                            </Button>
                          )}
                        </div>
                      </div>
                      {request.note && <p className={cn("text-sm", request.status === "read" && "text-gray-500")}>{request.note}</p>}
                      <p className="text-xs text-gray-400">{format(new Date(request.sentAt), "dd/MM/yyyy HH:mm")}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
            {contactRequests.length === 0 && <div className="text-center text-gray-500 py-8">{t("noContactRequest")}</div>}
          </div>
        )}

        <DialogFooter>
          <Button variant="outline" onClick={onClose}>
            {t("close")}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
