{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/app/actions/server/property.jsx"], "sourcesContent": ["\"use server\";\r\n\r\nimport { handleErrorResponse, logError } from \"@/lib/apiUtils\";\r\nimport { fetchWithAuth, getSession, fetchWithoutAuth } from \"@/lib/sessionUtils\";\r\nimport { parseEmptyStringsToNull } from \"@/lib/utils\";\r\n\r\nconst API_BASE_URL = `${process.env.API_URL}/api/Property`;\r\nconst PROPERTY_ANALYTICS_API_BASE_URL = `${process.env.API_URL}/api/PropertyAnalytics`;\r\n\r\n// New function to search properties with filters\r\nexport async function searchProperties(filterCriteria) {\r\n  try {\r\n    // Build query parameters from filter criteria\r\n    const queryParams = new URLSearchParams();\r\n\r\n    // Transaction Type (postType)\r\n    if (filterCriteria.transactionType && filterCriteria.transactionType.length > 0) {\r\n      filterCriteria.transactionType.forEach(type => {\r\n        queryParams.append('postType', type);\r\n      });\r\n    }\r\n\r\n    // Property Type\r\n    if (filterCriteria.propertyType && filterCriteria.propertyType.length > 0) {\r\n      filterCriteria.propertyType.forEach(type => {\r\n        queryParams.append('propertyType', type);\r\n      });\r\n    }\r\n\r\n    // Location\r\n    if (filterCriteria.location) {\r\n      if (filterCriteria.location.province) {\r\n        queryParams.append('cityId', filterCriteria.location.province);\r\n      }\r\n      if (filterCriteria.location.district) {\r\n        queryParams.append('districtId', filterCriteria.location.district);\r\n      }\r\n      if (filterCriteria.location.address) {\r\n        queryParams.append('address', filterCriteria.location.address);\r\n      }\r\n    }\r\n\r\n    // Price Range\r\n    if (filterCriteria.priceRange) {\r\n      if (filterCriteria.priceRange.min) {\r\n        queryParams.append('minPrice', filterCriteria.priceRange.min);\r\n      }\r\n      if (filterCriteria.priceRange.max) {\r\n        queryParams.append('maxPrice', filterCriteria.priceRange.max);\r\n      }\r\n    }\r\n\r\n    // Area Range\r\n    if (filterCriteria.areaRange) {\r\n      if (filterCriteria.areaRange.min) {\r\n        queryParams.append('minArea', filterCriteria.areaRange.min);\r\n      }\r\n      if (filterCriteria.areaRange.max) {\r\n        queryParams.append('maxArea', filterCriteria.areaRange.max);\r\n      }\r\n    }\r\n\r\n    // Bedrooms\r\n    if (filterCriteria.bedrooms) {\r\n      queryParams.append('minRooms', filterCriteria.bedrooms);\r\n    }\r\n\r\n    // Bathrooms\r\n    if (filterCriteria.bathrooms) {\r\n      queryParams.append('minToilets', filterCriteria.bathrooms);\r\n    }\r\n\r\n    // Direction\r\n    if (filterCriteria.direction) {\r\n      queryParams.append('direction', filterCriteria.direction);\r\n    }\r\n\r\n    // Legal Status\r\n    if (filterCriteria.legalStatus) {\r\n      queryParams.append('legality', filterCriteria.legalStatus);\r\n    }\r\n\r\n    // Road Width\r\n    if (filterCriteria.roadWidth) {\r\n      queryParams.append('minRoadWidth', filterCriteria.roadWidth);\r\n    }\r\n\r\n    // User location for proximity search\r\n    if (filterCriteria.sw_lat && filterCriteria.sw_lng && filterCriteria.ne_lat && filterCriteria.ne_lng) {\r\n      queryParams.append('swLat', filterCriteria.sw_lat);\r\n      queryParams.append('swLng', filterCriteria.sw_lng);\r\n      queryParams.append('neLat', filterCriteria.ne_lat);\r\n      queryParams.append('neLng', filterCriteria.ne_lng);\r\n\r\n    }\r\n\r\n    // Build the URL with query parameters\r\n    const url = `${API_BASE_URL}/search?${queryParams.toString()}`;\r\n\r\n    // Use fetchWithoutAuth for public API call\r\n    const response = await fetchWithoutAuth(url, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n\r\n    return response; // fetchWithoutAuth already returns the standard response format\r\n\r\n  } catch (error) {\r\n    // Handle errors outside fetchWithoutAuth\r\n    console.error(`Lỗi khi gọi fetchWithoutAuth cho searchProperties:`, error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi không mong muốn khi tìm kiếm bất động sản\",\r\n      errorType: \"internal_error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function getPropertyById(propertyId) {\r\n  const url = `${API_BASE_URL}/${propertyId}`;\r\n  try {\r\n    // Use fetchWithoutAuth for public API call\r\n    const response = await fetchWithoutAuth(url, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n\r\n    return response; // fetchWithoutAuth already returns the standard { success, data, message, errorType } object\r\n\r\n  } catch (error) {\r\n    // This catch block might handle errors occurring outside the fetchWithoutAuth call itself\r\n    console.error(`Lỗi khi gọi fetchWithoutAuth cho ${url}:`, error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi không mong muốn khi lấy thông tin bất động sản\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function updatePropertyById(prevState, formData) {\r\n  try {\r\n    // Get token from formData\r\n    const propertyId = formData.get(\"propertyId\");\r\n\r\n    // Convert FormData to a plain object for easier handling\r\n    const formDataObject = Object.fromEntries(formData.entries());\r\n    const payload = {\r\n      ...formDataObject,\r\n    };\r\n\r\n    const userSession = await getSession(\"UserProfile\");\r\n    if (userSession) {\r\n      const user = JSON.parse(userSession);\r\n      payload.ownerId = user.id;\r\n    }\r\n\r\n    // ✅ Parse the uploadedFiles JSON string back into an array\r\n    if (formDataObject.UploadedFiles) {\r\n      payload.UploadedFiles = JSON.parse(formDataObject.UploadedFiles);\r\n    }\r\n\r\n    payload.isHighlighted = formDataObject.isHighlighted === \"true\" ? true : false;\r\n    payload.isAutoRenew = formDataObject.isAutoRenew === \"true\" ? true : false;\r\n\r\n    return await fetchWithAuth(`${API_BASE_URL}/${propertyId}`, {\r\n      method: \"PUT\",\r\n      body: JSON.stringify(parseEmptyStringsToNull(payload)),\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n        \"accept\": \"*/*\"\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"PropertyService\", error, {\r\n      action: \"createProperty\",\r\n      formData,\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi tạo bất động sản\");\r\n  }\r\n}\r\n\r\nexport async function deletePropertyById(propertyId) {\r\n  try {\r\n    return await fetchWithAuth(`${API_BASE_URL}/${propertyId}`, {\r\n      method: \"DELETE\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    console.error(\"Lỗi khi xóa bất động sản:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi xóa bất động sản\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function getAllProperties() {\r\n  try {\r\n    // Use fetchWithoutAuth for public API call\r\n    const response = await fetchWithoutAuth(API_BASE_URL, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n\r\n    return response; // fetchWithoutAuth already returns the standard response format\r\n\r\n  } catch (error) {\r\n    // Handle errors outside fetchWithoutAuth\r\n    console.error(`Lỗi khi gọi fetchWithoutAuth cho getAllProperties:`, error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi không mong muốn khi lấy danh sách bất động sản\",\r\n      errorType: \"internal_error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function createProperty(prevState, formData) {\r\n  try {\r\n    // Convert FormData to a plain object for easier handling\r\n    const formDataObject = Object.fromEntries(formData.entries());\r\n\r\n    const payload = {\r\n      ...formDataObject,\r\n    };\r\n\r\n    const userSession = await getSession(\"UserProfile\");\r\n    if (userSession) {\r\n      const user = JSON.parse(userSession);\r\n      payload.ownerId = user.id;\r\n    }\r\n\r\n    // ✅ Parse the uploadedFiles JSON string back into an array\r\n    if (formDataObject.UploadedFiles) {\r\n      payload.UploadedFiles = JSON.parse(formDataObject.UploadedFiles);\r\n    }\r\n\r\n    payload.isHighlighted = formDataObject.isHighlighted === \"true\" ? true : false;\r\n    payload.isAutoRenew = formDataObject.isAutoRenew === \"true\" ? true : false;\r\n\r\n    return await fetchWithAuth(API_BASE_URL, {\r\n      method: \"POST\",\r\n      body: JSON.stringify(parseEmptyStringsToNull(payload)),\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"PropertyService\", error, {\r\n      action: \"createProperty\",\r\n      formData,\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi tạo bất động sản\");\r\n  }\r\n}\r\n\r\nexport async function updatePropertyStatus(formData) {\r\n  try {\r\n    const propertyId = formData.get(\"propertyId\");\r\n\r\n    return await fetchWithAuth(`${API_BASE_URL}/${propertyId}/status`, {\r\n      method: \"PUT\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      body: JSON.stringify({\r\n        status: formData.get(\"status\"),\r\n        comment: formData.get(\"comment\") || \"\"\r\n      }),\r\n    });\r\n  } catch (error) {\r\n    logError(\"PropertyService\", error, {\r\n      action: \"updatePropertyStatus\",\r\n      formData,\r\n    });\r\n    return handleErrorResponse(false, null, \"Không thể cập nhật trạng thái bất động sản\");\r\n  }\r\n}\r\n\r\nexport async function getPropertyByUser(status, page = 1, pageSize = 10) {\r\n  try {\r\n    // If status is 'counts', call the stats endpoint instead\r\n    if (status === 'counts') {\r\n      return await getPropertyStats();\r\n    }\r\n\r\n    let url = `${API_BASE_URL}/me`;\r\n    const queryParams = new URLSearchParams();\r\n\r\n    // Add parameters if provided\r\n    if (status) {\r\n      queryParams.append('status', status);\r\n    }\r\n\r\n    queryParams.append('page', page);\r\n    queryParams.append('pageSize', pageSize);\r\n\r\n    // Append query parameters to URL if any exist\r\n    if (queryParams.toString()) {\r\n      url += `?${queryParams.toString()}`;\r\n    }\r\n\r\n    const response = await fetchWithAuth(url, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n\r\n    return response;\r\n  } catch (error) {\r\n    console.error(\"Lỗi khi lấy thông tin bất động sản của người dùng:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi lấy thông tin bất động sản của người dùng\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Get property statistics by status\r\n * @returns {Promise<Object>} Response with property stats data\r\n */\r\nexport async function getPropertyStats() {\r\n  try {\r\n    const response = await fetchWithAuth(`${API_BASE_URL}/stats`, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n\r\n    if (response.success) {\r\n      // Transform the response to match the expected format for the UI\r\n      const statsData = response.data;\r\n      return {\r\n        success: true,\r\n        data: {\r\n          total: statsData.totalProperties || 0,\r\n          approved: statsData.propertiesByStatus?.Approved || 0,\r\n          pendingApproval: statsData.propertiesByStatus?.PendingApproval || 0,\r\n          rejectedByAdmin: statsData.propertiesByStatus?.RejectedByAdmin || 0,\r\n          rejectedDueToUnpaid: statsData.propertiesByStatus?.RejectedDueToUnpaid || 0,\r\n          waitingPayment: statsData.propertiesByStatus?.WaitingPayment || 0,\r\n          expired: statsData.propertiesByStatus?.Expired || 0,\r\n          draft: statsData.propertiesByStatus?.Draft || 0,\r\n          sold: statsData.propertiesByStatus?.Sold || 0,\r\n        }\r\n      };\r\n    }\r\n\r\n    return response;\r\n  } catch (error) {\r\n    console.error(\"Lỗi khi lấy thống kê bất động sản:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi lấy thống kê bất động sản\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function uploadPropertyImages(prevState, formData) {\r\n  try {\r\n    const files = formData.getAll(\"files\"); // Get all files from FormData\r\n    const propertyId = formData.get(\"propertyId\");\r\n\r\n    if (files.length === 0) {\r\n      return handleErrorResponse(false, null, \"Thiếu tập tin\");\r\n    }\r\n\r\n    const formDataToSend = new FormData();\r\n    files.forEach((file) => formDataToSend.append(\"files\", file));\r\n    formDataToSend.append(\"propertyId\", propertyId);\r\n\r\n    return await fetchWithAuth(`${API_BASE_URL}/upload-images`, {\r\n      method: \"POST\",\r\n      body: formDataToSend,\r\n    });\r\n  } catch (error) {\r\n    logError(\"PropertyService\", error, { action: \"uploadPropertyImages\", formData });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi tải lên hình ảnh\");\r\n  }\r\n}\r\n\r\nexport async function verifyPropertyRemainingTimes(propertyId) {\r\n  try {\r\n    return await fetchWithAuth(`${API_BASE_URL}/edit-remaining/${propertyId}`, {\r\n      method: \"GET\",\r\n    });\r\n  } catch (error) {\r\n    console.error(\"Error checking remaining verification times:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi kiểm tra số lần xác thực.\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function getPropertyStatusHistory(propertyId) {\r\n  try {\r\n    return await fetchWithAuth(`${PROPERTY_ANALYTICS_API_BASE_URL}/history/status/${propertyId}`, {\r\n      method: \"GET\",\r\n    });\r\n  } catch (error) {\r\n    console.error(\"Error fetching property history:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi lấy lịch sử hoạt động\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function getNearbyProperties(latitude, longitude, radius = 5000) {\r\n  try {\r\n    // Use fetchWithoutAuth for public API call\r\n    return await fetchWithoutAuth(\r\n      `${API_BASE_URL}/nearby?latitude=${latitude}&longitude=${longitude}&radius=${radius}`, {\r\n        method: \"GET\",\r\n      }\r\n    );\r\n  } catch (error) {\r\n    console.error(\"Error fetching nearby properties:\", error);\r\n    return {\r\n        success: false,\r\n        message: \"Đã xảy ra lỗi khi tìm kiếm bất động sản lân cận\",\r\n        errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\n// New function to get property report data (Placeholder)\r\nexport async function getPropertyReportById(propertyId) {\r\n  \"use server\"; // Ensure this runs on the server\r\n\r\n  // Simulate API call delay\r\n  await new Promise(resolve => setTimeout(resolve, 750));\r\n\r\n  try {\r\n    // --- FAKE DATA ---\r\n    // In a real scenario, you would fetch this from your API:\r\n    // const response = await fetchWithAuth(`${API_BASE_URL}/report/${propertyId}`);\r\n    // if (!response.success) return response;\r\n    // const reportData = response.data;\r\n\r\n    // For now, generate some fake data:\r\n    const fakeReportData = {\r\n      views: 30 + Math.floor(Math.random() * 100),           // Lượt xem\r\n      impressions: 33 + Math.floor(Math.random() * 150),    // Lượt hiển thị\r\n      cartAdds: 5 + Math.floor(Math.random() * 40),         // Lượt bỏ vào giỏ hàng (Example)\r\n      contactRequests: 10 + Math.floor(Math.random() * 20), // Lượt liên hệ (Example - added based on previous comment)\r\n      highlightsCount: 1 + Math.floor(Math.random() * 15),  // Số lần highlight\r\n      renewalsCount: 0 + Math.floor(Math.random() * 5),     // Số lần gia hạn\r\n      postCost: 55000,                                      // Tiền bài đăng\r\n      highlightCost: (1 + Math.floor(Math.random() * 15)) * 40000, // Tổng tiền highlight\r\n      renewalCost: (0 + Math.floor(Math.random() * 5)) * 300000, // Tổng tiền gia hạn\r\n    };\r\n    fakeReportData.totalCost = fakeReportData.postCost + fakeReportData.highlightCost + fakeReportData.renewalCost;\r\n\r\n    return {\r\n      success: true,\r\n      data: fakeReportData,\r\n    };\r\n\r\n  } catch (error) {\r\n    console.error(`Error fetching report for propertyId ${propertyId}:`, error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi lấy dữ liệu báo cáo.\",\r\n      errorType: \"internal_error\",\r\n    };\r\n  }\r\n}\r\n\r\n// Update property media caption\r\nexport async function updatePropertyMediaCaption(mediaId, caption) {\r\n  try {\r\n    const payload = {\r\n      id: mediaId,\r\n      caption: caption\r\n    };\r\n\r\n    return await fetchWithAuth(`${process.env.API_URL}/Media/update-caption`, {\r\n      method: \"PUT\",\r\n      body: JSON.stringify(payload),\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"PropertyService\", error, {\r\n      action: \"updatePropertyMediaCaption\",\r\n      mediaId,\r\n      caption,\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi cập nhật chú thích hình ảnh\");\r\n  }\r\n}\r\n\r\n// Update property media isAvatar status\r\nexport async function updatePropertyMediaIsAvatar(mediaId, isAvatar) {\r\n  try {\r\n    const payload = {\r\n      id: mediaId,\r\n      isAvatar: isAvatar\r\n    };\r\n\r\n    return await fetchWithAuth(`${process.env.API_URL}/Media/update-is-avatar`, {\r\n      method: \"PUT\",\r\n      body: JSON.stringify(payload),\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"PropertyService\", error, {\r\n      action: \"updatePropertyMediaIsAvatar\",\r\n      mediaId,\r\n      isAvatar,\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi cập nhật ảnh đại diện\");\r\n  }\r\n}\r\n\r\n// Delete property media\r\nexport async function deletePropertyMedia(mediaId) {\r\n  try {\r\n    return await fetchWithAuth(`${process.env.API_URL}/Media/${mediaId}`, {\r\n      method: \"DELETE\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"PropertyService\", error, {\r\n      action: \"deletePropertyMedia\",\r\n      mediaId,\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi xóa hình ảnh\");\r\n  }\r\n}\r\n\r\n/**\r\n * Bulk delete multiple properties\r\n * @param {Array<string>} propertyIds - Array of property IDs to delete\r\n * @returns {Promise<Object>} Response with success/error information\r\n */\r\nexport async function bulkDeleteProperties(propertyIds) {\r\n  try {\r\n    return await fetchWithAuth(`${API_BASE_URL}/bulk`, {\r\n      method: \"DELETE\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      body: JSON.stringify({\r\n        propertyIds: propertyIds\r\n      }),\r\n    });\r\n  } catch (error) {\r\n    console.error(\"Lỗi khi xóa nhiều bất động sản:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi xóa nhiều bất động sản\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Bulk update status for multiple properties\r\n * @param {Array<string>} propertyIds - Array of property IDs to update\r\n * @param {string} status - New status to set\r\n * @param {string} comment - Optional comment for the status update\r\n * @returns {Promise<Object>} Response with success/error information\r\n */\r\nexport async function bulkUpdatePropertyStatus(propertyIds, status, comment = \"\") {\r\n  try {\r\n    return await fetchWithAuth(`${API_BASE_URL}/bulk/status`, {\r\n      method: \"PUT\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      body: JSON.stringify({\r\n        propertyIds: propertyIds,\r\n        status: status,\r\n        comment: comment\r\n      }),\r\n    });\r\n  } catch (error) {\r\n    console.error(\"Lỗi khi cập nhật trạng thái nhiều bất động sản:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi cập nhật trạng thái nhiều bất động sản\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Update highlight status for a property\r\n * @param {string} propertyId - ID of the property to update\r\n * @param {boolean} isHighlighted - Whether the property should be highlighted\r\n * @returns {Promise<Object>} Response with success/error information\r\n */\r\nexport async function updatePropertyHighlight(propertyId, isHighlighted) {\r\n  try {\r\n    return await fetchWithAuth(`${API_BASE_URL}/${propertyId}/highlight`, {\r\n      method: \"PUT\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      body: JSON.stringify({\r\n        isHighlighted: isHighlighted\r\n      }),\r\n    });\r\n  } catch (error) {\r\n    console.error(\"Lỗi khi cập nhật trạng thái nổi bật của bất động sản:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi cập nhật trạng thái nổi bật của bất động sản\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Bulk update highlight status for multiple properties\r\n * @param {Array<string>} propertyIds - Array of property IDs to update\r\n * @param {boolean} isHighlighted - Whether the properties should be highlighted\r\n * @returns {Promise<Object>} Response with success/error information\r\n */\r\nexport async function bulkUpdatePropertyHighlight(propertyIds, isHighlighted) {\r\n  try {\r\n    return await fetchWithAuth(`${API_BASE_URL}/bulk/highlight`, {\r\n      method: \"PUT\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      body: JSON.stringify({\r\n        propertyIds: propertyIds,\r\n        isHighlighted: isHighlighted\r\n      }),\r\n    });\r\n  } catch (error) {\r\n    console.error(\"Lỗi khi cập nhật trạng thái nổi bật của nhiều bất động sản:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi cập nhật trạng thái nổi bật của nhiều bất động sản\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;;IA0LsB,qBAAA,WAAA,GAAA,CAAA,GAAA,sNAAA,CAAA,wBAAA,EAAA,8CAAA,sNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,sNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 20, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/app/actions/server/property.jsx"], "sourcesContent": ["\"use server\";\r\n\r\nimport { handleErrorResponse, logError } from \"@/lib/apiUtils\";\r\nimport { fetchWithAuth, getSession, fetchWithoutAuth } from \"@/lib/sessionUtils\";\r\nimport { parseEmptyStringsToNull } from \"@/lib/utils\";\r\n\r\nconst API_BASE_URL = `${process.env.API_URL}/api/Property`;\r\nconst PROPERTY_ANALYTICS_API_BASE_URL = `${process.env.API_URL}/api/PropertyAnalytics`;\r\n\r\n// New function to search properties with filters\r\nexport async function searchProperties(filterCriteria) {\r\n  try {\r\n    // Build query parameters from filter criteria\r\n    const queryParams = new URLSearchParams();\r\n\r\n    // Transaction Type (postType)\r\n    if (filterCriteria.transactionType && filterCriteria.transactionType.length > 0) {\r\n      filterCriteria.transactionType.forEach(type => {\r\n        queryParams.append('postType', type);\r\n      });\r\n    }\r\n\r\n    // Property Type\r\n    if (filterCriteria.propertyType && filterCriteria.propertyType.length > 0) {\r\n      filterCriteria.propertyType.forEach(type => {\r\n        queryParams.append('propertyType', type);\r\n      });\r\n    }\r\n\r\n    // Location\r\n    if (filterCriteria.location) {\r\n      if (filterCriteria.location.province) {\r\n        queryParams.append('cityId', filterCriteria.location.province);\r\n      }\r\n      if (filterCriteria.location.district) {\r\n        queryParams.append('districtId', filterCriteria.location.district);\r\n      }\r\n      if (filterCriteria.location.address) {\r\n        queryParams.append('address', filterCriteria.location.address);\r\n      }\r\n    }\r\n\r\n    // Price Range\r\n    if (filterCriteria.priceRange) {\r\n      if (filterCriteria.priceRange.min) {\r\n        queryParams.append('minPrice', filterCriteria.priceRange.min);\r\n      }\r\n      if (filterCriteria.priceRange.max) {\r\n        queryParams.append('maxPrice', filterCriteria.priceRange.max);\r\n      }\r\n    }\r\n\r\n    // Area Range\r\n    if (filterCriteria.areaRange) {\r\n      if (filterCriteria.areaRange.min) {\r\n        queryParams.append('minArea', filterCriteria.areaRange.min);\r\n      }\r\n      if (filterCriteria.areaRange.max) {\r\n        queryParams.append('maxArea', filterCriteria.areaRange.max);\r\n      }\r\n    }\r\n\r\n    // Bedrooms\r\n    if (filterCriteria.bedrooms) {\r\n      queryParams.append('minRooms', filterCriteria.bedrooms);\r\n    }\r\n\r\n    // Bathrooms\r\n    if (filterCriteria.bathrooms) {\r\n      queryParams.append('minToilets', filterCriteria.bathrooms);\r\n    }\r\n\r\n    // Direction\r\n    if (filterCriteria.direction) {\r\n      queryParams.append('direction', filterCriteria.direction);\r\n    }\r\n\r\n    // Legal Status\r\n    if (filterCriteria.legalStatus) {\r\n      queryParams.append('legality', filterCriteria.legalStatus);\r\n    }\r\n\r\n    // Road Width\r\n    if (filterCriteria.roadWidth) {\r\n      queryParams.append('minRoadWidth', filterCriteria.roadWidth);\r\n    }\r\n\r\n    // User location for proximity search\r\n    if (filterCriteria.sw_lat && filterCriteria.sw_lng && filterCriteria.ne_lat && filterCriteria.ne_lng) {\r\n      queryParams.append('swLat', filterCriteria.sw_lat);\r\n      queryParams.append('swLng', filterCriteria.sw_lng);\r\n      queryParams.append('neLat', filterCriteria.ne_lat);\r\n      queryParams.append('neLng', filterCriteria.ne_lng);\r\n\r\n    }\r\n\r\n    // Build the URL with query parameters\r\n    const url = `${API_BASE_URL}/search?${queryParams.toString()}`;\r\n\r\n    // Use fetchWithoutAuth for public API call\r\n    const response = await fetchWithoutAuth(url, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n\r\n    return response; // fetchWithoutAuth already returns the standard response format\r\n\r\n  } catch (error) {\r\n    // Handle errors outside fetchWithoutAuth\r\n    console.error(`Lỗi khi gọi fetchWithoutAuth cho searchProperties:`, error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi không mong muốn khi tìm kiếm bất động sản\",\r\n      errorType: \"internal_error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function getPropertyById(propertyId) {\r\n  const url = `${API_BASE_URL}/${propertyId}`;\r\n  try {\r\n    // Use fetchWithoutAuth for public API call\r\n    const response = await fetchWithoutAuth(url, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n\r\n    return response; // fetchWithoutAuth already returns the standard { success, data, message, errorType } object\r\n\r\n  } catch (error) {\r\n    // This catch block might handle errors occurring outside the fetchWithoutAuth call itself\r\n    console.error(`Lỗi khi gọi fetchWithoutAuth cho ${url}:`, error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi không mong muốn khi lấy thông tin bất động sản\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function updatePropertyById(prevState, formData) {\r\n  try {\r\n    // Get token from formData\r\n    const propertyId = formData.get(\"propertyId\");\r\n\r\n    // Convert FormData to a plain object for easier handling\r\n    const formDataObject = Object.fromEntries(formData.entries());\r\n    const payload = {\r\n      ...formDataObject,\r\n    };\r\n\r\n    const userSession = await getSession(\"UserProfile\");\r\n    if (userSession) {\r\n      const user = JSON.parse(userSession);\r\n      payload.ownerId = user.id;\r\n    }\r\n\r\n    // ✅ Parse the uploadedFiles JSON string back into an array\r\n    if (formDataObject.UploadedFiles) {\r\n      payload.UploadedFiles = JSON.parse(formDataObject.UploadedFiles);\r\n    }\r\n\r\n    payload.isHighlighted = formDataObject.isHighlighted === \"true\" ? true : false;\r\n    payload.isAutoRenew = formDataObject.isAutoRenew === \"true\" ? true : false;\r\n\r\n    return await fetchWithAuth(`${API_BASE_URL}/${propertyId}`, {\r\n      method: \"PUT\",\r\n      body: JSON.stringify(parseEmptyStringsToNull(payload)),\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n        \"accept\": \"*/*\"\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"PropertyService\", error, {\r\n      action: \"createProperty\",\r\n      formData,\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi tạo bất động sản\");\r\n  }\r\n}\r\n\r\nexport async function deletePropertyById(propertyId) {\r\n  try {\r\n    return await fetchWithAuth(`${API_BASE_URL}/${propertyId}`, {\r\n      method: \"DELETE\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    console.error(\"Lỗi khi xóa bất động sản:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi xóa bất động sản\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function getAllProperties() {\r\n  try {\r\n    // Use fetchWithoutAuth for public API call\r\n    const response = await fetchWithoutAuth(API_BASE_URL, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n\r\n    return response; // fetchWithoutAuth already returns the standard response format\r\n\r\n  } catch (error) {\r\n    // Handle errors outside fetchWithoutAuth\r\n    console.error(`Lỗi khi gọi fetchWithoutAuth cho getAllProperties:`, error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi không mong muốn khi lấy danh sách bất động sản\",\r\n      errorType: \"internal_error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function createProperty(prevState, formData) {\r\n  try {\r\n    // Convert FormData to a plain object for easier handling\r\n    const formDataObject = Object.fromEntries(formData.entries());\r\n\r\n    const payload = {\r\n      ...formDataObject,\r\n    };\r\n\r\n    const userSession = await getSession(\"UserProfile\");\r\n    if (userSession) {\r\n      const user = JSON.parse(userSession);\r\n      payload.ownerId = user.id;\r\n    }\r\n\r\n    // ✅ Parse the uploadedFiles JSON string back into an array\r\n    if (formDataObject.UploadedFiles) {\r\n      payload.UploadedFiles = JSON.parse(formDataObject.UploadedFiles);\r\n    }\r\n\r\n    payload.isHighlighted = formDataObject.isHighlighted === \"true\" ? true : false;\r\n    payload.isAutoRenew = formDataObject.isAutoRenew === \"true\" ? true : false;\r\n\r\n    return await fetchWithAuth(API_BASE_URL, {\r\n      method: \"POST\",\r\n      body: JSON.stringify(parseEmptyStringsToNull(payload)),\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"PropertyService\", error, {\r\n      action: \"createProperty\",\r\n      formData,\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi tạo bất động sản\");\r\n  }\r\n}\r\n\r\nexport async function updatePropertyStatus(formData) {\r\n  try {\r\n    const propertyId = formData.get(\"propertyId\");\r\n\r\n    return await fetchWithAuth(`${API_BASE_URL}/${propertyId}/status`, {\r\n      method: \"PUT\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      body: JSON.stringify({\r\n        status: formData.get(\"status\"),\r\n        comment: formData.get(\"comment\") || \"\"\r\n      }),\r\n    });\r\n  } catch (error) {\r\n    logError(\"PropertyService\", error, {\r\n      action: \"updatePropertyStatus\",\r\n      formData,\r\n    });\r\n    return handleErrorResponse(false, null, \"Không thể cập nhật trạng thái bất động sản\");\r\n  }\r\n}\r\n\r\nexport async function getPropertyByUser(status, page = 1, pageSize = 10) {\r\n  try {\r\n    // If status is 'counts', call the stats endpoint instead\r\n    if (status === 'counts') {\r\n      return await getPropertyStats();\r\n    }\r\n\r\n    let url = `${API_BASE_URL}/me`;\r\n    const queryParams = new URLSearchParams();\r\n\r\n    // Add parameters if provided\r\n    if (status) {\r\n      queryParams.append('status', status);\r\n    }\r\n\r\n    queryParams.append('page', page);\r\n    queryParams.append('pageSize', pageSize);\r\n\r\n    // Append query parameters to URL if any exist\r\n    if (queryParams.toString()) {\r\n      url += `?${queryParams.toString()}`;\r\n    }\r\n\r\n    const response = await fetchWithAuth(url, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n\r\n    return response;\r\n  } catch (error) {\r\n    console.error(\"Lỗi khi lấy thông tin bất động sản của người dùng:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi lấy thông tin bất động sản của người dùng\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Get property statistics by status\r\n * @returns {Promise<Object>} Response with property stats data\r\n */\r\nexport async function getPropertyStats() {\r\n  try {\r\n    const response = await fetchWithAuth(`${API_BASE_URL}/stats`, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n\r\n    if (response.success) {\r\n      // Transform the response to match the expected format for the UI\r\n      const statsData = response.data;\r\n      return {\r\n        success: true,\r\n        data: {\r\n          total: statsData.totalProperties || 0,\r\n          approved: statsData.propertiesByStatus?.Approved || 0,\r\n          pendingApproval: statsData.propertiesByStatus?.PendingApproval || 0,\r\n          rejectedByAdmin: statsData.propertiesByStatus?.RejectedByAdmin || 0,\r\n          rejectedDueToUnpaid: statsData.propertiesByStatus?.RejectedDueToUnpaid || 0,\r\n          waitingPayment: statsData.propertiesByStatus?.WaitingPayment || 0,\r\n          expired: statsData.propertiesByStatus?.Expired || 0,\r\n          draft: statsData.propertiesByStatus?.Draft || 0,\r\n          sold: statsData.propertiesByStatus?.Sold || 0,\r\n        }\r\n      };\r\n    }\r\n\r\n    return response;\r\n  } catch (error) {\r\n    console.error(\"Lỗi khi lấy thống kê bất động sản:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi lấy thống kê bất động sản\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function uploadPropertyImages(prevState, formData) {\r\n  try {\r\n    const files = formData.getAll(\"files\"); // Get all files from FormData\r\n    const propertyId = formData.get(\"propertyId\");\r\n\r\n    if (files.length === 0) {\r\n      return handleErrorResponse(false, null, \"Thiếu tập tin\");\r\n    }\r\n\r\n    const formDataToSend = new FormData();\r\n    files.forEach((file) => formDataToSend.append(\"files\", file));\r\n    formDataToSend.append(\"propertyId\", propertyId);\r\n\r\n    return await fetchWithAuth(`${API_BASE_URL}/upload-images`, {\r\n      method: \"POST\",\r\n      body: formDataToSend,\r\n    });\r\n  } catch (error) {\r\n    logError(\"PropertyService\", error, { action: \"uploadPropertyImages\", formData });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi tải lên hình ảnh\");\r\n  }\r\n}\r\n\r\nexport async function verifyPropertyRemainingTimes(propertyId) {\r\n  try {\r\n    return await fetchWithAuth(`${API_BASE_URL}/edit-remaining/${propertyId}`, {\r\n      method: \"GET\",\r\n    });\r\n  } catch (error) {\r\n    console.error(\"Error checking remaining verification times:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi kiểm tra số lần xác thực.\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function getPropertyStatusHistory(propertyId) {\r\n  try {\r\n    return await fetchWithAuth(`${PROPERTY_ANALYTICS_API_BASE_URL}/history/status/${propertyId}`, {\r\n      method: \"GET\",\r\n    });\r\n  } catch (error) {\r\n    console.error(\"Error fetching property history:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi lấy lịch sử hoạt động\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function getNearbyProperties(latitude, longitude, radius = 5000) {\r\n  try {\r\n    // Use fetchWithoutAuth for public API call\r\n    return await fetchWithoutAuth(\r\n      `${API_BASE_URL}/nearby?latitude=${latitude}&longitude=${longitude}&radius=${radius}`, {\r\n        method: \"GET\",\r\n      }\r\n    );\r\n  } catch (error) {\r\n    console.error(\"Error fetching nearby properties:\", error);\r\n    return {\r\n        success: false,\r\n        message: \"Đã xảy ra lỗi khi tìm kiếm bất động sản lân cận\",\r\n        errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\n// New function to get property report data (Placeholder)\r\nexport async function getPropertyReportById(propertyId) {\r\n  \"use server\"; // Ensure this runs on the server\r\n\r\n  // Simulate API call delay\r\n  await new Promise(resolve => setTimeout(resolve, 750));\r\n\r\n  try {\r\n    // --- FAKE DATA ---\r\n    // In a real scenario, you would fetch this from your API:\r\n    // const response = await fetchWithAuth(`${API_BASE_URL}/report/${propertyId}`);\r\n    // if (!response.success) return response;\r\n    // const reportData = response.data;\r\n\r\n    // For now, generate some fake data:\r\n    const fakeReportData = {\r\n      views: 30 + Math.floor(Math.random() * 100),           // Lượt xem\r\n      impressions: 33 + Math.floor(Math.random() * 150),    // Lượt hiển thị\r\n      cartAdds: 5 + Math.floor(Math.random() * 40),         // Lượt bỏ vào giỏ hàng (Example)\r\n      contactRequests: 10 + Math.floor(Math.random() * 20), // Lượt liên hệ (Example - added based on previous comment)\r\n      highlightsCount: 1 + Math.floor(Math.random() * 15),  // Số lần highlight\r\n      renewalsCount: 0 + Math.floor(Math.random() * 5),     // Số lần gia hạn\r\n      postCost: 55000,                                      // Tiền bài đăng\r\n      highlightCost: (1 + Math.floor(Math.random() * 15)) * 40000, // Tổng tiền highlight\r\n      renewalCost: (0 + Math.floor(Math.random() * 5)) * 300000, // Tổng tiền gia hạn\r\n    };\r\n    fakeReportData.totalCost = fakeReportData.postCost + fakeReportData.highlightCost + fakeReportData.renewalCost;\r\n\r\n    return {\r\n      success: true,\r\n      data: fakeReportData,\r\n    };\r\n\r\n  } catch (error) {\r\n    console.error(`Error fetching report for propertyId ${propertyId}:`, error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi lấy dữ liệu báo cáo.\",\r\n      errorType: \"internal_error\",\r\n    };\r\n  }\r\n}\r\n\r\n// Update property media caption\r\nexport async function updatePropertyMediaCaption(mediaId, caption) {\r\n  try {\r\n    const payload = {\r\n      id: mediaId,\r\n      caption: caption\r\n    };\r\n\r\n    return await fetchWithAuth(`${process.env.API_URL}/Media/update-caption`, {\r\n      method: \"PUT\",\r\n      body: JSON.stringify(payload),\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"PropertyService\", error, {\r\n      action: \"updatePropertyMediaCaption\",\r\n      mediaId,\r\n      caption,\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi cập nhật chú thích hình ảnh\");\r\n  }\r\n}\r\n\r\n// Update property media isAvatar status\r\nexport async function updatePropertyMediaIsAvatar(mediaId, isAvatar) {\r\n  try {\r\n    const payload = {\r\n      id: mediaId,\r\n      isAvatar: isAvatar\r\n    };\r\n\r\n    return await fetchWithAuth(`${process.env.API_URL}/Media/update-is-avatar`, {\r\n      method: \"PUT\",\r\n      body: JSON.stringify(payload),\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"PropertyService\", error, {\r\n      action: \"updatePropertyMediaIsAvatar\",\r\n      mediaId,\r\n      isAvatar,\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi cập nhật ảnh đại diện\");\r\n  }\r\n}\r\n\r\n// Delete property media\r\nexport async function deletePropertyMedia(mediaId) {\r\n  try {\r\n    return await fetchWithAuth(`${process.env.API_URL}/Media/${mediaId}`, {\r\n      method: \"DELETE\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"PropertyService\", error, {\r\n      action: \"deletePropertyMedia\",\r\n      mediaId,\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi xóa hình ảnh\");\r\n  }\r\n}\r\n\r\n/**\r\n * Bulk delete multiple properties\r\n * @param {Array<string>} propertyIds - Array of property IDs to delete\r\n * @returns {Promise<Object>} Response with success/error information\r\n */\r\nexport async function bulkDeleteProperties(propertyIds) {\r\n  try {\r\n    return await fetchWithAuth(`${API_BASE_URL}/bulk`, {\r\n      method: \"DELETE\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      body: JSON.stringify({\r\n        propertyIds: propertyIds\r\n      }),\r\n    });\r\n  } catch (error) {\r\n    console.error(\"Lỗi khi xóa nhiều bất động sản:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi xóa nhiều bất động sản\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Bulk update status for multiple properties\r\n * @param {Array<string>} propertyIds - Array of property IDs to update\r\n * @param {string} status - New status to set\r\n * @param {string} comment - Optional comment for the status update\r\n * @returns {Promise<Object>} Response with success/error information\r\n */\r\nexport async function bulkUpdatePropertyStatus(propertyIds, status, comment = \"\") {\r\n  try {\r\n    return await fetchWithAuth(`${API_BASE_URL}/bulk/status`, {\r\n      method: \"PUT\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      body: JSON.stringify({\r\n        propertyIds: propertyIds,\r\n        status: status,\r\n        comment: comment\r\n      }),\r\n    });\r\n  } catch (error) {\r\n    console.error(\"Lỗi khi cập nhật trạng thái nhiều bất động sản:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi cập nhật trạng thái nhiều bất động sản\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Update highlight status for a property\r\n * @param {string} propertyId - ID of the property to update\r\n * @param {boolean} isHighlighted - Whether the property should be highlighted\r\n * @returns {Promise<Object>} Response with success/error information\r\n */\r\nexport async function updatePropertyHighlight(propertyId, isHighlighted) {\r\n  try {\r\n    return await fetchWithAuth(`${API_BASE_URL}/${propertyId}/highlight`, {\r\n      method: \"PUT\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      body: JSON.stringify({\r\n        isHighlighted: isHighlighted\r\n      }),\r\n    });\r\n  } catch (error) {\r\n    console.error(\"Lỗi khi cập nhật trạng thái nổi bật của bất động sản:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi cập nhật trạng thái nổi bật của bất động sản\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Bulk update highlight status for multiple properties\r\n * @param {Array<string>} propertyIds - Array of property IDs to update\r\n * @param {boolean} isHighlighted - Whether the properties should be highlighted\r\n * @returns {Promise<Object>} Response with success/error information\r\n */\r\nexport async function bulkUpdatePropertyHighlight(propertyIds, isHighlighted) {\r\n  try {\r\n    return await fetchWithAuth(`${API_BASE_URL}/bulk/highlight`, {\r\n      method: \"PUT\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      body: JSON.stringify({\r\n        propertyIds: propertyIds,\r\n        isHighlighted: isHighlighted\r\n      }),\r\n    });\r\n  } catch (error) {\r\n    console.error(\"Lỗi khi cập nhật trạng thái nổi bật của nhiều bất động sản:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi cập nhật trạng thái nổi bật của nhiều bất động sản\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;;IAiSsB,oBAAA,WAAA,GAAA,CAAA,GAAA,sNAAA,CAAA,wBAAA,EAAA,8CAAA,sNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,sNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 33, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/app/actions/server/property.jsx"], "sourcesContent": ["\"use server\";\r\n\r\nimport { handleErrorResponse, logError } from \"@/lib/apiUtils\";\r\nimport { fetchWithAuth, getSession, fetchWithoutAuth } from \"@/lib/sessionUtils\";\r\nimport { parseEmptyStringsToNull } from \"@/lib/utils\";\r\n\r\nconst API_BASE_URL = `${process.env.API_URL}/api/Property`;\r\nconst PROPERTY_ANALYTICS_API_BASE_URL = `${process.env.API_URL}/api/PropertyAnalytics`;\r\n\r\n// New function to search properties with filters\r\nexport async function searchProperties(filterCriteria) {\r\n  try {\r\n    // Build query parameters from filter criteria\r\n    const queryParams = new URLSearchParams();\r\n\r\n    // Transaction Type (postType)\r\n    if (filterCriteria.transactionType && filterCriteria.transactionType.length > 0) {\r\n      filterCriteria.transactionType.forEach(type => {\r\n        queryParams.append('postType', type);\r\n      });\r\n    }\r\n\r\n    // Property Type\r\n    if (filterCriteria.propertyType && filterCriteria.propertyType.length > 0) {\r\n      filterCriteria.propertyType.forEach(type => {\r\n        queryParams.append('propertyType', type);\r\n      });\r\n    }\r\n\r\n    // Location\r\n    if (filterCriteria.location) {\r\n      if (filterCriteria.location.province) {\r\n        queryParams.append('cityId', filterCriteria.location.province);\r\n      }\r\n      if (filterCriteria.location.district) {\r\n        queryParams.append('districtId', filterCriteria.location.district);\r\n      }\r\n      if (filterCriteria.location.address) {\r\n        queryParams.append('address', filterCriteria.location.address);\r\n      }\r\n    }\r\n\r\n    // Price Range\r\n    if (filterCriteria.priceRange) {\r\n      if (filterCriteria.priceRange.min) {\r\n        queryParams.append('minPrice', filterCriteria.priceRange.min);\r\n      }\r\n      if (filterCriteria.priceRange.max) {\r\n        queryParams.append('maxPrice', filterCriteria.priceRange.max);\r\n      }\r\n    }\r\n\r\n    // Area Range\r\n    if (filterCriteria.areaRange) {\r\n      if (filterCriteria.areaRange.min) {\r\n        queryParams.append('minArea', filterCriteria.areaRange.min);\r\n      }\r\n      if (filterCriteria.areaRange.max) {\r\n        queryParams.append('maxArea', filterCriteria.areaRange.max);\r\n      }\r\n    }\r\n\r\n    // Bedrooms\r\n    if (filterCriteria.bedrooms) {\r\n      queryParams.append('minRooms', filterCriteria.bedrooms);\r\n    }\r\n\r\n    // Bathrooms\r\n    if (filterCriteria.bathrooms) {\r\n      queryParams.append('minToilets', filterCriteria.bathrooms);\r\n    }\r\n\r\n    // Direction\r\n    if (filterCriteria.direction) {\r\n      queryParams.append('direction', filterCriteria.direction);\r\n    }\r\n\r\n    // Legal Status\r\n    if (filterCriteria.legalStatus) {\r\n      queryParams.append('legality', filterCriteria.legalStatus);\r\n    }\r\n\r\n    // Road Width\r\n    if (filterCriteria.roadWidth) {\r\n      queryParams.append('minRoadWidth', filterCriteria.roadWidth);\r\n    }\r\n\r\n    // User location for proximity search\r\n    if (filterCriteria.sw_lat && filterCriteria.sw_lng && filterCriteria.ne_lat && filterCriteria.ne_lng) {\r\n      queryParams.append('swLat', filterCriteria.sw_lat);\r\n      queryParams.append('swLng', filterCriteria.sw_lng);\r\n      queryParams.append('neLat', filterCriteria.ne_lat);\r\n      queryParams.append('neLng', filterCriteria.ne_lng);\r\n\r\n    }\r\n\r\n    // Build the URL with query parameters\r\n    const url = `${API_BASE_URL}/search?${queryParams.toString()}`;\r\n\r\n    // Use fetchWithoutAuth for public API call\r\n    const response = await fetchWithoutAuth(url, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n\r\n    return response; // fetchWithoutAuth already returns the standard response format\r\n\r\n  } catch (error) {\r\n    // Handle errors outside fetchWithoutAuth\r\n    console.error(`Lỗi khi gọi fetchWithoutAuth cho searchProperties:`, error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi không mong muốn khi tìm kiếm bất động sản\",\r\n      errorType: \"internal_error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function getPropertyById(propertyId) {\r\n  const url = `${API_BASE_URL}/${propertyId}`;\r\n  try {\r\n    // Use fetchWithoutAuth for public API call\r\n    const response = await fetchWithoutAuth(url, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n\r\n    return response; // fetchWithoutAuth already returns the standard { success, data, message, errorType } object\r\n\r\n  } catch (error) {\r\n    // This catch block might handle errors occurring outside the fetchWithoutAuth call itself\r\n    console.error(`Lỗi khi gọi fetchWithoutAuth cho ${url}:`, error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi không mong muốn khi lấy thông tin bất động sản\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function updatePropertyById(prevState, formData) {\r\n  try {\r\n    // Get token from formData\r\n    const propertyId = formData.get(\"propertyId\");\r\n\r\n    // Convert FormData to a plain object for easier handling\r\n    const formDataObject = Object.fromEntries(formData.entries());\r\n    const payload = {\r\n      ...formDataObject,\r\n    };\r\n\r\n    const userSession = await getSession(\"UserProfile\");\r\n    if (userSession) {\r\n      const user = JSON.parse(userSession);\r\n      payload.ownerId = user.id;\r\n    }\r\n\r\n    // ✅ Parse the uploadedFiles JSON string back into an array\r\n    if (formDataObject.UploadedFiles) {\r\n      payload.UploadedFiles = JSON.parse(formDataObject.UploadedFiles);\r\n    }\r\n\r\n    payload.isHighlighted = formDataObject.isHighlighted === \"true\" ? true : false;\r\n    payload.isAutoRenew = formDataObject.isAutoRenew === \"true\" ? true : false;\r\n\r\n    return await fetchWithAuth(`${API_BASE_URL}/${propertyId}`, {\r\n      method: \"PUT\",\r\n      body: JSON.stringify(parseEmptyStringsToNull(payload)),\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n        \"accept\": \"*/*\"\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"PropertyService\", error, {\r\n      action: \"createProperty\",\r\n      formData,\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi tạo bất động sản\");\r\n  }\r\n}\r\n\r\nexport async function deletePropertyById(propertyId) {\r\n  try {\r\n    return await fetchWithAuth(`${API_BASE_URL}/${propertyId}`, {\r\n      method: \"DELETE\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    console.error(\"Lỗi khi xóa bất động sản:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi xóa bất động sản\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function getAllProperties() {\r\n  try {\r\n    // Use fetchWithoutAuth for public API call\r\n    const response = await fetchWithoutAuth(API_BASE_URL, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n\r\n    return response; // fetchWithoutAuth already returns the standard response format\r\n\r\n  } catch (error) {\r\n    // Handle errors outside fetchWithoutAuth\r\n    console.error(`Lỗi khi gọi fetchWithoutAuth cho getAllProperties:`, error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi không mong muốn khi lấy danh sách bất động sản\",\r\n      errorType: \"internal_error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function createProperty(prevState, formData) {\r\n  try {\r\n    // Convert FormData to a plain object for easier handling\r\n    const formDataObject = Object.fromEntries(formData.entries());\r\n\r\n    const payload = {\r\n      ...formDataObject,\r\n    };\r\n\r\n    const userSession = await getSession(\"UserProfile\");\r\n    if (userSession) {\r\n      const user = JSON.parse(userSession);\r\n      payload.ownerId = user.id;\r\n    }\r\n\r\n    // ✅ Parse the uploadedFiles JSON string back into an array\r\n    if (formDataObject.UploadedFiles) {\r\n      payload.UploadedFiles = JSON.parse(formDataObject.UploadedFiles);\r\n    }\r\n\r\n    payload.isHighlighted = formDataObject.isHighlighted === \"true\" ? true : false;\r\n    payload.isAutoRenew = formDataObject.isAutoRenew === \"true\" ? true : false;\r\n\r\n    return await fetchWithAuth(API_BASE_URL, {\r\n      method: \"POST\",\r\n      body: JSON.stringify(parseEmptyStringsToNull(payload)),\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"PropertyService\", error, {\r\n      action: \"createProperty\",\r\n      formData,\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi tạo bất động sản\");\r\n  }\r\n}\r\n\r\nexport async function updatePropertyStatus(formData) {\r\n  try {\r\n    const propertyId = formData.get(\"propertyId\");\r\n\r\n    return await fetchWithAuth(`${API_BASE_URL}/${propertyId}/status`, {\r\n      method: \"PUT\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      body: JSON.stringify({\r\n        status: formData.get(\"status\"),\r\n        comment: formData.get(\"comment\") || \"\"\r\n      }),\r\n    });\r\n  } catch (error) {\r\n    logError(\"PropertyService\", error, {\r\n      action: \"updatePropertyStatus\",\r\n      formData,\r\n    });\r\n    return handleErrorResponse(false, null, \"Không thể cập nhật trạng thái bất động sản\");\r\n  }\r\n}\r\n\r\nexport async function getPropertyByUser(status, page = 1, pageSize = 10) {\r\n  try {\r\n    // If status is 'counts', call the stats endpoint instead\r\n    if (status === 'counts') {\r\n      return await getPropertyStats();\r\n    }\r\n\r\n    let url = `${API_BASE_URL}/me`;\r\n    const queryParams = new URLSearchParams();\r\n\r\n    // Add parameters if provided\r\n    if (status) {\r\n      queryParams.append('status', status);\r\n    }\r\n\r\n    queryParams.append('page', page);\r\n    queryParams.append('pageSize', pageSize);\r\n\r\n    // Append query parameters to URL if any exist\r\n    if (queryParams.toString()) {\r\n      url += `?${queryParams.toString()}`;\r\n    }\r\n\r\n    const response = await fetchWithAuth(url, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n\r\n    return response;\r\n  } catch (error) {\r\n    console.error(\"Lỗi khi lấy thông tin bất động sản của người dùng:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi lấy thông tin bất động sản của người dùng\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Get property statistics by status\r\n * @returns {Promise<Object>} Response with property stats data\r\n */\r\nexport async function getPropertyStats() {\r\n  try {\r\n    const response = await fetchWithAuth(`${API_BASE_URL}/stats`, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n\r\n    if (response.success) {\r\n      // Transform the response to match the expected format for the UI\r\n      const statsData = response.data;\r\n      return {\r\n        success: true,\r\n        data: {\r\n          total: statsData.totalProperties || 0,\r\n          approved: statsData.propertiesByStatus?.Approved || 0,\r\n          pendingApproval: statsData.propertiesByStatus?.PendingApproval || 0,\r\n          rejectedByAdmin: statsData.propertiesByStatus?.RejectedByAdmin || 0,\r\n          rejectedDueToUnpaid: statsData.propertiesByStatus?.RejectedDueToUnpaid || 0,\r\n          waitingPayment: statsData.propertiesByStatus?.WaitingPayment || 0,\r\n          expired: statsData.propertiesByStatus?.Expired || 0,\r\n          draft: statsData.propertiesByStatus?.Draft || 0,\r\n          sold: statsData.propertiesByStatus?.Sold || 0,\r\n        }\r\n      };\r\n    }\r\n\r\n    return response;\r\n  } catch (error) {\r\n    console.error(\"Lỗi khi lấy thống kê bất động sản:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi lấy thống kê bất động sản\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function uploadPropertyImages(prevState, formData) {\r\n  try {\r\n    const files = formData.getAll(\"files\"); // Get all files from FormData\r\n    const propertyId = formData.get(\"propertyId\");\r\n\r\n    if (files.length === 0) {\r\n      return handleErrorResponse(false, null, \"Thiếu tập tin\");\r\n    }\r\n\r\n    const formDataToSend = new FormData();\r\n    files.forEach((file) => formDataToSend.append(\"files\", file));\r\n    formDataToSend.append(\"propertyId\", propertyId);\r\n\r\n    return await fetchWithAuth(`${API_BASE_URL}/upload-images`, {\r\n      method: \"POST\",\r\n      body: formDataToSend,\r\n    });\r\n  } catch (error) {\r\n    logError(\"PropertyService\", error, { action: \"uploadPropertyImages\", formData });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi tải lên hình ảnh\");\r\n  }\r\n}\r\n\r\nexport async function verifyPropertyRemainingTimes(propertyId) {\r\n  try {\r\n    return await fetchWithAuth(`${API_BASE_URL}/edit-remaining/${propertyId}`, {\r\n      method: \"GET\",\r\n    });\r\n  } catch (error) {\r\n    console.error(\"Error checking remaining verification times:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi kiểm tra số lần xác thực.\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function getPropertyStatusHistory(propertyId) {\r\n  try {\r\n    return await fetchWithAuth(`${PROPERTY_ANALYTICS_API_BASE_URL}/history/status/${propertyId}`, {\r\n      method: \"GET\",\r\n    });\r\n  } catch (error) {\r\n    console.error(\"Error fetching property history:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi lấy lịch sử hoạt động\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function getNearbyProperties(latitude, longitude, radius = 5000) {\r\n  try {\r\n    // Use fetchWithoutAuth for public API call\r\n    return await fetchWithoutAuth(\r\n      `${API_BASE_URL}/nearby?latitude=${latitude}&longitude=${longitude}&radius=${radius}`, {\r\n        method: \"GET\",\r\n      }\r\n    );\r\n  } catch (error) {\r\n    console.error(\"Error fetching nearby properties:\", error);\r\n    return {\r\n        success: false,\r\n        message: \"Đã xảy ra lỗi khi tìm kiếm bất động sản lân cận\",\r\n        errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\n// New function to get property report data (Placeholder)\r\nexport async function getPropertyReportById(propertyId) {\r\n  \"use server\"; // Ensure this runs on the server\r\n\r\n  // Simulate API call delay\r\n  await new Promise(resolve => setTimeout(resolve, 750));\r\n\r\n  try {\r\n    // --- FAKE DATA ---\r\n    // In a real scenario, you would fetch this from your API:\r\n    // const response = await fetchWithAuth(`${API_BASE_URL}/report/${propertyId}`);\r\n    // if (!response.success) return response;\r\n    // const reportData = response.data;\r\n\r\n    // For now, generate some fake data:\r\n    const fakeReportData = {\r\n      views: 30 + Math.floor(Math.random() * 100),           // Lượt xem\r\n      impressions: 33 + Math.floor(Math.random() * 150),    // Lượt hiển thị\r\n      cartAdds: 5 + Math.floor(Math.random() * 40),         // Lượt bỏ vào giỏ hàng (Example)\r\n      contactRequests: 10 + Math.floor(Math.random() * 20), // Lượt liên hệ (Example - added based on previous comment)\r\n      highlightsCount: 1 + Math.floor(Math.random() * 15),  // Số lần highlight\r\n      renewalsCount: 0 + Math.floor(Math.random() * 5),     // Số lần gia hạn\r\n      postCost: 55000,                                      // Tiền bài đăng\r\n      highlightCost: (1 + Math.floor(Math.random() * 15)) * 40000, // Tổng tiền highlight\r\n      renewalCost: (0 + Math.floor(Math.random() * 5)) * 300000, // Tổng tiền gia hạn\r\n    };\r\n    fakeReportData.totalCost = fakeReportData.postCost + fakeReportData.highlightCost + fakeReportData.renewalCost;\r\n\r\n    return {\r\n      success: true,\r\n      data: fakeReportData,\r\n    };\r\n\r\n  } catch (error) {\r\n    console.error(`Error fetching report for propertyId ${propertyId}:`, error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi lấy dữ liệu báo cáo.\",\r\n      errorType: \"internal_error\",\r\n    };\r\n  }\r\n}\r\n\r\n// Update property media caption\r\nexport async function updatePropertyMediaCaption(mediaId, caption) {\r\n  try {\r\n    const payload = {\r\n      id: mediaId,\r\n      caption: caption\r\n    };\r\n\r\n    return await fetchWithAuth(`${process.env.API_URL}/Media/update-caption`, {\r\n      method: \"PUT\",\r\n      body: JSON.stringify(payload),\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"PropertyService\", error, {\r\n      action: \"updatePropertyMediaCaption\",\r\n      mediaId,\r\n      caption,\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi cập nhật chú thích hình ảnh\");\r\n  }\r\n}\r\n\r\n// Update property media isAvatar status\r\nexport async function updatePropertyMediaIsAvatar(mediaId, isAvatar) {\r\n  try {\r\n    const payload = {\r\n      id: mediaId,\r\n      isAvatar: isAvatar\r\n    };\r\n\r\n    return await fetchWithAuth(`${process.env.API_URL}/Media/update-is-avatar`, {\r\n      method: \"PUT\",\r\n      body: JSON.stringify(payload),\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"PropertyService\", error, {\r\n      action: \"updatePropertyMediaIsAvatar\",\r\n      mediaId,\r\n      isAvatar,\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi cập nhật ảnh đại diện\");\r\n  }\r\n}\r\n\r\n// Delete property media\r\nexport async function deletePropertyMedia(mediaId) {\r\n  try {\r\n    return await fetchWithAuth(`${process.env.API_URL}/Media/${mediaId}`, {\r\n      method: \"DELETE\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"PropertyService\", error, {\r\n      action: \"deletePropertyMedia\",\r\n      mediaId,\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi xóa hình ảnh\");\r\n  }\r\n}\r\n\r\n/**\r\n * Bulk delete multiple properties\r\n * @param {Array<string>} propertyIds - Array of property IDs to delete\r\n * @returns {Promise<Object>} Response with success/error information\r\n */\r\nexport async function bulkDeleteProperties(propertyIds) {\r\n  try {\r\n    return await fetchWithAuth(`${API_BASE_URL}/bulk`, {\r\n      method: \"DELETE\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      body: JSON.stringify({\r\n        propertyIds: propertyIds\r\n      }),\r\n    });\r\n  } catch (error) {\r\n    console.error(\"Lỗi khi xóa nhiều bất động sản:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi xóa nhiều bất động sản\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Bulk update status for multiple properties\r\n * @param {Array<string>} propertyIds - Array of property IDs to update\r\n * @param {string} status - New status to set\r\n * @param {string} comment - Optional comment for the status update\r\n * @returns {Promise<Object>} Response with success/error information\r\n */\r\nexport async function bulkUpdatePropertyStatus(propertyIds, status, comment = \"\") {\r\n  try {\r\n    return await fetchWithAuth(`${API_BASE_URL}/bulk/status`, {\r\n      method: \"PUT\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      body: JSON.stringify({\r\n        propertyIds: propertyIds,\r\n        status: status,\r\n        comment: comment\r\n      }),\r\n    });\r\n  } catch (error) {\r\n    console.error(\"Lỗi khi cập nhật trạng thái nhiều bất động sản:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi cập nhật trạng thái nhiều bất động sản\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Update highlight status for a property\r\n * @param {string} propertyId - ID of the property to update\r\n * @param {boolean} isHighlighted - Whether the property should be highlighted\r\n * @returns {Promise<Object>} Response with success/error information\r\n */\r\nexport async function updatePropertyHighlight(propertyId, isHighlighted) {\r\n  try {\r\n    return await fetchWithAuth(`${API_BASE_URL}/${propertyId}/highlight`, {\r\n      method: \"PUT\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      body: JSON.stringify({\r\n        isHighlighted: isHighlighted\r\n      }),\r\n    });\r\n  } catch (error) {\r\n    console.error(\"Lỗi khi cập nhật trạng thái nổi bật của bất động sản:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi cập nhật trạng thái nổi bật của bất động sản\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Bulk update highlight status for multiple properties\r\n * @param {Array<string>} propertyIds - Array of property IDs to update\r\n * @param {boolean} isHighlighted - Whether the properties should be highlighted\r\n * @returns {Promise<Object>} Response with success/error information\r\n */\r\nexport async function bulkUpdatePropertyHighlight(propertyIds, isHighlighted) {\r\n  try {\r\n    return await fetchWithAuth(`${API_BASE_URL}/bulk/highlight`, {\r\n      method: \"PUT\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      body: JSON.stringify({\r\n        propertyIds: propertyIds,\r\n        isHighlighted: isHighlighted\r\n      }),\r\n    });\r\n  } catch (error) {\r\n    console.error(\"Lỗi khi cập nhật trạng thái nổi bật của nhiều bất động sản:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi cập nhật trạng thái nổi bật của nhiều bất động sản\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;;IA4YsB,+BAAA,WAAA,GAAA,CAAA,GAAA,sNAAA,CAAA,wBAAA,EAAA,8CAAA,sNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,sNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 46, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/app/actions/server/property.jsx"], "sourcesContent": ["\"use server\";\r\n\r\nimport { handleErrorResponse, logError } from \"@/lib/apiUtils\";\r\nimport { fetchWithAuth, getSession, fetchWithoutAuth } from \"@/lib/sessionUtils\";\r\nimport { parseEmptyStringsToNull } from \"@/lib/utils\";\r\n\r\nconst API_BASE_URL = `${process.env.API_URL}/api/Property`;\r\nconst PROPERTY_ANALYTICS_API_BASE_URL = `${process.env.API_URL}/api/PropertyAnalytics`;\r\n\r\n// New function to search properties with filters\r\nexport async function searchProperties(filterCriteria) {\r\n  try {\r\n    // Build query parameters from filter criteria\r\n    const queryParams = new URLSearchParams();\r\n\r\n    // Transaction Type (postType)\r\n    if (filterCriteria.transactionType && filterCriteria.transactionType.length > 0) {\r\n      filterCriteria.transactionType.forEach(type => {\r\n        queryParams.append('postType', type);\r\n      });\r\n    }\r\n\r\n    // Property Type\r\n    if (filterCriteria.propertyType && filterCriteria.propertyType.length > 0) {\r\n      filterCriteria.propertyType.forEach(type => {\r\n        queryParams.append('propertyType', type);\r\n      });\r\n    }\r\n\r\n    // Location\r\n    if (filterCriteria.location) {\r\n      if (filterCriteria.location.province) {\r\n        queryParams.append('cityId', filterCriteria.location.province);\r\n      }\r\n      if (filterCriteria.location.district) {\r\n        queryParams.append('districtId', filterCriteria.location.district);\r\n      }\r\n      if (filterCriteria.location.address) {\r\n        queryParams.append('address', filterCriteria.location.address);\r\n      }\r\n    }\r\n\r\n    // Price Range\r\n    if (filterCriteria.priceRange) {\r\n      if (filterCriteria.priceRange.min) {\r\n        queryParams.append('minPrice', filterCriteria.priceRange.min);\r\n      }\r\n      if (filterCriteria.priceRange.max) {\r\n        queryParams.append('maxPrice', filterCriteria.priceRange.max);\r\n      }\r\n    }\r\n\r\n    // Area Range\r\n    if (filterCriteria.areaRange) {\r\n      if (filterCriteria.areaRange.min) {\r\n        queryParams.append('minArea', filterCriteria.areaRange.min);\r\n      }\r\n      if (filterCriteria.areaRange.max) {\r\n        queryParams.append('maxArea', filterCriteria.areaRange.max);\r\n      }\r\n    }\r\n\r\n    // Bedrooms\r\n    if (filterCriteria.bedrooms) {\r\n      queryParams.append('minRooms', filterCriteria.bedrooms);\r\n    }\r\n\r\n    // Bathrooms\r\n    if (filterCriteria.bathrooms) {\r\n      queryParams.append('minToilets', filterCriteria.bathrooms);\r\n    }\r\n\r\n    // Direction\r\n    if (filterCriteria.direction) {\r\n      queryParams.append('direction', filterCriteria.direction);\r\n    }\r\n\r\n    // Legal Status\r\n    if (filterCriteria.legalStatus) {\r\n      queryParams.append('legality', filterCriteria.legalStatus);\r\n    }\r\n\r\n    // Road Width\r\n    if (filterCriteria.roadWidth) {\r\n      queryParams.append('minRoadWidth', filterCriteria.roadWidth);\r\n    }\r\n\r\n    // User location for proximity search\r\n    if (filterCriteria.sw_lat && filterCriteria.sw_lng && filterCriteria.ne_lat && filterCriteria.ne_lng) {\r\n      queryParams.append('swLat', filterCriteria.sw_lat);\r\n      queryParams.append('swLng', filterCriteria.sw_lng);\r\n      queryParams.append('neLat', filterCriteria.ne_lat);\r\n      queryParams.append('neLng', filterCriteria.ne_lng);\r\n\r\n    }\r\n\r\n    // Build the URL with query parameters\r\n    const url = `${API_BASE_URL}/search?${queryParams.toString()}`;\r\n\r\n    // Use fetchWithoutAuth for public API call\r\n    const response = await fetchWithoutAuth(url, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n\r\n    return response; // fetchWithoutAuth already returns the standard response format\r\n\r\n  } catch (error) {\r\n    // Handle errors outside fetchWithoutAuth\r\n    console.error(`Lỗi khi gọi fetchWithoutAuth cho searchProperties:`, error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi không mong muốn khi tìm kiếm bất động sản\",\r\n      errorType: \"internal_error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function getPropertyById(propertyId) {\r\n  const url = `${API_BASE_URL}/${propertyId}`;\r\n  try {\r\n    // Use fetchWithoutAuth for public API call\r\n    const response = await fetchWithoutAuth(url, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n\r\n    return response; // fetchWithoutAuth already returns the standard { success, data, message, errorType } object\r\n\r\n  } catch (error) {\r\n    // This catch block might handle errors occurring outside the fetchWithoutAuth call itself\r\n    console.error(`Lỗi khi gọi fetchWithoutAuth cho ${url}:`, error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi không mong muốn khi lấy thông tin bất động sản\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function updatePropertyById(prevState, formData) {\r\n  try {\r\n    // Get token from formData\r\n    const propertyId = formData.get(\"propertyId\");\r\n\r\n    // Convert FormData to a plain object for easier handling\r\n    const formDataObject = Object.fromEntries(formData.entries());\r\n    const payload = {\r\n      ...formDataObject,\r\n    };\r\n\r\n    const userSession = await getSession(\"UserProfile\");\r\n    if (userSession) {\r\n      const user = JSON.parse(userSession);\r\n      payload.ownerId = user.id;\r\n    }\r\n\r\n    // ✅ Parse the uploadedFiles JSON string back into an array\r\n    if (formDataObject.UploadedFiles) {\r\n      payload.UploadedFiles = JSON.parse(formDataObject.UploadedFiles);\r\n    }\r\n\r\n    payload.isHighlighted = formDataObject.isHighlighted === \"true\" ? true : false;\r\n    payload.isAutoRenew = formDataObject.isAutoRenew === \"true\" ? true : false;\r\n\r\n    return await fetchWithAuth(`${API_BASE_URL}/${propertyId}`, {\r\n      method: \"PUT\",\r\n      body: JSON.stringify(parseEmptyStringsToNull(payload)),\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n        \"accept\": \"*/*\"\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"PropertyService\", error, {\r\n      action: \"createProperty\",\r\n      formData,\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi tạo bất động sản\");\r\n  }\r\n}\r\n\r\nexport async function deletePropertyById(propertyId) {\r\n  try {\r\n    return await fetchWithAuth(`${API_BASE_URL}/${propertyId}`, {\r\n      method: \"DELETE\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    console.error(\"Lỗi khi xóa bất động sản:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi xóa bất động sản\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function getAllProperties() {\r\n  try {\r\n    // Use fetchWithoutAuth for public API call\r\n    const response = await fetchWithoutAuth(API_BASE_URL, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n\r\n    return response; // fetchWithoutAuth already returns the standard response format\r\n\r\n  } catch (error) {\r\n    // Handle errors outside fetchWithoutAuth\r\n    console.error(`Lỗi khi gọi fetchWithoutAuth cho getAllProperties:`, error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi không mong muốn khi lấy danh sách bất động sản\",\r\n      errorType: \"internal_error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function createProperty(prevState, formData) {\r\n  try {\r\n    // Convert FormData to a plain object for easier handling\r\n    const formDataObject = Object.fromEntries(formData.entries());\r\n\r\n    const payload = {\r\n      ...formDataObject,\r\n    };\r\n\r\n    const userSession = await getSession(\"UserProfile\");\r\n    if (userSession) {\r\n      const user = JSON.parse(userSession);\r\n      payload.ownerId = user.id;\r\n    }\r\n\r\n    // ✅ Parse the uploadedFiles JSON string back into an array\r\n    if (formDataObject.UploadedFiles) {\r\n      payload.UploadedFiles = JSON.parse(formDataObject.UploadedFiles);\r\n    }\r\n\r\n    payload.isHighlighted = formDataObject.isHighlighted === \"true\" ? true : false;\r\n    payload.isAutoRenew = formDataObject.isAutoRenew === \"true\" ? true : false;\r\n\r\n    return await fetchWithAuth(API_BASE_URL, {\r\n      method: \"POST\",\r\n      body: JSON.stringify(parseEmptyStringsToNull(payload)),\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"PropertyService\", error, {\r\n      action: \"createProperty\",\r\n      formData,\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi tạo bất động sản\");\r\n  }\r\n}\r\n\r\nexport async function updatePropertyStatus(formData) {\r\n  try {\r\n    const propertyId = formData.get(\"propertyId\");\r\n\r\n    return await fetchWithAuth(`${API_BASE_URL}/${propertyId}/status`, {\r\n      method: \"PUT\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      body: JSON.stringify({\r\n        status: formData.get(\"status\"),\r\n        comment: formData.get(\"comment\") || \"\"\r\n      }),\r\n    });\r\n  } catch (error) {\r\n    logError(\"PropertyService\", error, {\r\n      action: \"updatePropertyStatus\",\r\n      formData,\r\n    });\r\n    return handleErrorResponse(false, null, \"Không thể cập nhật trạng thái bất động sản\");\r\n  }\r\n}\r\n\r\nexport async function getPropertyByUser(status, page = 1, pageSize = 10) {\r\n  try {\r\n    // If status is 'counts', call the stats endpoint instead\r\n    if (status === 'counts') {\r\n      return await getPropertyStats();\r\n    }\r\n\r\n    let url = `${API_BASE_URL}/me`;\r\n    const queryParams = new URLSearchParams();\r\n\r\n    // Add parameters if provided\r\n    if (status) {\r\n      queryParams.append('status', status);\r\n    }\r\n\r\n    queryParams.append('page', page);\r\n    queryParams.append('pageSize', pageSize);\r\n\r\n    // Append query parameters to URL if any exist\r\n    if (queryParams.toString()) {\r\n      url += `?${queryParams.toString()}`;\r\n    }\r\n\r\n    const response = await fetchWithAuth(url, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n\r\n    return response;\r\n  } catch (error) {\r\n    console.error(\"Lỗi khi lấy thông tin bất động sản của người dùng:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi lấy thông tin bất động sản của người dùng\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Get property statistics by status\r\n * @returns {Promise<Object>} Response with property stats data\r\n */\r\nexport async function getPropertyStats() {\r\n  try {\r\n    const response = await fetchWithAuth(`${API_BASE_URL}/stats`, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n\r\n    if (response.success) {\r\n      // Transform the response to match the expected format for the UI\r\n      const statsData = response.data;\r\n      return {\r\n        success: true,\r\n        data: {\r\n          total: statsData.totalProperties || 0,\r\n          approved: statsData.propertiesByStatus?.Approved || 0,\r\n          pendingApproval: statsData.propertiesByStatus?.PendingApproval || 0,\r\n          rejectedByAdmin: statsData.propertiesByStatus?.RejectedByAdmin || 0,\r\n          rejectedDueToUnpaid: statsData.propertiesByStatus?.RejectedDueToUnpaid || 0,\r\n          waitingPayment: statsData.propertiesByStatus?.WaitingPayment || 0,\r\n          expired: statsData.propertiesByStatus?.Expired || 0,\r\n          draft: statsData.propertiesByStatus?.Draft || 0,\r\n          sold: statsData.propertiesByStatus?.Sold || 0,\r\n        }\r\n      };\r\n    }\r\n\r\n    return response;\r\n  } catch (error) {\r\n    console.error(\"Lỗi khi lấy thống kê bất động sản:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi lấy thống kê bất động sản\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function uploadPropertyImages(prevState, formData) {\r\n  try {\r\n    const files = formData.getAll(\"files\"); // Get all files from FormData\r\n    const propertyId = formData.get(\"propertyId\");\r\n\r\n    if (files.length === 0) {\r\n      return handleErrorResponse(false, null, \"Thiếu tập tin\");\r\n    }\r\n\r\n    const formDataToSend = new FormData();\r\n    files.forEach((file) => formDataToSend.append(\"files\", file));\r\n    formDataToSend.append(\"propertyId\", propertyId);\r\n\r\n    return await fetchWithAuth(`${API_BASE_URL}/upload-images`, {\r\n      method: \"POST\",\r\n      body: formDataToSend,\r\n    });\r\n  } catch (error) {\r\n    logError(\"PropertyService\", error, { action: \"uploadPropertyImages\", formData });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi tải lên hình ảnh\");\r\n  }\r\n}\r\n\r\nexport async function verifyPropertyRemainingTimes(propertyId) {\r\n  try {\r\n    return await fetchWithAuth(`${API_BASE_URL}/edit-remaining/${propertyId}`, {\r\n      method: \"GET\",\r\n    });\r\n  } catch (error) {\r\n    console.error(\"Error checking remaining verification times:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi kiểm tra số lần xác thực.\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function getPropertyStatusHistory(propertyId) {\r\n  try {\r\n    return await fetchWithAuth(`${PROPERTY_ANALYTICS_API_BASE_URL}/history/status/${propertyId}`, {\r\n      method: \"GET\",\r\n    });\r\n  } catch (error) {\r\n    console.error(\"Error fetching property history:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi lấy lịch sử hoạt động\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function getNearbyProperties(latitude, longitude, radius = 5000) {\r\n  try {\r\n    // Use fetchWithoutAuth for public API call\r\n    return await fetchWithoutAuth(\r\n      `${API_BASE_URL}/nearby?latitude=${latitude}&longitude=${longitude}&radius=${radius}`, {\r\n        method: \"GET\",\r\n      }\r\n    );\r\n  } catch (error) {\r\n    console.error(\"Error fetching nearby properties:\", error);\r\n    return {\r\n        success: false,\r\n        message: \"Đã xảy ra lỗi khi tìm kiếm bất động sản lân cận\",\r\n        errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\n// New function to get property report data (Placeholder)\r\nexport async function getPropertyReportById(propertyId) {\r\n  \"use server\"; // Ensure this runs on the server\r\n\r\n  // Simulate API call delay\r\n  await new Promise(resolve => setTimeout(resolve, 750));\r\n\r\n  try {\r\n    // --- FAKE DATA ---\r\n    // In a real scenario, you would fetch this from your API:\r\n    // const response = await fetchWithAuth(`${API_BASE_URL}/report/${propertyId}`);\r\n    // if (!response.success) return response;\r\n    // const reportData = response.data;\r\n\r\n    // For now, generate some fake data:\r\n    const fakeReportData = {\r\n      views: 30 + Math.floor(Math.random() * 100),           // Lượt xem\r\n      impressions: 33 + Math.floor(Math.random() * 150),    // Lượt hiển thị\r\n      cartAdds: 5 + Math.floor(Math.random() * 40),         // Lượt bỏ vào giỏ hàng (Example)\r\n      contactRequests: 10 + Math.floor(Math.random() * 20), // Lượt liên hệ (Example - added based on previous comment)\r\n      highlightsCount: 1 + Math.floor(Math.random() * 15),  // Số lần highlight\r\n      renewalsCount: 0 + Math.floor(Math.random() * 5),     // Số lần gia hạn\r\n      postCost: 55000,                                      // Tiền bài đăng\r\n      highlightCost: (1 + Math.floor(Math.random() * 15)) * 40000, // Tổng tiền highlight\r\n      renewalCost: (0 + Math.floor(Math.random() * 5)) * 300000, // Tổng tiền gia hạn\r\n    };\r\n    fakeReportData.totalCost = fakeReportData.postCost + fakeReportData.highlightCost + fakeReportData.renewalCost;\r\n\r\n    return {\r\n      success: true,\r\n      data: fakeReportData,\r\n    };\r\n\r\n  } catch (error) {\r\n    console.error(`Error fetching report for propertyId ${propertyId}:`, error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi lấy dữ liệu báo cáo.\",\r\n      errorType: \"internal_error\",\r\n    };\r\n  }\r\n}\r\n\r\n// Update property media caption\r\nexport async function updatePropertyMediaCaption(mediaId, caption) {\r\n  try {\r\n    const payload = {\r\n      id: mediaId,\r\n      caption: caption\r\n    };\r\n\r\n    return await fetchWithAuth(`${process.env.API_URL}/Media/update-caption`, {\r\n      method: \"PUT\",\r\n      body: JSON.stringify(payload),\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"PropertyService\", error, {\r\n      action: \"updatePropertyMediaCaption\",\r\n      mediaId,\r\n      caption,\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi cập nhật chú thích hình ảnh\");\r\n  }\r\n}\r\n\r\n// Update property media isAvatar status\r\nexport async function updatePropertyMediaIsAvatar(mediaId, isAvatar) {\r\n  try {\r\n    const payload = {\r\n      id: mediaId,\r\n      isAvatar: isAvatar\r\n    };\r\n\r\n    return await fetchWithAuth(`${process.env.API_URL}/Media/update-is-avatar`, {\r\n      method: \"PUT\",\r\n      body: JSON.stringify(payload),\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"PropertyService\", error, {\r\n      action: \"updatePropertyMediaIsAvatar\",\r\n      mediaId,\r\n      isAvatar,\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi cập nhật ảnh đại diện\");\r\n  }\r\n}\r\n\r\n// Delete property media\r\nexport async function deletePropertyMedia(mediaId) {\r\n  try {\r\n    return await fetchWithAuth(`${process.env.API_URL}/Media/${mediaId}`, {\r\n      method: \"DELETE\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"PropertyService\", error, {\r\n      action: \"deletePropertyMedia\",\r\n      mediaId,\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi xóa hình ảnh\");\r\n  }\r\n}\r\n\r\n/**\r\n * Bulk delete multiple properties\r\n * @param {Array<string>} propertyIds - Array of property IDs to delete\r\n * @returns {Promise<Object>} Response with success/error information\r\n */\r\nexport async function bulkDeleteProperties(propertyIds) {\r\n  try {\r\n    return await fetchWithAuth(`${API_BASE_URL}/bulk`, {\r\n      method: \"DELETE\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      body: JSON.stringify({\r\n        propertyIds: propertyIds\r\n      }),\r\n    });\r\n  } catch (error) {\r\n    console.error(\"Lỗi khi xóa nhiều bất động sản:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi xóa nhiều bất động sản\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Bulk update status for multiple properties\r\n * @param {Array<string>} propertyIds - Array of property IDs to update\r\n * @param {string} status - New status to set\r\n * @param {string} comment - Optional comment for the status update\r\n * @returns {Promise<Object>} Response with success/error information\r\n */\r\nexport async function bulkUpdatePropertyStatus(propertyIds, status, comment = \"\") {\r\n  try {\r\n    return await fetchWithAuth(`${API_BASE_URL}/bulk/status`, {\r\n      method: \"PUT\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      body: JSON.stringify({\r\n        propertyIds: propertyIds,\r\n        status: status,\r\n        comment: comment\r\n      }),\r\n    });\r\n  } catch (error) {\r\n    console.error(\"Lỗi khi cập nhật trạng thái nhiều bất động sản:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi cập nhật trạng thái nhiều bất động sản\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Update highlight status for a property\r\n * @param {string} propertyId - ID of the property to update\r\n * @param {boolean} isHighlighted - Whether the property should be highlighted\r\n * @returns {Promise<Object>} Response with success/error information\r\n */\r\nexport async function updatePropertyHighlight(propertyId, isHighlighted) {\r\n  try {\r\n    return await fetchWithAuth(`${API_BASE_URL}/${propertyId}/highlight`, {\r\n      method: \"PUT\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      body: JSON.stringify({\r\n        isHighlighted: isHighlighted\r\n      }),\r\n    });\r\n  } catch (error) {\r\n    console.error(\"Lỗi khi cập nhật trạng thái nổi bật của bất động sản:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi cập nhật trạng thái nổi bật của bất động sản\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Bulk update highlight status for multiple properties\r\n * @param {Array<string>} propertyIds - Array of property IDs to update\r\n * @param {boolean} isHighlighted - Whether the properties should be highlighted\r\n * @returns {Promise<Object>} Response with success/error information\r\n */\r\nexport async function bulkUpdatePropertyHighlight(propertyIds, isHighlighted) {\r\n  try {\r\n    return await fetchWithAuth(`${API_BASE_URL}/bulk/highlight`, {\r\n      method: \"PUT\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      body: JSON.stringify({\r\n        propertyIds: propertyIds,\r\n        isHighlighted: isHighlighted\r\n      }),\r\n    });\r\n  } catch (error) {\r\n    console.error(\"Lỗi khi cập nhật trạng thái nổi bật của nhiều bất động sản:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi cập nhật trạng thái nổi bật của nhiều bất động sản\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;;IA0QsB,uBAAA,WAAA,GAAA,CAAA,GAAA,sNAAA,CAAA,wBAAA,EAAA,8CAAA,sNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,sNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 59, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/app/actions/server/property.jsx"], "sourcesContent": ["\"use server\";\r\n\r\nimport { handleErrorResponse, logError } from \"@/lib/apiUtils\";\r\nimport { fetchWithAuth, getSession, fetchWithoutAuth } from \"@/lib/sessionUtils\";\r\nimport { parseEmptyStringsToNull } from \"@/lib/utils\";\r\n\r\nconst API_BASE_URL = `${process.env.API_URL}/api/Property`;\r\nconst PROPERTY_ANALYTICS_API_BASE_URL = `${process.env.API_URL}/api/PropertyAnalytics`;\r\n\r\n// New function to search properties with filters\r\nexport async function searchProperties(filterCriteria) {\r\n  try {\r\n    // Build query parameters from filter criteria\r\n    const queryParams = new URLSearchParams();\r\n\r\n    // Transaction Type (postType)\r\n    if (filterCriteria.transactionType && filterCriteria.transactionType.length > 0) {\r\n      filterCriteria.transactionType.forEach(type => {\r\n        queryParams.append('postType', type);\r\n      });\r\n    }\r\n\r\n    // Property Type\r\n    if (filterCriteria.propertyType && filterCriteria.propertyType.length > 0) {\r\n      filterCriteria.propertyType.forEach(type => {\r\n        queryParams.append('propertyType', type);\r\n      });\r\n    }\r\n\r\n    // Location\r\n    if (filterCriteria.location) {\r\n      if (filterCriteria.location.province) {\r\n        queryParams.append('cityId', filterCriteria.location.province);\r\n      }\r\n      if (filterCriteria.location.district) {\r\n        queryParams.append('districtId', filterCriteria.location.district);\r\n      }\r\n      if (filterCriteria.location.address) {\r\n        queryParams.append('address', filterCriteria.location.address);\r\n      }\r\n    }\r\n\r\n    // Price Range\r\n    if (filterCriteria.priceRange) {\r\n      if (filterCriteria.priceRange.min) {\r\n        queryParams.append('minPrice', filterCriteria.priceRange.min);\r\n      }\r\n      if (filterCriteria.priceRange.max) {\r\n        queryParams.append('maxPrice', filterCriteria.priceRange.max);\r\n      }\r\n    }\r\n\r\n    // Area Range\r\n    if (filterCriteria.areaRange) {\r\n      if (filterCriteria.areaRange.min) {\r\n        queryParams.append('minArea', filterCriteria.areaRange.min);\r\n      }\r\n      if (filterCriteria.areaRange.max) {\r\n        queryParams.append('maxArea', filterCriteria.areaRange.max);\r\n      }\r\n    }\r\n\r\n    // Bedrooms\r\n    if (filterCriteria.bedrooms) {\r\n      queryParams.append('minRooms', filterCriteria.bedrooms);\r\n    }\r\n\r\n    // Bathrooms\r\n    if (filterCriteria.bathrooms) {\r\n      queryParams.append('minToilets', filterCriteria.bathrooms);\r\n    }\r\n\r\n    // Direction\r\n    if (filterCriteria.direction) {\r\n      queryParams.append('direction', filterCriteria.direction);\r\n    }\r\n\r\n    // Legal Status\r\n    if (filterCriteria.legalStatus) {\r\n      queryParams.append('legality', filterCriteria.legalStatus);\r\n    }\r\n\r\n    // Road Width\r\n    if (filterCriteria.roadWidth) {\r\n      queryParams.append('minRoadWidth', filterCriteria.roadWidth);\r\n    }\r\n\r\n    // User location for proximity search\r\n    if (filterCriteria.sw_lat && filterCriteria.sw_lng && filterCriteria.ne_lat && filterCriteria.ne_lng) {\r\n      queryParams.append('swLat', filterCriteria.sw_lat);\r\n      queryParams.append('swLng', filterCriteria.sw_lng);\r\n      queryParams.append('neLat', filterCriteria.ne_lat);\r\n      queryParams.append('neLng', filterCriteria.ne_lng);\r\n\r\n    }\r\n\r\n    // Build the URL with query parameters\r\n    const url = `${API_BASE_URL}/search?${queryParams.toString()}`;\r\n\r\n    // Use fetchWithoutAuth for public API call\r\n    const response = await fetchWithoutAuth(url, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n\r\n    return response; // fetchWithoutAuth already returns the standard response format\r\n\r\n  } catch (error) {\r\n    // Handle errors outside fetchWithoutAuth\r\n    console.error(`Lỗi khi gọi fetchWithoutAuth cho searchProperties:`, error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi không mong muốn khi tìm kiếm bất động sản\",\r\n      errorType: \"internal_error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function getPropertyById(propertyId) {\r\n  const url = `${API_BASE_URL}/${propertyId}`;\r\n  try {\r\n    // Use fetchWithoutAuth for public API call\r\n    const response = await fetchWithoutAuth(url, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n\r\n    return response; // fetchWithoutAuth already returns the standard { success, data, message, errorType } object\r\n\r\n  } catch (error) {\r\n    // This catch block might handle errors occurring outside the fetchWithoutAuth call itself\r\n    console.error(`Lỗi khi gọi fetchWithoutAuth cho ${url}:`, error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi không mong muốn khi lấy thông tin bất động sản\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function updatePropertyById(prevState, formData) {\r\n  try {\r\n    // Get token from formData\r\n    const propertyId = formData.get(\"propertyId\");\r\n\r\n    // Convert FormData to a plain object for easier handling\r\n    const formDataObject = Object.fromEntries(formData.entries());\r\n    const payload = {\r\n      ...formDataObject,\r\n    };\r\n\r\n    const userSession = await getSession(\"UserProfile\");\r\n    if (userSession) {\r\n      const user = JSON.parse(userSession);\r\n      payload.ownerId = user.id;\r\n    }\r\n\r\n    // ✅ Parse the uploadedFiles JSON string back into an array\r\n    if (formDataObject.UploadedFiles) {\r\n      payload.UploadedFiles = JSON.parse(formDataObject.UploadedFiles);\r\n    }\r\n\r\n    payload.isHighlighted = formDataObject.isHighlighted === \"true\" ? true : false;\r\n    payload.isAutoRenew = formDataObject.isAutoRenew === \"true\" ? true : false;\r\n\r\n    return await fetchWithAuth(`${API_BASE_URL}/${propertyId}`, {\r\n      method: \"PUT\",\r\n      body: JSON.stringify(parseEmptyStringsToNull(payload)),\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n        \"accept\": \"*/*\"\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"PropertyService\", error, {\r\n      action: \"createProperty\",\r\n      formData,\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi tạo bất động sản\");\r\n  }\r\n}\r\n\r\nexport async function deletePropertyById(propertyId) {\r\n  try {\r\n    return await fetchWithAuth(`${API_BASE_URL}/${propertyId}`, {\r\n      method: \"DELETE\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    console.error(\"Lỗi khi xóa bất động sản:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi xóa bất động sản\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function getAllProperties() {\r\n  try {\r\n    // Use fetchWithoutAuth for public API call\r\n    const response = await fetchWithoutAuth(API_BASE_URL, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n\r\n    return response; // fetchWithoutAuth already returns the standard response format\r\n\r\n  } catch (error) {\r\n    // Handle errors outside fetchWithoutAuth\r\n    console.error(`Lỗi khi gọi fetchWithoutAuth cho getAllProperties:`, error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi không mong muốn khi lấy danh sách bất động sản\",\r\n      errorType: \"internal_error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function createProperty(prevState, formData) {\r\n  try {\r\n    // Convert FormData to a plain object for easier handling\r\n    const formDataObject = Object.fromEntries(formData.entries());\r\n\r\n    const payload = {\r\n      ...formDataObject,\r\n    };\r\n\r\n    const userSession = await getSession(\"UserProfile\");\r\n    if (userSession) {\r\n      const user = JSON.parse(userSession);\r\n      payload.ownerId = user.id;\r\n    }\r\n\r\n    // ✅ Parse the uploadedFiles JSON string back into an array\r\n    if (formDataObject.UploadedFiles) {\r\n      payload.UploadedFiles = JSON.parse(formDataObject.UploadedFiles);\r\n    }\r\n\r\n    payload.isHighlighted = formDataObject.isHighlighted === \"true\" ? true : false;\r\n    payload.isAutoRenew = formDataObject.isAutoRenew === \"true\" ? true : false;\r\n\r\n    return await fetchWithAuth(API_BASE_URL, {\r\n      method: \"POST\",\r\n      body: JSON.stringify(parseEmptyStringsToNull(payload)),\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"PropertyService\", error, {\r\n      action: \"createProperty\",\r\n      formData,\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi tạo bất động sản\");\r\n  }\r\n}\r\n\r\nexport async function updatePropertyStatus(formData) {\r\n  try {\r\n    const propertyId = formData.get(\"propertyId\");\r\n\r\n    return await fetchWithAuth(`${API_BASE_URL}/${propertyId}/status`, {\r\n      method: \"PUT\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      body: JSON.stringify({\r\n        status: formData.get(\"status\"),\r\n        comment: formData.get(\"comment\") || \"\"\r\n      }),\r\n    });\r\n  } catch (error) {\r\n    logError(\"PropertyService\", error, {\r\n      action: \"updatePropertyStatus\",\r\n      formData,\r\n    });\r\n    return handleErrorResponse(false, null, \"Không thể cập nhật trạng thái bất động sản\");\r\n  }\r\n}\r\n\r\nexport async function getPropertyByUser(status, page = 1, pageSize = 10) {\r\n  try {\r\n    // If status is 'counts', call the stats endpoint instead\r\n    if (status === 'counts') {\r\n      return await getPropertyStats();\r\n    }\r\n\r\n    let url = `${API_BASE_URL}/me`;\r\n    const queryParams = new URLSearchParams();\r\n\r\n    // Add parameters if provided\r\n    if (status) {\r\n      queryParams.append('status', status);\r\n    }\r\n\r\n    queryParams.append('page', page);\r\n    queryParams.append('pageSize', pageSize);\r\n\r\n    // Append query parameters to URL if any exist\r\n    if (queryParams.toString()) {\r\n      url += `?${queryParams.toString()}`;\r\n    }\r\n\r\n    const response = await fetchWithAuth(url, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n\r\n    return response;\r\n  } catch (error) {\r\n    console.error(\"Lỗi khi lấy thông tin bất động sản của người dùng:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi lấy thông tin bất động sản của người dùng\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Get property statistics by status\r\n * @returns {Promise<Object>} Response with property stats data\r\n */\r\nexport async function getPropertyStats() {\r\n  try {\r\n    const response = await fetchWithAuth(`${API_BASE_URL}/stats`, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n\r\n    if (response.success) {\r\n      // Transform the response to match the expected format for the UI\r\n      const statsData = response.data;\r\n      return {\r\n        success: true,\r\n        data: {\r\n          total: statsData.totalProperties || 0,\r\n          approved: statsData.propertiesByStatus?.Approved || 0,\r\n          pendingApproval: statsData.propertiesByStatus?.PendingApproval || 0,\r\n          rejectedByAdmin: statsData.propertiesByStatus?.RejectedByAdmin || 0,\r\n          rejectedDueToUnpaid: statsData.propertiesByStatus?.RejectedDueToUnpaid || 0,\r\n          waitingPayment: statsData.propertiesByStatus?.WaitingPayment || 0,\r\n          expired: statsData.propertiesByStatus?.Expired || 0,\r\n          draft: statsData.propertiesByStatus?.Draft || 0,\r\n          sold: statsData.propertiesByStatus?.Sold || 0,\r\n        }\r\n      };\r\n    }\r\n\r\n    return response;\r\n  } catch (error) {\r\n    console.error(\"Lỗi khi lấy thống kê bất động sản:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi lấy thống kê bất động sản\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function uploadPropertyImages(prevState, formData) {\r\n  try {\r\n    const files = formData.getAll(\"files\"); // Get all files from FormData\r\n    const propertyId = formData.get(\"propertyId\");\r\n\r\n    if (files.length === 0) {\r\n      return handleErrorResponse(false, null, \"Thiếu tập tin\");\r\n    }\r\n\r\n    const formDataToSend = new FormData();\r\n    files.forEach((file) => formDataToSend.append(\"files\", file));\r\n    formDataToSend.append(\"propertyId\", propertyId);\r\n\r\n    return await fetchWithAuth(`${API_BASE_URL}/upload-images`, {\r\n      method: \"POST\",\r\n      body: formDataToSend,\r\n    });\r\n  } catch (error) {\r\n    logError(\"PropertyService\", error, { action: \"uploadPropertyImages\", formData });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi tải lên hình ảnh\");\r\n  }\r\n}\r\n\r\nexport async function verifyPropertyRemainingTimes(propertyId) {\r\n  try {\r\n    return await fetchWithAuth(`${API_BASE_URL}/edit-remaining/${propertyId}`, {\r\n      method: \"GET\",\r\n    });\r\n  } catch (error) {\r\n    console.error(\"Error checking remaining verification times:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi kiểm tra số lần xác thực.\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function getPropertyStatusHistory(propertyId) {\r\n  try {\r\n    return await fetchWithAuth(`${PROPERTY_ANALYTICS_API_BASE_URL}/history/status/${propertyId}`, {\r\n      method: \"GET\",\r\n    });\r\n  } catch (error) {\r\n    console.error(\"Error fetching property history:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi lấy lịch sử hoạt động\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function getNearbyProperties(latitude, longitude, radius = 5000) {\r\n  try {\r\n    // Use fetchWithoutAuth for public API call\r\n    return await fetchWithoutAuth(\r\n      `${API_BASE_URL}/nearby?latitude=${latitude}&longitude=${longitude}&radius=${radius}`, {\r\n        method: \"GET\",\r\n      }\r\n    );\r\n  } catch (error) {\r\n    console.error(\"Error fetching nearby properties:\", error);\r\n    return {\r\n        success: false,\r\n        message: \"Đã xảy ra lỗi khi tìm kiếm bất động sản lân cận\",\r\n        errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\n// New function to get property report data (Placeholder)\r\nexport async function getPropertyReportById(propertyId) {\r\n  \"use server\"; // Ensure this runs on the server\r\n\r\n  // Simulate API call delay\r\n  await new Promise(resolve => setTimeout(resolve, 750));\r\n\r\n  try {\r\n    // --- FAKE DATA ---\r\n    // In a real scenario, you would fetch this from your API:\r\n    // const response = await fetchWithAuth(`${API_BASE_URL}/report/${propertyId}`);\r\n    // if (!response.success) return response;\r\n    // const reportData = response.data;\r\n\r\n    // For now, generate some fake data:\r\n    const fakeReportData = {\r\n      views: 30 + Math.floor(Math.random() * 100),           // Lượt xem\r\n      impressions: 33 + Math.floor(Math.random() * 150),    // Lượt hiển thị\r\n      cartAdds: 5 + Math.floor(Math.random() * 40),         // Lượt bỏ vào giỏ hàng (Example)\r\n      contactRequests: 10 + Math.floor(Math.random() * 20), // Lượt liên hệ (Example - added based on previous comment)\r\n      highlightsCount: 1 + Math.floor(Math.random() * 15),  // Số lần highlight\r\n      renewalsCount: 0 + Math.floor(Math.random() * 5),     // Số lần gia hạn\r\n      postCost: 55000,                                      // Tiền bài đăng\r\n      highlightCost: (1 + Math.floor(Math.random() * 15)) * 40000, // Tổng tiền highlight\r\n      renewalCost: (0 + Math.floor(Math.random() * 5)) * 300000, // Tổng tiền gia hạn\r\n    };\r\n    fakeReportData.totalCost = fakeReportData.postCost + fakeReportData.highlightCost + fakeReportData.renewalCost;\r\n\r\n    return {\r\n      success: true,\r\n      data: fakeReportData,\r\n    };\r\n\r\n  } catch (error) {\r\n    console.error(`Error fetching report for propertyId ${propertyId}:`, error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi lấy dữ liệu báo cáo.\",\r\n      errorType: \"internal_error\",\r\n    };\r\n  }\r\n}\r\n\r\n// Update property media caption\r\nexport async function updatePropertyMediaCaption(mediaId, caption) {\r\n  try {\r\n    const payload = {\r\n      id: mediaId,\r\n      caption: caption\r\n    };\r\n\r\n    return await fetchWithAuth(`${process.env.API_URL}/Media/update-caption`, {\r\n      method: \"PUT\",\r\n      body: JSON.stringify(payload),\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"PropertyService\", error, {\r\n      action: \"updatePropertyMediaCaption\",\r\n      mediaId,\r\n      caption,\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi cập nhật chú thích hình ảnh\");\r\n  }\r\n}\r\n\r\n// Update property media isAvatar status\r\nexport async function updatePropertyMediaIsAvatar(mediaId, isAvatar) {\r\n  try {\r\n    const payload = {\r\n      id: mediaId,\r\n      isAvatar: isAvatar\r\n    };\r\n\r\n    return await fetchWithAuth(`${process.env.API_URL}/Media/update-is-avatar`, {\r\n      method: \"PUT\",\r\n      body: JSON.stringify(payload),\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"PropertyService\", error, {\r\n      action: \"updatePropertyMediaIsAvatar\",\r\n      mediaId,\r\n      isAvatar,\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi cập nhật ảnh đại diện\");\r\n  }\r\n}\r\n\r\n// Delete property media\r\nexport async function deletePropertyMedia(mediaId) {\r\n  try {\r\n    return await fetchWithAuth(`${process.env.API_URL}/Media/${mediaId}`, {\r\n      method: \"DELETE\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"PropertyService\", error, {\r\n      action: \"deletePropertyMedia\",\r\n      mediaId,\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi xóa hình ảnh\");\r\n  }\r\n}\r\n\r\n/**\r\n * Bulk delete multiple properties\r\n * @param {Array<string>} propertyIds - Array of property IDs to delete\r\n * @returns {Promise<Object>} Response with success/error information\r\n */\r\nexport async function bulkDeleteProperties(propertyIds) {\r\n  try {\r\n    return await fetchWithAuth(`${API_BASE_URL}/bulk`, {\r\n      method: \"DELETE\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      body: JSON.stringify({\r\n        propertyIds: propertyIds\r\n      }),\r\n    });\r\n  } catch (error) {\r\n    console.error(\"Lỗi khi xóa nhiều bất động sản:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi xóa nhiều bất động sản\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Bulk update status for multiple properties\r\n * @param {Array<string>} propertyIds - Array of property IDs to update\r\n * @param {string} status - New status to set\r\n * @param {string} comment - Optional comment for the status update\r\n * @returns {Promise<Object>} Response with success/error information\r\n */\r\nexport async function bulkUpdatePropertyStatus(propertyIds, status, comment = \"\") {\r\n  try {\r\n    return await fetchWithAuth(`${API_BASE_URL}/bulk/status`, {\r\n      method: \"PUT\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      body: JSON.stringify({\r\n        propertyIds: propertyIds,\r\n        status: status,\r\n        comment: comment\r\n      }),\r\n    });\r\n  } catch (error) {\r\n    console.error(\"Lỗi khi cập nhật trạng thái nhiều bất động sản:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi cập nhật trạng thái nhiều bất động sản\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Update highlight status for a property\r\n * @param {string} propertyId - ID of the property to update\r\n * @param {boolean} isHighlighted - Whether the property should be highlighted\r\n * @returns {Promise<Object>} Response with success/error information\r\n */\r\nexport async function updatePropertyHighlight(propertyId, isHighlighted) {\r\n  try {\r\n    return await fetchWithAuth(`${API_BASE_URL}/${propertyId}/highlight`, {\r\n      method: \"PUT\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      body: JSON.stringify({\r\n        isHighlighted: isHighlighted\r\n      }),\r\n    });\r\n  } catch (error) {\r\n    console.error(\"Lỗi khi cập nhật trạng thái nổi bật của bất động sản:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi cập nhật trạng thái nổi bật của bất động sản\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Bulk update highlight status for multiple properties\r\n * @param {Array<string>} propertyIds - Array of property IDs to update\r\n * @param {boolean} isHighlighted - Whether the properties should be highlighted\r\n * @returns {Promise<Object>} Response with success/error information\r\n */\r\nexport async function bulkUpdatePropertyHighlight(propertyIds, isHighlighted) {\r\n  try {\r\n    return await fetchWithAuth(`${API_BASE_URL}/bulk/highlight`, {\r\n      method: \"PUT\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      body: JSON.stringify({\r\n        propertyIds: propertyIds,\r\n        isHighlighted: isHighlighted\r\n      }),\r\n    });\r\n  } catch (error) {\r\n    console.error(\"Lỗi khi cập nhật trạng thái nổi bật của nhiều bất động sản:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi cập nhật trạng thái nổi bật của nhiều bất động sản\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;;IAgjBsB,uBAAA,WAAA,GAAA,CAAA,GAAA,sNAAA,CAAA,wBAAA,EAAA,8CAAA,sNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,sNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 72, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/app/actions/server/property.jsx"], "sourcesContent": ["\"use server\";\r\n\r\nimport { handleErrorResponse, logError } from \"@/lib/apiUtils\";\r\nimport { fetchWithAuth, getSession, fetchWithoutAuth } from \"@/lib/sessionUtils\";\r\nimport { parseEmptyStringsToNull } from \"@/lib/utils\";\r\n\r\nconst API_BASE_URL = `${process.env.API_URL}/api/Property`;\r\nconst PROPERTY_ANALYTICS_API_BASE_URL = `${process.env.API_URL}/api/PropertyAnalytics`;\r\n\r\n// New function to search properties with filters\r\nexport async function searchProperties(filterCriteria) {\r\n  try {\r\n    // Build query parameters from filter criteria\r\n    const queryParams = new URLSearchParams();\r\n\r\n    // Transaction Type (postType)\r\n    if (filterCriteria.transactionType && filterCriteria.transactionType.length > 0) {\r\n      filterCriteria.transactionType.forEach(type => {\r\n        queryParams.append('postType', type);\r\n      });\r\n    }\r\n\r\n    // Property Type\r\n    if (filterCriteria.propertyType && filterCriteria.propertyType.length > 0) {\r\n      filterCriteria.propertyType.forEach(type => {\r\n        queryParams.append('propertyType', type);\r\n      });\r\n    }\r\n\r\n    // Location\r\n    if (filterCriteria.location) {\r\n      if (filterCriteria.location.province) {\r\n        queryParams.append('cityId', filterCriteria.location.province);\r\n      }\r\n      if (filterCriteria.location.district) {\r\n        queryParams.append('districtId', filterCriteria.location.district);\r\n      }\r\n      if (filterCriteria.location.address) {\r\n        queryParams.append('address', filterCriteria.location.address);\r\n      }\r\n    }\r\n\r\n    // Price Range\r\n    if (filterCriteria.priceRange) {\r\n      if (filterCriteria.priceRange.min) {\r\n        queryParams.append('minPrice', filterCriteria.priceRange.min);\r\n      }\r\n      if (filterCriteria.priceRange.max) {\r\n        queryParams.append('maxPrice', filterCriteria.priceRange.max);\r\n      }\r\n    }\r\n\r\n    // Area Range\r\n    if (filterCriteria.areaRange) {\r\n      if (filterCriteria.areaRange.min) {\r\n        queryParams.append('minArea', filterCriteria.areaRange.min);\r\n      }\r\n      if (filterCriteria.areaRange.max) {\r\n        queryParams.append('maxArea', filterCriteria.areaRange.max);\r\n      }\r\n    }\r\n\r\n    // Bedrooms\r\n    if (filterCriteria.bedrooms) {\r\n      queryParams.append('minRooms', filterCriteria.bedrooms);\r\n    }\r\n\r\n    // Bathrooms\r\n    if (filterCriteria.bathrooms) {\r\n      queryParams.append('minToilets', filterCriteria.bathrooms);\r\n    }\r\n\r\n    // Direction\r\n    if (filterCriteria.direction) {\r\n      queryParams.append('direction', filterCriteria.direction);\r\n    }\r\n\r\n    // Legal Status\r\n    if (filterCriteria.legalStatus) {\r\n      queryParams.append('legality', filterCriteria.legalStatus);\r\n    }\r\n\r\n    // Road Width\r\n    if (filterCriteria.roadWidth) {\r\n      queryParams.append('minRoadWidth', filterCriteria.roadWidth);\r\n    }\r\n\r\n    // User location for proximity search\r\n    if (filterCriteria.sw_lat && filterCriteria.sw_lng && filterCriteria.ne_lat && filterCriteria.ne_lng) {\r\n      queryParams.append('swLat', filterCriteria.sw_lat);\r\n      queryParams.append('swLng', filterCriteria.sw_lng);\r\n      queryParams.append('neLat', filterCriteria.ne_lat);\r\n      queryParams.append('neLng', filterCriteria.ne_lng);\r\n\r\n    }\r\n\r\n    // Build the URL with query parameters\r\n    const url = `${API_BASE_URL}/search?${queryParams.toString()}`;\r\n\r\n    // Use fetchWithoutAuth for public API call\r\n    const response = await fetchWithoutAuth(url, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n\r\n    return response; // fetchWithoutAuth already returns the standard response format\r\n\r\n  } catch (error) {\r\n    // Handle errors outside fetchWithoutAuth\r\n    console.error(`Lỗi khi gọi fetchWithoutAuth cho searchProperties:`, error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi không mong muốn khi tìm kiếm bất động sản\",\r\n      errorType: \"internal_error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function getPropertyById(propertyId) {\r\n  const url = `${API_BASE_URL}/${propertyId}`;\r\n  try {\r\n    // Use fetchWithoutAuth for public API call\r\n    const response = await fetchWithoutAuth(url, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n\r\n    return response; // fetchWithoutAuth already returns the standard { success, data, message, errorType } object\r\n\r\n  } catch (error) {\r\n    // This catch block might handle errors occurring outside the fetchWithoutAuth call itself\r\n    console.error(`Lỗi khi gọi fetchWithoutAuth cho ${url}:`, error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi không mong muốn khi lấy thông tin bất động sản\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function updatePropertyById(prevState, formData) {\r\n  try {\r\n    // Get token from formData\r\n    const propertyId = formData.get(\"propertyId\");\r\n\r\n    // Convert FormData to a plain object for easier handling\r\n    const formDataObject = Object.fromEntries(formData.entries());\r\n    const payload = {\r\n      ...formDataObject,\r\n    };\r\n\r\n    const userSession = await getSession(\"UserProfile\");\r\n    if (userSession) {\r\n      const user = JSON.parse(userSession);\r\n      payload.ownerId = user.id;\r\n    }\r\n\r\n    // ✅ Parse the uploadedFiles JSON string back into an array\r\n    if (formDataObject.UploadedFiles) {\r\n      payload.UploadedFiles = JSON.parse(formDataObject.UploadedFiles);\r\n    }\r\n\r\n    payload.isHighlighted = formDataObject.isHighlighted === \"true\" ? true : false;\r\n    payload.isAutoRenew = formDataObject.isAutoRenew === \"true\" ? true : false;\r\n\r\n    return await fetchWithAuth(`${API_BASE_URL}/${propertyId}`, {\r\n      method: \"PUT\",\r\n      body: JSON.stringify(parseEmptyStringsToNull(payload)),\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n        \"accept\": \"*/*\"\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"PropertyService\", error, {\r\n      action: \"createProperty\",\r\n      formData,\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi tạo bất động sản\");\r\n  }\r\n}\r\n\r\nexport async function deletePropertyById(propertyId) {\r\n  try {\r\n    return await fetchWithAuth(`${API_BASE_URL}/${propertyId}`, {\r\n      method: \"DELETE\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    console.error(\"Lỗi khi xóa bất động sản:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi xóa bất động sản\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function getAllProperties() {\r\n  try {\r\n    // Use fetchWithoutAuth for public API call\r\n    const response = await fetchWithoutAuth(API_BASE_URL, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n\r\n    return response; // fetchWithoutAuth already returns the standard response format\r\n\r\n  } catch (error) {\r\n    // Handle errors outside fetchWithoutAuth\r\n    console.error(`Lỗi khi gọi fetchWithoutAuth cho getAllProperties:`, error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi không mong muốn khi lấy danh sách bất động sản\",\r\n      errorType: \"internal_error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function createProperty(prevState, formData) {\r\n  try {\r\n    // Convert FormData to a plain object for easier handling\r\n    const formDataObject = Object.fromEntries(formData.entries());\r\n\r\n    const payload = {\r\n      ...formDataObject,\r\n    };\r\n\r\n    const userSession = await getSession(\"UserProfile\");\r\n    if (userSession) {\r\n      const user = JSON.parse(userSession);\r\n      payload.ownerId = user.id;\r\n    }\r\n\r\n    // ✅ Parse the uploadedFiles JSON string back into an array\r\n    if (formDataObject.UploadedFiles) {\r\n      payload.UploadedFiles = JSON.parse(formDataObject.UploadedFiles);\r\n    }\r\n\r\n    payload.isHighlighted = formDataObject.isHighlighted === \"true\" ? true : false;\r\n    payload.isAutoRenew = formDataObject.isAutoRenew === \"true\" ? true : false;\r\n\r\n    return await fetchWithAuth(API_BASE_URL, {\r\n      method: \"POST\",\r\n      body: JSON.stringify(parseEmptyStringsToNull(payload)),\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"PropertyService\", error, {\r\n      action: \"createProperty\",\r\n      formData,\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi tạo bất động sản\");\r\n  }\r\n}\r\n\r\nexport async function updatePropertyStatus(formData) {\r\n  try {\r\n    const propertyId = formData.get(\"propertyId\");\r\n\r\n    return await fetchWithAuth(`${API_BASE_URL}/${propertyId}/status`, {\r\n      method: \"PUT\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      body: JSON.stringify({\r\n        status: formData.get(\"status\"),\r\n        comment: formData.get(\"comment\") || \"\"\r\n      }),\r\n    });\r\n  } catch (error) {\r\n    logError(\"PropertyService\", error, {\r\n      action: \"updatePropertyStatus\",\r\n      formData,\r\n    });\r\n    return handleErrorResponse(false, null, \"Không thể cập nhật trạng thái bất động sản\");\r\n  }\r\n}\r\n\r\nexport async function getPropertyByUser(status, page = 1, pageSize = 10) {\r\n  try {\r\n    // If status is 'counts', call the stats endpoint instead\r\n    if (status === 'counts') {\r\n      return await getPropertyStats();\r\n    }\r\n\r\n    let url = `${API_BASE_URL}/me`;\r\n    const queryParams = new URLSearchParams();\r\n\r\n    // Add parameters if provided\r\n    if (status) {\r\n      queryParams.append('status', status);\r\n    }\r\n\r\n    queryParams.append('page', page);\r\n    queryParams.append('pageSize', pageSize);\r\n\r\n    // Append query parameters to URL if any exist\r\n    if (queryParams.toString()) {\r\n      url += `?${queryParams.toString()}`;\r\n    }\r\n\r\n    const response = await fetchWithAuth(url, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n\r\n    return response;\r\n  } catch (error) {\r\n    console.error(\"Lỗi khi lấy thông tin bất động sản của người dùng:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi lấy thông tin bất động sản của người dùng\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Get property statistics by status\r\n * @returns {Promise<Object>} Response with property stats data\r\n */\r\nexport async function getPropertyStats() {\r\n  try {\r\n    const response = await fetchWithAuth(`${API_BASE_URL}/stats`, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n\r\n    if (response.success) {\r\n      // Transform the response to match the expected format for the UI\r\n      const statsData = response.data;\r\n      return {\r\n        success: true,\r\n        data: {\r\n          total: statsData.totalProperties || 0,\r\n          approved: statsData.propertiesByStatus?.Approved || 0,\r\n          pendingApproval: statsData.propertiesByStatus?.PendingApproval || 0,\r\n          rejectedByAdmin: statsData.propertiesByStatus?.RejectedByAdmin || 0,\r\n          rejectedDueToUnpaid: statsData.propertiesByStatus?.RejectedDueToUnpaid || 0,\r\n          waitingPayment: statsData.propertiesByStatus?.WaitingPayment || 0,\r\n          expired: statsData.propertiesByStatus?.Expired || 0,\r\n          draft: statsData.propertiesByStatus?.Draft || 0,\r\n          sold: statsData.propertiesByStatus?.Sold || 0,\r\n        }\r\n      };\r\n    }\r\n\r\n    return response;\r\n  } catch (error) {\r\n    console.error(\"Lỗi khi lấy thống kê bất động sản:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi lấy thống kê bất động sản\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function uploadPropertyImages(prevState, formData) {\r\n  try {\r\n    const files = formData.getAll(\"files\"); // Get all files from FormData\r\n    const propertyId = formData.get(\"propertyId\");\r\n\r\n    if (files.length === 0) {\r\n      return handleErrorResponse(false, null, \"Thiếu tập tin\");\r\n    }\r\n\r\n    const formDataToSend = new FormData();\r\n    files.forEach((file) => formDataToSend.append(\"files\", file));\r\n    formDataToSend.append(\"propertyId\", propertyId);\r\n\r\n    return await fetchWithAuth(`${API_BASE_URL}/upload-images`, {\r\n      method: \"POST\",\r\n      body: formDataToSend,\r\n    });\r\n  } catch (error) {\r\n    logError(\"PropertyService\", error, { action: \"uploadPropertyImages\", formData });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi tải lên hình ảnh\");\r\n  }\r\n}\r\n\r\nexport async function verifyPropertyRemainingTimes(propertyId) {\r\n  try {\r\n    return await fetchWithAuth(`${API_BASE_URL}/edit-remaining/${propertyId}`, {\r\n      method: \"GET\",\r\n    });\r\n  } catch (error) {\r\n    console.error(\"Error checking remaining verification times:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi kiểm tra số lần xác thực.\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function getPropertyStatusHistory(propertyId) {\r\n  try {\r\n    return await fetchWithAuth(`${PROPERTY_ANALYTICS_API_BASE_URL}/history/status/${propertyId}`, {\r\n      method: \"GET\",\r\n    });\r\n  } catch (error) {\r\n    console.error(\"Error fetching property history:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi lấy lịch sử hoạt động\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function getNearbyProperties(latitude, longitude, radius = 5000) {\r\n  try {\r\n    // Use fetchWithoutAuth for public API call\r\n    return await fetchWithoutAuth(\r\n      `${API_BASE_URL}/nearby?latitude=${latitude}&longitude=${longitude}&radius=${radius}`, {\r\n        method: \"GET\",\r\n      }\r\n    );\r\n  } catch (error) {\r\n    console.error(\"Error fetching nearby properties:\", error);\r\n    return {\r\n        success: false,\r\n        message: \"Đã xảy ra lỗi khi tìm kiếm bất động sản lân cận\",\r\n        errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\n// New function to get property report data (Placeholder)\r\nexport async function getPropertyReportById(propertyId) {\r\n  \"use server\"; // Ensure this runs on the server\r\n\r\n  // Simulate API call delay\r\n  await new Promise(resolve => setTimeout(resolve, 750));\r\n\r\n  try {\r\n    // --- FAKE DATA ---\r\n    // In a real scenario, you would fetch this from your API:\r\n    // const response = await fetchWithAuth(`${API_BASE_URL}/report/${propertyId}`);\r\n    // if (!response.success) return response;\r\n    // const reportData = response.data;\r\n\r\n    // For now, generate some fake data:\r\n    const fakeReportData = {\r\n      views: 30 + Math.floor(Math.random() * 100),           // Lượt xem\r\n      impressions: 33 + Math.floor(Math.random() * 150),    // Lượt hiển thị\r\n      cartAdds: 5 + Math.floor(Math.random() * 40),         // Lượt bỏ vào giỏ hàng (Example)\r\n      contactRequests: 10 + Math.floor(Math.random() * 20), // Lượt liên hệ (Example - added based on previous comment)\r\n      highlightsCount: 1 + Math.floor(Math.random() * 15),  // Số lần highlight\r\n      renewalsCount: 0 + Math.floor(Math.random() * 5),     // Số lần gia hạn\r\n      postCost: 55000,                                      // Tiền bài đăng\r\n      highlightCost: (1 + Math.floor(Math.random() * 15)) * 40000, // Tổng tiền highlight\r\n      renewalCost: (0 + Math.floor(Math.random() * 5)) * 300000, // Tổng tiền gia hạn\r\n    };\r\n    fakeReportData.totalCost = fakeReportData.postCost + fakeReportData.highlightCost + fakeReportData.renewalCost;\r\n\r\n    return {\r\n      success: true,\r\n      data: fakeReportData,\r\n    };\r\n\r\n  } catch (error) {\r\n    console.error(`Error fetching report for propertyId ${propertyId}:`, error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi lấy dữ liệu báo cáo.\",\r\n      errorType: \"internal_error\",\r\n    };\r\n  }\r\n}\r\n\r\n// Update property media caption\r\nexport async function updatePropertyMediaCaption(mediaId, caption) {\r\n  try {\r\n    const payload = {\r\n      id: mediaId,\r\n      caption: caption\r\n    };\r\n\r\n    return await fetchWithAuth(`${process.env.API_URL}/Media/update-caption`, {\r\n      method: \"PUT\",\r\n      body: JSON.stringify(payload),\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"PropertyService\", error, {\r\n      action: \"updatePropertyMediaCaption\",\r\n      mediaId,\r\n      caption,\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi cập nhật chú thích hình ảnh\");\r\n  }\r\n}\r\n\r\n// Update property media isAvatar status\r\nexport async function updatePropertyMediaIsAvatar(mediaId, isAvatar) {\r\n  try {\r\n    const payload = {\r\n      id: mediaId,\r\n      isAvatar: isAvatar\r\n    };\r\n\r\n    return await fetchWithAuth(`${process.env.API_URL}/Media/update-is-avatar`, {\r\n      method: \"PUT\",\r\n      body: JSON.stringify(payload),\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"PropertyService\", error, {\r\n      action: \"updatePropertyMediaIsAvatar\",\r\n      mediaId,\r\n      isAvatar,\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi cập nhật ảnh đại diện\");\r\n  }\r\n}\r\n\r\n// Delete property media\r\nexport async function deletePropertyMedia(mediaId) {\r\n  try {\r\n    return await fetchWithAuth(`${process.env.API_URL}/Media/${mediaId}`, {\r\n      method: \"DELETE\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"PropertyService\", error, {\r\n      action: \"deletePropertyMedia\",\r\n      mediaId,\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi xóa hình ảnh\");\r\n  }\r\n}\r\n\r\n/**\r\n * Bulk delete multiple properties\r\n * @param {Array<string>} propertyIds - Array of property IDs to delete\r\n * @returns {Promise<Object>} Response with success/error information\r\n */\r\nexport async function bulkDeleteProperties(propertyIds) {\r\n  try {\r\n    return await fetchWithAuth(`${API_BASE_URL}/bulk`, {\r\n      method: \"DELETE\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      body: JSON.stringify({\r\n        propertyIds: propertyIds\r\n      }),\r\n    });\r\n  } catch (error) {\r\n    console.error(\"Lỗi khi xóa nhiều bất động sản:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi xóa nhiều bất động sản\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Bulk update status for multiple properties\r\n * @param {Array<string>} propertyIds - Array of property IDs to update\r\n * @param {string} status - New status to set\r\n * @param {string} comment - Optional comment for the status update\r\n * @returns {Promise<Object>} Response with success/error information\r\n */\r\nexport async function bulkUpdatePropertyStatus(propertyIds, status, comment = \"\") {\r\n  try {\r\n    return await fetchWithAuth(`${API_BASE_URL}/bulk/status`, {\r\n      method: \"PUT\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      body: JSON.stringify({\r\n        propertyIds: propertyIds,\r\n        status: status,\r\n        comment: comment\r\n      }),\r\n    });\r\n  } catch (error) {\r\n    console.error(\"Lỗi khi cập nhật trạng thái nhiều bất động sản:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi cập nhật trạng thái nhiều bất động sản\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Update highlight status for a property\r\n * @param {string} propertyId - ID of the property to update\r\n * @param {boolean} isHighlighted - Whether the property should be highlighted\r\n * @returns {Promise<Object>} Response with success/error information\r\n */\r\nexport async function updatePropertyHighlight(propertyId, isHighlighted) {\r\n  try {\r\n    return await fetchWithAuth(`${API_BASE_URL}/${propertyId}/highlight`, {\r\n      method: \"PUT\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      body: JSON.stringify({\r\n        isHighlighted: isHighlighted\r\n      }),\r\n    });\r\n  } catch (error) {\r\n    console.error(\"Lỗi khi cập nhật trạng thái nổi bật của bất động sản:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi cập nhật trạng thái nổi bật của bất động sản\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Bulk update highlight status for multiple properties\r\n * @param {Array<string>} propertyIds - Array of property IDs to update\r\n * @param {boolean} isHighlighted - Whether the properties should be highlighted\r\n * @returns {Promise<Object>} Response with success/error information\r\n */\r\nexport async function bulkUpdatePropertyHighlight(propertyIds, isHighlighted) {\r\n  try {\r\n    return await fetchWithAuth(`${API_BASE_URL}/bulk/highlight`, {\r\n      method: \"PUT\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      body: JSON.stringify({\r\n        propertyIds: propertyIds,\r\n        isHighlighted: isHighlighted\r\n      }),\r\n    });\r\n  } catch (error) {\r\n    console.error(\"Lỗi khi cập nhật trạng thái nổi bật của nhiều bất động sản:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Đã xảy ra lỗi khi cập nhật trạng thái nổi bật của nhiều bất động sản\",\r\n      errorType: \"network_error\",\r\n    };\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;;IAooBsB,8BAAA,WAAA,GAAA,CAAA,GAAA,sNAAA,CAAA,wBAAA,EAAA,8CAAA,sNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,sNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 85, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/components/ui/checkbox.jsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as CheckboxPrimitive from \"@radix-ui/react-checkbox\"\r\nimport { Check } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Checkbox = React.forwardRef(({ className, ...props }, ref) => (\r\n  <CheckboxPrimitive.Root\r\n    ref={ref}\r\n    className={cn(\r\n      \"peer h-4 w-4 shrink-0 rounded-sm border border-primary shadow focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground\",\r\n      className\r\n    )}\r\n    {...props}>\r\n    <CheckboxPrimitive.Indicator className={cn(\"flex items-center justify-center text-current\")}>\r\n      <Check className=\"h-4 w-4\" />\r\n    </CheckboxPrimitive.Indicator>\r\n  </CheckboxPrimitive.Root>\r\n))\r\nCheckbox.displayName = CheckboxPrimitive.Root.displayName\r\n\r\nexport { Checkbox }\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AANA;;;;;;AAQA,MAAM,yBAAW,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1D,8OAAC,oKAAA,CAAA,OAAsB;QACrB,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,sQACA;QAED,GAAG,KAAK;kBACT,cAAA,8OAAC,oKAAA,CAAA,YAA2B;YAAC,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE;sBACzC,cAAA,8OAAC,oMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;;;;;;;;;;;AAIvB,SAAS,WAAW,GAAG,oKAAA,CAAA,OAAsB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 130, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/hooks/usePropertyList.js"], "sourcesContent": ["import { useState, useCallback, useMemo, useEffect, useRef } from 'react';\nimport { useToast } from '@/hooks/use-toast';\nimport {\n  getPropertyByUser,\n  deletePropertyById,\n  verifyPropertyRemainingTimes,\n  updatePropertyStatus,\n  bulkDeleteProperties,\n  bulkUpdatePropertyHighlight,\n} from '@/app/actions/server/property';\nimport { PropertyStatus } from '@/lib/enum';\n\n// Define filter keys\nconst FilterKeys = {\n  ALL: \"all\",\n  APPROVED: \"Approved\",\n  PENDING_APPROVAL: \"PendingApproval\",\n  REJECTED_BY_ADMIN: \"RejectedByAdmin\",\n  REJECTED_DUE_TO_UNPAID: \"RejectedDueToUnpaid\",\n  WAITING_PAYMENT: \"WaitingPayment\",\n  EXPIRED: \"Expired\",\n  DRAFT: \"Draft\",\n  SOLD: \"Sold\",\n};\n\nexport function usePropertyList(initialData) {\n  const [data, setData] = useState(initialData || []);\n  const [loadingId, setLoadingId] = useState(null);\n  const [isLoading, setIsLoading] = useState(false);\n  const [isLoadingFilterCounts, setIsLoadingFilterCounts] = useState(true);\n  const [isInitialized, setIsInitialized] = useState(false);\n  const [searchTerm, setSearchTerm] = useState(\"\");\n  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState(\"\");\n  const [activeFilter, setActiveFilter] = useState(FilterKeys.ALL);\n  const [selectedIds, setSelectedIds] = useState(new Set());\n  const [currentPage, setCurrentPage] = useState(1);\n  const [pageSize] = useState(10);\n  const { toast } = useToast();\n  \n  // Refs for performance optimization\n  const searchTimeoutRef = useRef(null);\n  \n  // State for filter counts - memoized initial state\n  const [filterCounts, setFilterCounts] = useState(() => ({\n    [FilterKeys.ALL]: 0,\n    [FilterKeys.APPROVED]: 0,\n    [FilterKeys.PENDING_APPROVAL]: 0,\n    [FilterKeys.REJECTED_BY_ADMIN]: 0,\n    [FilterKeys.REJECTED_DUE_TO_UNPAID]: 0,\n    [FilterKeys.WAITING_PAYMENT]: 0,\n    [FilterKeys.EXPIRED]: 0,\n    [FilterKeys.DRAFT]: 0,\n    [FilterKeys.SOLD]: 0,\n  }));\n\n  // Debounce search input\n  useEffect(() => {\n    if (searchTimeoutRef.current) {\n      clearTimeout(searchTimeoutRef.current);\n    }\n    \n    searchTimeoutRef.current = setTimeout(() => {\n      setDebouncedSearchTerm(searchTerm);\n    }, 300); // 300ms debounce\n    \n    return () => {\n      if (searchTimeoutRef.current) {\n        clearTimeout(searchTimeoutRef.current);\n      }\n    };\n  }, [searchTerm]);\n\n  // Fetch filter counts from server\n  const fetchFilterCounts = useCallback(async () => {\n    setIsLoadingFilterCounts(true);\n    try {\n      const result = await getPropertyByUser(\"counts\");\n      if (result && result?.success && result?.data) {\n        setFilterCounts({\n          [FilterKeys.ALL]: result.data.total || 0,\n          [FilterKeys.APPROVED]: result.data.approved || 0,\n          [FilterKeys.PENDING_APPROVAL]: result.data.pendingApproval || 0,\n          [FilterKeys.REJECTED_BY_ADMIN]: result.data.rejectedByAdmin || 0,\n          [FilterKeys.REJECTED_DUE_TO_UNPAID]: result.data.rejectedDueToUnpaid || 0,\n          [FilterKeys.WAITING_PAYMENT]: result.data.waitingPayment || 0,\n          [FilterKeys.EXPIRED]: result.data.expired || 0,\n          [FilterKeys.DRAFT]: result.data.draft || 0,\n          [FilterKeys.SOLD]: result.data.sold || 0,\n        });\n      }\n    } catch (error) {\n      console.error(\"Error fetching filter counts:\", error);\n      toast({\n        description: \"Failed to fetch property statistics\",\n        variant: \"destructive\",\n      });\n    } finally {\n      setIsLoadingFilterCounts(false);\n    }\n  }, [toast]);\n\n  // Data fetching logic\n  const fetchProperties = useCallback(\n    async (status = null, page = 1, size = pageSize) => {\n      setIsLoading(true);\n      try {\n        const result = await getPropertyByUser(status, page, size);\n\n        if (result.success && result.data) {\n          const propertyData = result.data.items || [];\n          setData(propertyData);\n          setCurrentPage(result.data.currentPage || page);\n          fetchFilterCounts();\n        } else {\n          console.error(\"API returned error:\", result);\n          toast({\n            description: result.message || \"Failed to fetch properties\",\n            variant: \"destructive\",\n          });\n        }\n      } catch (error) {\n        console.error(\"Error fetching properties:\", error);\n        toast({\n          description: \"An unexpected error occurred\",\n          variant: \"destructive\",\n        });\n      } finally {\n        setIsLoading(false);\n      }\n    },\n    [pageSize, toast, fetchFilterCounts]\n  );\n\n  // Initialize component - fetch filter counts and handle initial data\n  useEffect(() => {\n    const initializeComponent = async () => {\n      await fetchFilterCounts();\n      \n      if (initialData && Array.isArray(initialData) && initialData.length > 0 && activeFilter === FilterKeys.ALL) {\n        setData(initialData);\n      } else {\n        const statusParam = activeFilter !== FilterKeys.ALL ? activeFilter : null;\n        await fetchProperties(statusParam, 1, pageSize);\n      }\n      \n      setIsInitialized(true);\n    };\n\n    initializeComponent();\n  }, []); // Only run once on mount\n\n  // Handle filter changes after initialization\n  useEffect(() => {\n    if (!isInitialized) return;\n    \n    const statusParam = activeFilter !== FilterKeys.ALL ? activeFilter : null;\n    fetchProperties(statusParam, 1, pageSize);\n  }, [activeFilter, isInitialized, fetchProperties, pageSize]);\n\n  // Filtering logic for search - optimized with debounced search\n  const filteredData = useMemo(() => {\n    if (!Array.isArray(data)) {\n      console.error(\"Data is not an array:\", data);\n      return [];\n    }\n\n    if (!debouncedSearchTerm.trim()) {\n      return data;\n    }\n\n    const searchLower = debouncedSearchTerm.toLowerCase();\n    return data.filter((property) => {\n      return property && (\n        (property.name && property.name.toLowerCase().includes(searchLower)) ||\n        (property.address && property.address.toLowerCase().includes(searchLower)) ||\n        (property.addressSelected && property.addressSelected.toLowerCase().includes(searchLower))\n      );\n    });\n  }, [data, debouncedSearchTerm]);\n\n  // Memoized computed values for better performance\n  const computedValues = useMemo(() => {\n    const isOverallLoading = isLoading || isLoadingFilterCounts || !isInitialized;\n    const isAllSelected = filteredData.length > 0 && selectedIds.size === filteredData.length;\n    const isIndeterminate = selectedIds.size > 0 && selectedIds.size < filteredData.length;\n    const isEmptyDatabase = !isOverallLoading && filterCounts[FilterKeys.ALL] === 0;\n    const isEmptySearchResults = !isOverallLoading && filterCounts[FilterKeys.ALL] > 0 && (!filteredData || filteredData.length === 0);\n    const hasResults = !isOverallLoading && filteredData && filteredData.length > 0;\n    \n    return {\n      isOverallLoading,\n      isAllSelected,\n      isIndeterminate,\n      isEmptyDatabase,\n      isEmptySearchResults,\n      hasResults,\n    };\n  }, [isLoading, isLoadingFilterCounts, isInitialized, filteredData, selectedIds.size, filterCounts]);\n\n  return {\n    // State\n    data,\n    filteredData,\n    loadingId,\n    setLoadingId,\n    searchTerm,\n    setSearchTerm,\n    activeFilter,\n    setActiveFilter,\n    selectedIds,\n    setSelectedIds,\n    currentPage,\n    pageSize,\n    filterCounts,\n    \n    // Computed values\n    ...computedValues,\n    \n    // Functions\n    fetchProperties,\n    fetchFilterCounts,\n    \n    // Constants\n    FilterKeys,\n  };\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AAQA;;;;;AAEA,qBAAqB;AACrB,MAAM,aAAa;IACjB,KAAK;IACL,UAAU;IACV,kBAAkB;IAClB,mBAAmB;IACnB,wBAAwB;IACxB,iBAAiB;IACjB,SAAS;IACT,OAAO;IACP,MAAM;AACR;AAEO,SAAS,gBAAgB,WAAW;IACzC,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,eAAe,EAAE;IAClD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,uBAAuB,yBAAyB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnE,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,WAAW,GAAG;IAC/D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,IAAI;IACnD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC5B,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,qHAAA,CAAA,WAAQ,AAAD;IAEzB,oCAAoC;IACpC,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAEhC,mDAAmD;IACnD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,IAAM,CAAC;YACtD,CAAC,WAAW,GAAG,CAAC,EAAE;YAClB,CAAC,WAAW,QAAQ,CAAC,EAAE;YACvB,CAAC,WAAW,gBAAgB,CAAC,EAAE;YAC/B,CAAC,WAAW,iBAAiB,CAAC,EAAE;YAChC,CAAC,WAAW,sBAAsB,CAAC,EAAE;YACrC,CAAC,WAAW,eAAe,CAAC,EAAE;YAC9B,CAAC,WAAW,OAAO,CAAC,EAAE;YACtB,CAAC,WAAW,KAAK,CAAC,EAAE;YACpB,CAAC,WAAW,IAAI,CAAC,EAAE;QACrB,CAAC;IAED,wBAAwB;IACxB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,iBAAiB,OAAO,EAAE;YAC5B,aAAa,iBAAiB,OAAO;QACvC;QAEA,iBAAiB,OAAO,GAAG,WAAW;YACpC,uBAAuB;QACzB,GAAG,MAAM,iBAAiB;QAE1B,OAAO;YACL,IAAI,iBAAiB,OAAO,EAAE;gBAC5B,aAAa,iBAAiB,OAAO;YACvC;QACF;IACF,GAAG;QAAC;KAAW;IAEf,kCAAkC;IAClC,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACpC,yBAAyB;QACzB,IAAI;YACF,MAAM,SAAS,MAAM,CAAA,GAAA,gKAAA,CAAA,oBAAiB,AAAD,EAAE;YACvC,IAAI,UAAU,QAAQ,WAAW,QAAQ,MAAM;gBAC7C,gBAAgB;oBACd,CAAC,WAAW,GAAG,CAAC,EAAE,OAAO,IAAI,CAAC,KAAK,IAAI;oBACvC,CAAC,WAAW,QAAQ,CAAC,EAAE,OAAO,IAAI,CAAC,QAAQ,IAAI;oBAC/C,CAAC,WAAW,gBAAgB,CAAC,EAAE,OAAO,IAAI,CAAC,eAAe,IAAI;oBAC9D,CAAC,WAAW,iBAAiB,CAAC,EAAE,OAAO,IAAI,CAAC,eAAe,IAAI;oBAC/D,CAAC,WAAW,sBAAsB,CAAC,EAAE,OAAO,IAAI,CAAC,mBAAmB,IAAI;oBACxE,CAAC,WAAW,eAAe,CAAC,EAAE,OAAO,IAAI,CAAC,cAAc,IAAI;oBAC5D,CAAC,WAAW,OAAO,CAAC,EAAE,OAAO,IAAI,CAAC,OAAO,IAAI;oBAC7C,CAAC,WAAW,KAAK,CAAC,EAAE,OAAO,IAAI,CAAC,KAAK,IAAI;oBACzC,CAAC,WAAW,IAAI,CAAC,EAAE,OAAO,IAAI,CAAC,IAAI,IAAI;gBACzC;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,MAAM;gBACJ,aAAa;gBACb,SAAS;YACX;QACF,SAAU;YACR,yBAAyB;QAC3B;IACF,GAAG;QAAC;KAAM;IAEV,sBAAsB;IACtB,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAChC,OAAO,SAAS,IAAI,EAAE,OAAO,CAAC,EAAE,OAAO,QAAQ;QAC7C,aAAa;QACb,IAAI;YACF,MAAM,SAAS,MAAM,CAAA,GAAA,gKAAA,CAAA,oBAAiB,AAAD,EAAE,QAAQ,MAAM;YAErD,IAAI,OAAO,OAAO,IAAI,OAAO,IAAI,EAAE;gBACjC,MAAM,eAAe,OAAO,IAAI,CAAC,KAAK,IAAI,EAAE;gBAC5C,QAAQ;gBACR,eAAe,OAAO,IAAI,CAAC,WAAW,IAAI;gBAC1C;YACF,OAAO;gBACL,QAAQ,KAAK,CAAC,uBAAuB;gBACrC,MAAM;oBACJ,aAAa,OAAO,OAAO,IAAI;oBAC/B,SAAS;gBACX;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,MAAM;gBACJ,aAAa;gBACb,SAAS;YACX;QACF,SAAU;YACR,aAAa;QACf;IACF,GACA;QAAC;QAAU;QAAO;KAAkB;IAGtC,qEAAqE;IACrE,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,sBAAsB;YAC1B,MAAM;YAEN,IAAI,eAAe,MAAM,OAAO,CAAC,gBAAgB,YAAY,MAAM,GAAG,KAAK,iBAAiB,WAAW,GAAG,EAAE;gBAC1G,QAAQ;YACV,OAAO;gBACL,MAAM,cAAc,iBAAiB,WAAW,GAAG,GAAG,eAAe;gBACrE,MAAM,gBAAgB,aAAa,GAAG;YACxC;YAEA,iBAAiB;QACnB;QAEA;IACF,GAAG,EAAE,GAAG,yBAAyB;IAEjC,6CAA6C;IAC7C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,eAAe;QAEpB,MAAM,cAAc,iBAAiB,WAAW,GAAG,GAAG,eAAe;QACrE,gBAAgB,aAAa,GAAG;IAClC,GAAG;QAAC;QAAc;QAAe;QAAiB;KAAS;IAE3D,+DAA+D;IAC/D,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QAC3B,IAAI,CAAC,MAAM,OAAO,CAAC,OAAO;YACxB,QAAQ,KAAK,CAAC,yBAAyB;YACvC,OAAO,EAAE;QACX;QAEA,IAAI,CAAC,oBAAoB,IAAI,IAAI;YAC/B,OAAO;QACT;QAEA,MAAM,cAAc,oBAAoB,WAAW;QACnD,OAAO,KAAK,MAAM,CAAC,CAAC;YAClB,OAAO,YAAY,CACjB,AAAC,SAAS,IAAI,IAAI,SAAS,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,gBACtD,SAAS,OAAO,IAAI,SAAS,OAAO,CAAC,WAAW,GAAG,QAAQ,CAAC,gBAC5D,SAAS,eAAe,IAAI,SAAS,eAAe,CAAC,WAAW,GAAG,QAAQ,CAAC,YAC/E;QACF;IACF,GAAG;QAAC;QAAM;KAAoB;IAE9B,kDAAkD;IAClD,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QAC7B,MAAM,mBAAmB,aAAa,yBAAyB,CAAC;QAChE,MAAM,gBAAgB,aAAa,MAAM,GAAG,KAAK,YAAY,IAAI,KAAK,aAAa,MAAM;QACzF,MAAM,kBAAkB,YAAY,IAAI,GAAG,KAAK,YAAY,IAAI,GAAG,aAAa,MAAM;QACtF,MAAM,kBAAkB,CAAC,oBAAoB,YAAY,CAAC,WAAW,GAAG,CAAC,KAAK;QAC9E,MAAM,uBAAuB,CAAC,oBAAoB,YAAY,CAAC,WAAW,GAAG,CAAC,GAAG,KAAK,CAAC,CAAC,gBAAgB,aAAa,MAAM,KAAK,CAAC;QACjI,MAAM,aAAa,CAAC,oBAAoB,gBAAgB,aAAa,MAAM,GAAG;QAE9E,OAAO;YACL;YACA;YACA;YACA;YACA;YACA;QACF;IACF,GAAG;QAAC;QAAW;QAAuB;QAAe;QAAc,YAAY,IAAI;QAAE;KAAa;IAElG,OAAO;QACL,QAAQ;QACR;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QAEA,kBAAkB;QAClB,GAAG,cAAc;QAEjB,YAAY;QACZ;QACA;QAEA,YAAY;QACZ;IACF;AACF", "debugId": null}}, {"offset": {"line": 353, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/app/%5Blocale%5D/%28protected%29/user/bds/PropertyList.jsx"], "sourcesContent": ["\"use client\";\r\nimport { memo, useState, useCallback, useMemo, useEffect, useRef } from \"react\";\r\nimport { LoaderCircle, Trash2, Search, Zap, RefreshCcw } from \"lucide-react\";\r\nimport { useRouter } from \"next/navigation\";\r\nimport dynamic from \"next/dynamic\";\r\nimport { useAlert } from \"@/contexts/AlertContext\";\r\nimport { useToast } from \"@/hooks/use-toast\";\r\nimport {\r\n  deletePropertyById,\r\n  getPropertyByUser,\r\n  verifyPropertyRemainingTimes,\r\n  updatePropertyStatus,\r\n  bulkDeleteProperties,\r\n  bulkUpdatePropertyHighlight,\r\n} from \"@/app/actions/server/property\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { PropertyStatus } from \"@/lib/enum\";\r\nimport { useTranslations } from \"next-intl\";\r\nimport { Input } from \"@/components/ui/input\";\r\nimport { Checkbox } from \"@/components/ui/checkbox\";\r\nimport { LoadingSpinner } from \"@/components/ui/loading-spinner\";\r\nimport { usePropertyList } from \"@/hooks/usePropertyList\";\r\n\r\n// Lazy load heavy components\r\nconst AlertPopup = dynamic(() => import(\"@/components/layout/AlertPopup\"), {\r\n  ssr: false,\r\n});\r\nconst NoData = dynamic(() => import(\"@/components/layout/NoData\"));\r\nconst PropertyCard = dynamic(() => import(\"@/components/user-property/PropertyCard\"));\r\nconst ContactRequestModal = dynamic(() => import(\"@/components/user-property/ContactRequestModal\"), {\r\n  ssr: false,\r\n});\r\nconst HistoryModal = dynamic(() => import(\"@/components/user-property/HistoryModal\"), {\r\n  ssr: false,\r\n});\r\n\r\n// Define filter keys\r\nconst FilterKeys = {\r\n  ALL: \"all\",\r\n  APPROVED: \"Approved\",\r\n  PENDING_APPROVAL: \"PendingApproval\",\r\n  REJECTED_BY_ADMIN: \"RejectedByAdmin\",\r\n  REJECTED_DUE_TO_UNPAID: \"RejectedDueToUnpaid\",\r\n  WAITING_PAYMENT: \"WaitingPayment\",\r\n  EXPIRED: \"Expired\",\r\n  DRAFT: \"Draft\",\r\n  SOLD: \"Sold\",\r\n};\r\n\r\nfunction PropertyList({ initialData }) {\r\n  const [data, setData] = useState(initialData || []);\r\n  const [loadingId, setLoadingId] = useState(null);\r\n  const [isLoading, setIsLoading] = useState(false);\r\n  const [isLoadingFilterCounts, setIsLoadingFilterCounts] = useState(true); // Track filter counts loading\r\n  const [isInitialized, setIsInitialized] = useState(false); // Track if component is fully initialized\r\n  const router = useRouter();\r\n  const { showAlert } = useAlert();\r\n  const { toast } = useToast();\r\n  const [selectedPropertyId, setSelectedPropertyId] = useState(null);\r\n  const [isContactModalOpen, setIsContactModalOpen] = useState(false);\r\n  const [isHistoryModalOpen, setIsHistoryModalOpen] = useState(false);\r\n  const [searchTerm, setSearchTerm] = useState(\"\");\r\n  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState(\"\"); // Add debounced search\r\n  const [activeFilter, setActiveFilter] = useState(FilterKeys.ALL);\r\n  const [selectedIds, setSelectedIds] = useState(new Set());\r\n  const [currentPage, setCurrentPage] = useState(1);\r\n  const [pageSize] = useState(10);\r\n  const t = useTranslations(\"PropertyList\");\r\n  const tCommon = useTranslations(\"Common\");\r\n\r\n  // Refs for performance optimization\r\n  const searchTimeoutRef = useRef(null);\r\n  // State for filter counts - memoized initial state\r\n  const [filterCounts, setFilterCounts] = useState(() => ({\r\n    [FilterKeys.ALL]: 0,\r\n    [FilterKeys.APPROVED]: 0,\r\n    [FilterKeys.PENDING_APPROVAL]: 0,\r\n    [FilterKeys.REJECTED_BY_ADMIN]: 0,\r\n    [FilterKeys.REJECTED_DUE_TO_UNPAID]: 0,\r\n    [FilterKeys.WAITING_PAYMENT]: 0,\r\n    [FilterKeys.EXPIRED]: 0,\r\n    [FilterKeys.DRAFT]: 0,\r\n    [FilterKeys.SOLD]: 0,\r\n  }));\r\n\r\n  // Debounce search input\r\n  useEffect(() => {\r\n    if (searchTimeoutRef.current) {\r\n      clearTimeout(searchTimeoutRef.current);\r\n    }\r\n\r\n    searchTimeoutRef.current = setTimeout(() => {\r\n      setDebouncedSearchTerm(searchTerm);\r\n    }, 300); // 300ms debounce\r\n\r\n    return () => {\r\n      if (searchTimeoutRef.current) {\r\n        clearTimeout(searchTimeoutRef.current);\r\n      }\r\n    };\r\n  }, [searchTerm]);\r\n\r\n  \r\n\r\n  // Fetch filter counts from server\r\n  const fetchFilterCounts = useCallback(async () => {\r\n    setIsLoadingFilterCounts(true);\r\n    try {\r\n      // Call the getPropertyByUser with 'counts' parameter to use the stats endpoint\r\n      const result = await getPropertyByUser(\"counts\");\r\n      if (result && result?.success && result?.data) {\r\n        // The getPropertyStats function in property.jsx transforms the API response\r\n        // to match this expected format\r\n        setFilterCounts({\r\n          [FilterKeys.ALL]: result.data.total || 0,\r\n          [FilterKeys.APPROVED]: result.data.approved || 0,\r\n          [FilterKeys.PENDING_APPROVAL]: result.data.pendingApproval || 0,\r\n          [FilterKeys.REJECTED_BY_ADMIN]: result.data.rejectedByAdmin || 0,\r\n          [FilterKeys.REJECTED_DUE_TO_UNPAID]: result.data.rejectedDueToUnpaid || 0,\r\n          [FilterKeys.WAITING_PAYMENT]: result.data.waitingPayment || 0,\r\n          [FilterKeys.EXPIRED]: result.data.expired || 0,\r\n          [FilterKeys.DRAFT]: result.data.draft || 0,\r\n          [FilterKeys.SOLD]: result.data.sold || 0,\r\n        });\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error fetching filter counts:\", error);\r\n      toast({\r\n        description: \"Failed to fetch property statistics\",\r\n        variant: \"destructive\",\r\n      });\r\n    } finally {\r\n      setIsLoadingFilterCounts(false);\r\n    }\r\n  }, [toast]);\r\n\r\n  // --- Data Fetching Logic ---\r\n  const fetchProperties = useCallback(\r\n    async (status = null, page = 1, size = pageSize) => {\r\n      setIsLoading(true);\r\n      try {\r\n        const result = await getPropertyByUser(status, page, size);\r\n\r\n        if (result.success && result.data) {\r\n          // Extract items array from the paginated response\r\n          const propertyData = result.data.items || [];\r\n\r\n          // Set the property data\r\n          setData(propertyData);\r\n\r\n          // Update pagination state\r\n          setCurrentPage(result.data.currentPage || page);\r\n\r\n          // Refresh filter counts after data changes\r\n          fetchFilterCounts();\r\n        } else {\r\n          console.error(\"API returned error:\", result);\r\n          toast({\r\n            description: result.message || \"Failed to fetch properties\",\r\n            variant: \"destructive\",\r\n          });\r\n        }\r\n      } catch (error) {\r\n        console.error(\"Error fetching properties:\", error);\r\n        toast({\r\n          description: \"An unexpected error occurred\",\r\n          variant: \"destructive\",\r\n        });\r\n      } finally {\r\n        setIsLoading(false);\r\n      }\r\n    },\r\n    [pageSize, toast, fetchFilterCounts]\r\n  );\r\n\r\n  // Initialize component - fetch filter counts and handle initial data\r\n  useEffect(() => {\r\n    const initializeComponent = async () => {\r\n      // Always fetch filter counts first\r\n      await fetchFilterCounts();\r\n\r\n      // Handle initial data or fetch properties\r\n      if (initialData && Array.isArray(initialData) && initialData.length > 0 && activeFilter === FilterKeys.ALL) {\r\n        setData(initialData);\r\n      } else {\r\n        const statusParam = activeFilter !== FilterKeys.ALL ? activeFilter : null;\r\n        await fetchProperties(statusParam, 1, pageSize);\r\n      }\r\n\r\n      setIsInitialized(true);\r\n    };\r\n\r\n    initializeComponent();\r\n  }, []); // Only run once on mount\r\n\r\n  // Handle filter changes after initialization\r\n  useEffect(() => {\r\n    if (!isInitialized) return; // Don't run until component is initialized\r\n\r\n    const statusParam = activeFilter !== FilterKeys.ALL ? activeFilter : null;\r\n    fetchProperties(statusParam, 1, pageSize);\r\n  }, [activeFilter, isInitialized, fetchProperties, pageSize]);\r\n\r\n  // --- Filtering Logic for Search --- (Optimized with debounced search)\r\n  const filteredData = useMemo(() => {\r\n    // Ensure data is an array before filtering\r\n    if (!Array.isArray(data)) {\r\n      console.error(\"Data is not an array:\", data);\r\n      return [];\r\n    }\r\n\r\n    // Use debounced search term for better performance\r\n    if (!debouncedSearchTerm.trim()) {\r\n      return data;\r\n    }\r\n\r\n    const searchLower = debouncedSearchTerm.toLowerCase();\r\n    return data.filter((property) => {\r\n      // Search in multiple fields for better UX\r\n      return property && (\r\n        (property.name && property.name.toLowerCase().includes(searchLower)) ||\r\n        (property.address && property.address.toLowerCase().includes(searchLower)) ||\r\n        (property.addressSelected && property.addressSelected.toLowerCase().includes(searchLower))\r\n      );\r\n    });\r\n  }, [data, debouncedSearchTerm]);\r\n\r\n  // --- Handlers ---\r\n  const handleEdit = useCallback(\r\n    (propertyId) => {\r\n      router.push(`/user/bds/${propertyId}`);\r\n    },\r\n    [router]\r\n  );\r\n\r\n  const handleDelete = useCallback(\r\n    async (propertyId) => {\r\n      showAlert({\r\n        title: t(\"deleteConfirmTitle\"),\r\n        message: t(\"deleteConfirmMessage\"),\r\n        onConfirm: async () => {\r\n          setLoadingId(propertyId);\r\n          try {\r\n            const result = await deletePropertyById(propertyId);\r\n            if (result.success) {\r\n              // Clear the selected ID\r\n              setSelectedIds((prev) => {\r\n                const newSet = new Set(prev);\r\n                newSet.delete(propertyId);\r\n                return newSet;\r\n              });\r\n\r\n              // Show success message\r\n              toast({\r\n                description: t(\"deleteSuccessToast\"),\r\n                className: \"bg-teal-600 text-white\",\r\n              });\r\n\r\n              // Refresh data with current filter\r\n              fetchProperties(activeFilter !== FilterKeys.ALL ? activeFilter : null, 1, pageSize);\r\n            } else {\r\n              toast({\r\n                description: result.message || t(\"deleteErrorToast\"),\r\n                variant: \"destructive\",\r\n              });\r\n            }\r\n          } catch (error) {\r\n            toast({\r\n              description: t(\"deleteErrorToast\"),\r\n              variant: \"destructive\",\r\n            });\r\n          } finally {\r\n            setLoadingId(null);\r\n          }\r\n        },\r\n      });\r\n    },\r\n    [showAlert, toast, t, setSelectedIds, fetchProperties, activeFilter, pageSize]\r\n  );\r\n\r\n  const handleSendToReviewRequest = useCallback(\r\n    async (propertyId) => {\r\n      setLoadingId(propertyId);\r\n      try {\r\n        const remainingTimes = await verifyPropertyRemainingTimes(propertyId);\r\n        if (remainingTimes.success) {\r\n          showAlert({\r\n            title: t(\"verifyConfirmTitle\"),\r\n            message: t(\"verifyConfirmMessage\", { remainingTimes: remainingTimes.data }),\r\n            onConfirm: async () => {\r\n              try {\r\n                setLoadingId(propertyId);\r\n                const formData = new FormData();\r\n                formData.append(\"propertyId\", propertyId);\r\n                formData.append(\"status\", PropertyStatus.PENDING_APPROVAL);\r\n\r\n                const result = await updatePropertyStatus(formData);\r\n                if (result.success) {\r\n                  toast({\r\n                    description: t(\"verifySuccessToast\"),\r\n                    className: \"bg-teal-600 text-white\",\r\n                  });\r\n\r\n                  // Refresh data with current filter\r\n                  fetchProperties(activeFilter !== FilterKeys.ALL ? activeFilter : null, 1, pageSize);\r\n                } else {\r\n                  toast({\r\n                    description: result?.message || t(\"verifyErrorToast\"),\r\n                    variant: \"destructive\",\r\n                  });\r\n                }\r\n              } catch (error) {\r\n                console.error(\"Error sending verification request:\", error);\r\n                toast({\r\n                  description: t(\"verifyGenericErrorToast\"),\r\n                  variant: \"destructive\",\r\n                });\r\n              } finally {\r\n                setLoadingId(null);\r\n              }\r\n            },\r\n            hasCancel: true,\r\n            onCancel: () => setLoadingId(null),\r\n          });\r\n        } else {\r\n          setLoadingId(null);\r\n          toast({\r\n            description: remainingTimes?.message || t(\"verifyCheckErrorToast\"),\r\n            className: \"bg-red-600 text-white\",\r\n          });\r\n        }\r\n      } catch (error) {\r\n        setLoadingId(null);\r\n        console.error(\"Error checking remaining verification times:\", error);\r\n        toast({\r\n          description: t(\"verifyGenericErrorToast\"),\r\n          className: \"bg-red-600 text-white\",\r\n        });\r\n      }\r\n    },\r\n    [showAlert, toast, t, fetchProperties, activeFilter, pageSize]\r\n  );\r\n\r\n  const handleShowContacts = useCallback((propertyId) => {\r\n    setSelectedPropertyId(propertyId);\r\n    setIsContactModalOpen(true);\r\n  }, []);\r\n\r\n  const handleShowHistory = useCallback((propertyId) => {\r\n    setSelectedPropertyId(propertyId);\r\n    setIsHistoryModalOpen(true);\r\n  }, []);\r\n\r\n  // Checkbox handlers - optimized to prevent unnecessary re-renders\r\n  const handleCheckboxChange = useCallback((propertyId, checked) => {\r\n    setSelectedIds((prev) => {\r\n      const newSet = new Set(prev);\r\n      if (checked) {\r\n        newSet.add(propertyId);\r\n      } else {\r\n        newSet.delete(propertyId);\r\n      }\r\n      return newSet;\r\n    });\r\n  }, []);\r\n\r\n  const handleSelectAllChange = useCallback(\r\n    (checked) => {\r\n      if (checked) {\r\n        // Only map IDs, not full objects\r\n        const allIds = filteredData.map((p) => p.id);\r\n        setSelectedIds(new Set(allIds));\r\n      } else {\r\n        setSelectedIds(new Set());\r\n      }\r\n    },\r\n    [filteredData]\r\n  );\r\n\r\n  // Bulk action handlers\r\n  const handleBulkHighlight = useCallback(() => {\r\n    if (selectedIds.size === 0) return;\r\n\r\n    showAlert({\r\n      title: t(\"bulkHighlightConfirmTitle\", { count: selectedIds.size }),\r\n      message: t(\"bulkHighlightConfirmMessage\"),\r\n      onConfirm: async () => {\r\n        setLoadingId(\"bulk-highlight\");\r\n        try {\r\n          const result = await bulkUpdatePropertyHighlight(Array.from(selectedIds), true);\r\n\r\n          if (result.success) {\r\n            toast({\r\n              description: t(\"bulkHighlightSuccessToast\", { count: selectedIds.size }),\r\n              className: \"bg-teal-600 text-white\",\r\n            });\r\n            // Refresh data with current filter\r\n            fetchProperties(activeFilter !== FilterKeys.ALL ? activeFilter : null, 1, pageSize);\r\n          } else {\r\n            toast({\r\n              description: result.message || t(\"bulkHighlightErrorToast\"),\r\n              variant: \"destructive\",\r\n            });\r\n          }\r\n        } catch (error) {\r\n          console.error(\"Error highlighting properties:\", error);\r\n          toast({\r\n            description: t(\"bulkHighlightGenericErrorToast\"),\r\n            variant: \"destructive\",\r\n          });\r\n        } finally {\r\n          setLoadingId(null);\r\n        }\r\n      },\r\n    });\r\n  }, [selectedIds, showAlert, toast, fetchProperties, activeFilter, pageSize, t]);\r\n\r\n  const handleBulkRenew = useCallback(() => {\r\n    if (selectedIds.size === 0) return;\r\n\r\n    // For now, just show a message since renew functionality is not implemented in the API\r\n    toast({\r\n      description: t(\"bulkRenewFeatureInDevelopment\"),\r\n      variant: \"default\",\r\n    });\r\n\r\n    // When API is available, implement similar to handleBulkHighlight\r\n    // using the appropriate API endpoint\r\n  }, [selectedIds, toast, t]);\r\n\r\n  const handleBulkDelete = useCallback(() => {\r\n    if (selectedIds.size === 0) return;\r\n\r\n    showAlert({\r\n      title: t(\"bulkDeleteConfirmTitle\", { count: selectedIds.size }),\r\n      message: t(\"bulkDeleteConfirmMessage\"),\r\n      onConfirm: async () => {\r\n        setLoadingId(\"bulk-delete\");\r\n        try {\r\n          const result = await bulkDeleteProperties(Array.from(selectedIds));\r\n\r\n          if (result.success) {\r\n            // Clear the selected IDs\r\n            setSelectedIds(new Set());\r\n\r\n            toast({\r\n              description: t(\"bulkDeleteSuccessToast\", { count: selectedIds.size }),\r\n              className: \"bg-teal-600 text-white\",\r\n            });\r\n\r\n            // Refresh data with current filter\r\n            fetchProperties(activeFilter !== FilterKeys.ALL ? activeFilter : null, 1, pageSize);\r\n          } else {\r\n            toast({\r\n              description: result.message || t(\"bulkDeleteErrorToast\"),\r\n              variant: \"destructive\",\r\n            });\r\n          }\r\n        } catch (error) {\r\n          console.error(\"Error deleting properties:\", error);\r\n          toast({\r\n            description: t(\"bulkDeleteGenericErrorToast\"),\r\n            variant: \"destructive\",\r\n          });\r\n        } finally {\r\n          setLoadingId(null);\r\n        }\r\n      },\r\n    });\r\n  }, [selectedIds, showAlert, toast, fetchProperties, activeFilter, pageSize, setSelectedIds, t]);\r\n\r\n  // Memoized computed values for better performance\r\n  const computedValues = useMemo(() => {\r\n    const isOverallLoading = isLoading || isLoadingFilterCounts || !isInitialized;\r\n    const isAllSelected = filteredData.length > 0 && selectedIds.size === filteredData.length;\r\n    const isIndeterminate = selectedIds.size > 0 && selectedIds.size < filteredData.length;\r\n    const isEmptyDatabase = !isOverallLoading && filterCounts[FilterKeys.ALL] === 0;\r\n    const isEmptySearchResults = !isOverallLoading && filterCounts[FilterKeys.ALL] > 0 && (!filteredData || filteredData.length === 0);\r\n    const hasResults = !isOverallLoading && filteredData && filteredData.length > 0;\r\n\r\n    return {\r\n      isOverallLoading,\r\n      isAllSelected,\r\n      isIndeterminate,\r\n      isEmptyDatabase,\r\n      isEmptySearchResults,\r\n      hasResults,\r\n    };\r\n  }, [isLoading, isLoadingFilterCounts, isInitialized, filteredData, selectedIds.size, filterCounts]);\r\n\r\n  const {\r\n    isOverallLoading,\r\n    isAllSelected,\r\n    isIndeterminate,\r\n    isEmptyDatabase,\r\n    isEmptySearchResults,\r\n    hasResults\r\n  } = computedValues;\r\n\r\n  // --- Render ---\r\n  return (\r\n    <>\r\n      <AlertPopup />\r\n      \r\n      {/* Search Input - Always visible, disabled during loading */}\r\n      <div className=\"mb-4 flex flex-col sm:flex-row gap-4\">\r\n        <div className=\"relative flex-grow\">\r\n          <Search className=\"absolute left-2.5 top-2.5 h-4 w-4 text-gray-500\" />\r\n          <Input\r\n            type=\"search\"\r\n            placeholder={t(\"searchPlaceholder\")}\r\n            className=\"pl-8 w-full\"\r\n            value={searchTerm}\r\n            onChange={(e) => setSearchTerm(e.target.value)}\r\n            disabled={isOverallLoading}\r\n          />\r\n        </div>  \r\n      </div>\r\n\r\n      {/* Filter Buttons - Always visible, disabled during loading */}\r\n      <div className=\"mb-4 flex flex-wrap gap-2 border-b relative\">\r\n        <Button\r\n          variant={activeFilter === FilterKeys.ALL ? \"solid\" : \"ghost\"}\r\n          size=\"sm\"\r\n          rounded=\"none\"\r\n          onClick={() => setActiveFilter(FilterKeys.ALL)}\r\n          disabled={isOverallLoading}\r\n        >\r\n          {t(\"all\")} ({filterCounts[FilterKeys.ALL]})\r\n        </Button>\r\n        <Button\r\n          variant={activeFilter === FilterKeys.APPROVED ? \"solid\" : \"ghost\"}\r\n          size=\"sm\"\r\n          rounded=\"none\"\r\n          onClick={() => setActiveFilter(FilterKeys.APPROVED)}\r\n          disabled={isOverallLoading}\r\n        >\r\n          {t(\"approved\")} ({filterCounts[FilterKeys.APPROVED]})\r\n        </Button>\r\n        <Button\r\n          variant={activeFilter === FilterKeys.PENDING_APPROVAL ? \"solid\" : \"ghost\"}\r\n          size=\"sm\"\r\n          rounded=\"none\"\r\n          onClick={() => setActiveFilter(FilterKeys.PENDING_APPROVAL)}\r\n          disabled={isOverallLoading}\r\n        >\r\n          {t(\"pendingApproval\")} ({filterCounts[FilterKeys.PENDING_APPROVAL]})\r\n        </Button>\r\n        <Button\r\n          variant={activeFilter === FilterKeys.REJECTED_BY_ADMIN ? \"solid\" : \"ghost\"}\r\n          size=\"sm\"\r\n          rounded=\"none\"\r\n          onClick={() => setActiveFilter(FilterKeys.REJECTED_BY_ADMIN)}\r\n          disabled={isOverallLoading}\r\n        >\r\n          {t(\"rejectedByAdmin\")} ({filterCounts[FilterKeys.REJECTED_BY_ADMIN]})\r\n        </Button>\r\n        <Button\r\n          variant={activeFilter === FilterKeys.REJECTED_DUE_TO_UNPAID ? \"solid\" : \"ghost\"}\r\n          size=\"sm\"\r\n          rounded=\"none\"\r\n          onClick={() => setActiveFilter(FilterKeys.REJECTED_DUE_TO_UNPAID)}\r\n          disabled={isOverallLoading}\r\n        >\r\n          {t(\"rejectedDueToUnpaid\")} ({filterCounts[FilterKeys.REJECTED_DUE_TO_UNPAID]})\r\n        </Button>\r\n        <Button\r\n          variant={activeFilter === FilterKeys.WAITING_PAYMENT ? \"solid\" : \"ghost\"}\r\n          size=\"sm\"\r\n          rounded=\"none\"\r\n          onClick={() => setActiveFilter(FilterKeys.WAITING_PAYMENT)}\r\n          disabled={isOverallLoading}\r\n        >\r\n          {t(\"waitingPayment\")} ({filterCounts[FilterKeys.WAITING_PAYMENT]})\r\n        </Button>\r\n        <Button\r\n          variant={activeFilter === FilterKeys.EXPIRED ? \"solid\" : \"ghost\"}\r\n          size=\"sm\"\r\n          rounded=\"none\"\r\n          onClick={() => setActiveFilter(FilterKeys.EXPIRED)}\r\n          disabled={isOverallLoading}\r\n        >\r\n          {t(\"expired\")} ({filterCounts[FilterKeys.EXPIRED]})\r\n        </Button>\r\n        <Button\r\n          variant={activeFilter === FilterKeys.DRAFT ? \"solid\" : \"ghost\"}\r\n          size=\"sm\"\r\n          rounded=\"none\"\r\n          onClick={() => setActiveFilter(FilterKeys.DRAFT)}\r\n          disabled={isOverallLoading}\r\n        >\r\n          {t(\"draft\")} ({filterCounts[FilterKeys.DRAFT]})\r\n        </Button>\r\n        <Button\r\n          variant={activeFilter === FilterKeys.SOLD ? \"solid\" : \"ghost\"}\r\n          size=\"sm\"\r\n          rounded=\"none\"\r\n          onClick={() => setActiveFilter(FilterKeys.SOLD)}\r\n          disabled={isOverallLoading}\r\n        >\r\n          {t(\"sold\")} ({filterCounts[FilterKeys.SOLD]})\r\n        </Button>\r\n      </div>\r\n\r\n      {/* Bulk Actions - Always visible, disabled during loading */}\r\n      <div className=\"mb-4 flex items-center gap-4\">\r\n        <Checkbox\r\n          id=\"select-all\"\r\n          checked={isAllSelected}\r\n          onCheckedChange={handleSelectAllChange}\r\n          aria-label=\"Select all properties on this page\"\r\n          data-state={isIndeterminate ? \"indeterminate\" : isAllSelected ? \"checked\" : \"unchecked\"}\r\n          disabled={isOverallLoading}\r\n        />\r\n        <label htmlFor=\"select-all\" className={`text-sm font-medium ${isOverallLoading ? 'text-gray-400' : ''}`}>\r\n          {t(\"selectAll\")}\r\n        </label>\r\n        {selectedIds.size > 0 && (\r\n          <div className=\"flex gap-2 ml-auto\">\r\n            <Button\r\n              size=\"sm\"\r\n              variant=\"outline\"\r\n              onClick={handleBulkHighlight}\r\n              disabled={isOverallLoading || loadingId === \"bulk-delete\" || loadingId === \"bulk-highlight\"}\r\n            >\r\n              <Zap className=\"h-4 w-4 mr-1\" /> {tCommon(\"highlight_status\")} ({selectedIds.size})\r\n            </Button>\r\n            <Button\r\n              size=\"sm\"\r\n              variant=\"outline\"\r\n              onClick={handleBulkRenew}\r\n              disabled={isOverallLoading || loadingId === \"bulk-delete\" || loadingId === \"bulk-highlight\"}\r\n            >\r\n              <RefreshCcw className=\"h-4 w-4 mr-1\" /> {t(\"renew\")} ({selectedIds.size})\r\n            </Button>\r\n            <Button\r\n              size=\"sm\"\r\n              variant=\"destructive\"\r\n              onClick={handleBulkDelete}\r\n              disabled={isOverallLoading || loadingId === \"bulk-delete\" || loadingId === \"bulk-highlight\"}\r\n            >\r\n              {loadingId === \"bulk-delete\" || loadingId === \"bulk-highlight\" ? (\r\n                <LoaderCircle className=\"animate-spin h-4 w-4 mr-1\" />\r\n              ) : (\r\n                <Trash2 className=\"h-4 w-4 mr-1\" />\r\n              )}\r\n              {t(\"delete\")} ({selectedIds.size})\r\n            </Button>\r\n          </div>\r\n        )}\r\n      </div>\r\n\r\n      {/* Content Area - Conditional rendering based on loading and data states */}\r\n      {isOverallLoading ? (\r\n        <LoadingSpinner />\r\n      ) : isEmptyDatabase ? (\r\n        <NoData\r\n          hasCreateButton={true}\r\n          createMessage={t(\"noProperty\")}\r\n          createPageRoute=\"/user/bds/new\"\r\n          createButtonTitle={t(\"createProperty\")}\r\n        />\r\n      ) : isEmptySearchResults ? (\r\n        <NoData message={t(\"noResults\")} />\r\n      ) : hasResults ? (\r\n        <>\r\n          <div className=\"grid gap-4\">\r\n            {filteredData.map((property) => (\r\n              <PropertyCard\r\n                key={property.id}\r\n                property={property}\r\n                onEdit={handleEdit}\r\n                onDelete={handleDelete}\r\n                onSendToReview={handleSendToReviewRequest}\r\n                onShowContacts={handleShowContacts}\r\n                onShowHistory={handleShowHistory}\r\n                loadingId={loadingId}\r\n                isSelected={selectedIds.has(property.id)}\r\n                onCheckboxChange={handleCheckboxChange}\r\n              />\r\n            ))}\r\n          </div>\r\n\r\n          {/* Pagination Controls */}\r\n          {data.length > pageSize && (\r\n            <div className=\"mt-6 flex justify-center items-center gap-2\">\r\n              <Button\r\n                variant=\"outline\"\r\n                size=\"sm\"\r\n                onClick={() => fetchProperties(activeFilter !== FilterKeys.ALL ? activeFilter : null, Math.max(1, currentPage - 1), pageSize)}\r\n                disabled={currentPage <= 1 || isOverallLoading}\r\n              >\r\n                {t(\"previous\")}\r\n              </Button>\r\n              <span className=\"text-sm\">\r\n                {t(\"page\")} {currentPage}\r\n              </span>\r\n              <Button\r\n                variant=\"outline\"\r\n                size=\"sm\"\r\n                onClick={() => fetchProperties(activeFilter !== FilterKeys.ALL ? activeFilter : null, currentPage + 1, pageSize)}\r\n                disabled={filteredData.length < pageSize || isOverallLoading}\r\n              >\r\n                {t(\"next\")}\r\n              </Button>\r\n            </div>\r\n          )}\r\n        </>\r\n      ) : null}\r\n\r\n      {isContactModalOpen && (\r\n        <ContactRequestModal propertyId={selectedPropertyId} open={isContactModalOpen} onClose={() => setIsContactModalOpen(false)} />\r\n      )}\r\n\r\n      {isHistoryModalOpen && <HistoryModal propertyId={selectedPropertyId} open={isHistoryModalOpen} onClose={() => setIsHistoryModalOpen(false)} />}\r\n    </>\r\n  );\r\n}\r\n\r\nexport default memo(PropertyList);\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAQA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;AArBA;;;;;;;;;;;;;;;;AAuBA,6BAA6B;AAC7B,MAAM,aAAa,CAAA,GAAA,+JAAA,CAAA,UAAO,AAAD;;;;;;IACvB,KAAK;;AAEP,MAAM,SAAS,CAAA,GAAA,+JAAA,CAAA,UAAO,AAAD,EAAE;;;;;;;AACvB,MAAM,eAAe,CAAA,GAAA,+JAAA,CAAA,UAAO,AAAD,EAAE;;;;;;;AAC7B,MAAM,sBAAsB,CAAA,GAAA,+JAAA,CAAA,UAAO,AAAD;;;;;;IAChC,KAAK;;AAEP,MAAM,eAAe,CAAA,GAAA,+JAAA,CAAA,UAAO,AAAD;;;;;;IACzB,KAAK;;AAGP,qBAAqB;AACrB,MAAM,aAAa;IACjB,KAAK;IACL,UAAU;IACV,kBAAkB;IAClB,mBAAmB;IACnB,wBAAwB;IACxB,iBAAiB;IACjB,SAAS;IACT,OAAO;IACP,MAAM;AACR;AAEA,SAAS,aAAa,EAAE,WAAW,EAAE;IACnC,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,eAAe,EAAE;IAClD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,uBAAuB,yBAAyB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,OAAO,8BAA8B;IACxG,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,QAAQ,0CAA0C;IACrG,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,yHAAA,CAAA,WAAQ,AAAD;IAC7B,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,qHAAA,CAAA,WAAQ,AAAD;IACzB,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,KAAK,uBAAuB;IAC3F,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,WAAW,GAAG;IAC/D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,IAAI;IACnD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC5B,MAAM,IAAI,CAAA,GAAA,sMAAA,CAAA,kBAAe,AAAD,EAAE;IAC1B,MAAM,UAAU,CAAA,GAAA,sMAAA,CAAA,kBAAe,AAAD,EAAE;IAEhC,oCAAoC;IACpC,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAChC,mDAAmD;IACnD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,IAAM,CAAC;YACtD,CAAC,WAAW,GAAG,CAAC,EAAE;YAClB,CAAC,WAAW,QAAQ,CAAC,EAAE;YACvB,CAAC,WAAW,gBAAgB,CAAC,EAAE;YAC/B,CAAC,WAAW,iBAAiB,CAAC,EAAE;YAChC,CAAC,WAAW,sBAAsB,CAAC,EAAE;YACrC,CAAC,WAAW,eAAe,CAAC,EAAE;YAC9B,CAAC,WAAW,OAAO,CAAC,EAAE;YACtB,CAAC,WAAW,KAAK,CAAC,EAAE;YACpB,CAAC,WAAW,IAAI,CAAC,EAAE;QACrB,CAAC;IAED,wBAAwB;IACxB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,iBAAiB,OAAO,EAAE;YAC5B,aAAa,iBAAiB,OAAO;QACvC;QAEA,iBAAiB,OAAO,GAAG,WAAW;YACpC,uBAAuB;QACzB,GAAG,MAAM,iBAAiB;QAE1B,OAAO;YACL,IAAI,iBAAiB,OAAO,EAAE;gBAC5B,aAAa,iBAAiB,OAAO;YACvC;QACF;IACF,GAAG;QAAC;KAAW;IAIf,kCAAkC;IAClC,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACpC,yBAAyB;QACzB,IAAI;YACF,+EAA+E;YAC/E,MAAM,SAAS,MAAM,CAAA,GAAA,gKAAA,CAAA,oBAAiB,AAAD,EAAE;YACvC,IAAI,UAAU,QAAQ,WAAW,QAAQ,MAAM;gBAC7C,4EAA4E;gBAC5E,gCAAgC;gBAChC,gBAAgB;oBACd,CAAC,WAAW,GAAG,CAAC,EAAE,OAAO,IAAI,CAAC,KAAK,IAAI;oBACvC,CAAC,WAAW,QAAQ,CAAC,EAAE,OAAO,IAAI,CAAC,QAAQ,IAAI;oBAC/C,CAAC,WAAW,gBAAgB,CAAC,EAAE,OAAO,IAAI,CAAC,eAAe,IAAI;oBAC9D,CAAC,WAAW,iBAAiB,CAAC,EAAE,OAAO,IAAI,CAAC,eAAe,IAAI;oBAC/D,CAAC,WAAW,sBAAsB,CAAC,EAAE,OAAO,IAAI,CAAC,mBAAmB,IAAI;oBACxE,CAAC,WAAW,eAAe,CAAC,EAAE,OAAO,IAAI,CAAC,cAAc,IAAI;oBAC5D,CAAC,WAAW,OAAO,CAAC,EAAE,OAAO,IAAI,CAAC,OAAO,IAAI;oBAC7C,CAAC,WAAW,KAAK,CAAC,EAAE,OAAO,IAAI,CAAC,KAAK,IAAI;oBACzC,CAAC,WAAW,IAAI,CAAC,EAAE,OAAO,IAAI,CAAC,IAAI,IAAI;gBACzC;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,MAAM;gBACJ,aAAa;gBACb,SAAS;YACX;QACF,SAAU;YACR,yBAAyB;QAC3B;IACF,GAAG;QAAC;KAAM;IAEV,8BAA8B;IAC9B,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAChC,OAAO,SAAS,IAAI,EAAE,OAAO,CAAC,EAAE,OAAO,QAAQ;QAC7C,aAAa;QACb,IAAI;YACF,MAAM,SAAS,MAAM,CAAA,GAAA,gKAAA,CAAA,oBAAiB,AAAD,EAAE,QAAQ,MAAM;YAErD,IAAI,OAAO,OAAO,IAAI,OAAO,IAAI,EAAE;gBACjC,kDAAkD;gBAClD,MAAM,eAAe,OAAO,IAAI,CAAC,KAAK,IAAI,EAAE;gBAE5C,wBAAwB;gBACxB,QAAQ;gBAER,0BAA0B;gBAC1B,eAAe,OAAO,IAAI,CAAC,WAAW,IAAI;gBAE1C,2CAA2C;gBAC3C;YACF,OAAO;gBACL,QAAQ,KAAK,CAAC,uBAAuB;gBACrC,MAAM;oBACJ,aAAa,OAAO,OAAO,IAAI;oBAC/B,SAAS;gBACX;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,MAAM;gBACJ,aAAa;gBACb,SAAS;YACX;QACF,SAAU;YACR,aAAa;QACf;IACF,GACA;QAAC;QAAU;QAAO;KAAkB;IAGtC,qEAAqE;IACrE,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,sBAAsB;YAC1B,mCAAmC;YACnC,MAAM;YAEN,0CAA0C;YAC1C,IAAI,eAAe,MAAM,OAAO,CAAC,gBAAgB,YAAY,MAAM,GAAG,KAAK,iBAAiB,WAAW,GAAG,EAAE;gBAC1G,QAAQ;YACV,OAAO;gBACL,MAAM,cAAc,iBAAiB,WAAW,GAAG,GAAG,eAAe;gBACrE,MAAM,gBAAgB,aAAa,GAAG;YACxC;YAEA,iBAAiB;QACnB;QAEA;IACF,GAAG,EAAE,GAAG,yBAAyB;IAEjC,6CAA6C;IAC7C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,eAAe,QAAQ,2CAA2C;QAEvE,MAAM,cAAc,iBAAiB,WAAW,GAAG,GAAG,eAAe;QACrE,gBAAgB,aAAa,GAAG;IAClC,GAAG;QAAC;QAAc;QAAe;QAAiB;KAAS;IAE3D,uEAAuE;IACvE,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QAC3B,2CAA2C;QAC3C,IAAI,CAAC,MAAM,OAAO,CAAC,OAAO;YACxB,QAAQ,KAAK,CAAC,yBAAyB;YACvC,OAAO,EAAE;QACX;QAEA,mDAAmD;QACnD,IAAI,CAAC,oBAAoB,IAAI,IAAI;YAC/B,OAAO;QACT;QAEA,MAAM,cAAc,oBAAoB,WAAW;QACnD,OAAO,KAAK,MAAM,CAAC,CAAC;YAClB,0CAA0C;YAC1C,OAAO,YAAY,CACjB,AAAC,SAAS,IAAI,IAAI,SAAS,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,gBACtD,SAAS,OAAO,IAAI,SAAS,OAAO,CAAC,WAAW,GAAG,QAAQ,CAAC,gBAC5D,SAAS,eAAe,IAAI,SAAS,eAAe,CAAC,WAAW,GAAG,QAAQ,CAAC,YAC/E;QACF;IACF,GAAG;QAAC;QAAM;KAAoB;IAE9B,mBAAmB;IACnB,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAC3B,CAAC;QACC,OAAO,IAAI,CAAC,CAAC,UAAU,EAAE,YAAY;IACvC,GACA;QAAC;KAAO;IAGV,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAC7B,OAAO;QACL,UAAU;YACR,OAAO,EAAE;YACT,SAAS,EAAE;YACX,WAAW;gBACT,aAAa;gBACb,IAAI;oBACF,MAAM,SAAS,MAAM,CAAA,GAAA,gKAAA,CAAA,qBAAkB,AAAD,EAAE;oBACxC,IAAI,OAAO,OAAO,EAAE;wBAClB,wBAAwB;wBACxB,eAAe,CAAC;4BACd,MAAM,SAAS,IAAI,IAAI;4BACvB,OAAO,MAAM,CAAC;4BACd,OAAO;wBACT;wBAEA,uBAAuB;wBACvB,MAAM;4BACJ,aAAa,EAAE;4BACf,WAAW;wBACb;wBAEA,mCAAmC;wBACnC,gBAAgB,iBAAiB,WAAW,GAAG,GAAG,eAAe,MAAM,GAAG;oBAC5E,OAAO;wBACL,MAAM;4BACJ,aAAa,OAAO,OAAO,IAAI,EAAE;4BACjC,SAAS;wBACX;oBACF;gBACF,EAAE,OAAO,OAAO;oBACd,MAAM;wBACJ,aAAa,EAAE;wBACf,SAAS;oBACX;gBACF,SAAU;oBACR,aAAa;gBACf;YACF;QACF;IACF,GACA;QAAC;QAAW;QAAO;QAAG;QAAgB;QAAiB;QAAc;KAAS;IAGhF,MAAM,4BAA4B,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAC1C,OAAO;QACL,aAAa;QACb,IAAI;YACF,MAAM,iBAAiB,MAAM,CAAA,GAAA,gKAAA,CAAA,+BAA4B,AAAD,EAAE;YAC1D,IAAI,eAAe,OAAO,EAAE;gBAC1B,UAAU;oBACR,OAAO,EAAE;oBACT,SAAS,EAAE,wBAAwB;wBAAE,gBAAgB,eAAe,IAAI;oBAAC;oBACzE,WAAW;wBACT,IAAI;4BACF,aAAa;4BACb,MAAM,WAAW,IAAI;4BACrB,SAAS,MAAM,CAAC,cAAc;4BAC9B,SAAS,MAAM,CAAC,UAAU,2GAAA,CAAA,iBAAc,CAAC,gBAAgB;4BAEzD,MAAM,SAAS,MAAM,CAAA,GAAA,gKAAA,CAAA,uBAAoB,AAAD,EAAE;4BAC1C,IAAI,OAAO,OAAO,EAAE;gCAClB,MAAM;oCACJ,aAAa,EAAE;oCACf,WAAW;gCACb;gCAEA,mCAAmC;gCACnC,gBAAgB,iBAAiB,WAAW,GAAG,GAAG,eAAe,MAAM,GAAG;4BAC5E,OAAO;gCACL,MAAM;oCACJ,aAAa,QAAQ,WAAW,EAAE;oCAClC,SAAS;gCACX;4BACF;wBACF,EAAE,OAAO,OAAO;4BACd,QAAQ,KAAK,CAAC,uCAAuC;4BACrD,MAAM;gCACJ,aAAa,EAAE;gCACf,SAAS;4BACX;wBACF,SAAU;4BACR,aAAa;wBACf;oBACF;oBACA,WAAW;oBACX,UAAU,IAAM,aAAa;gBAC/B;YACF,OAAO;gBACL,aAAa;gBACb,MAAM;oBACJ,aAAa,gBAAgB,WAAW,EAAE;oBAC1C,WAAW;gBACb;YACF;QACF,EAAE,OAAO,OAAO;YACd,aAAa;YACb,QAAQ,KAAK,CAAC,gDAAgD;YAC9D,MAAM;gBACJ,aAAa,EAAE;gBACf,WAAW;YACb;QACF;IACF,GACA;QAAC;QAAW;QAAO;QAAG;QAAiB;QAAc;KAAS;IAGhE,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACtC,sBAAsB;QACtB,sBAAsB;IACxB,GAAG,EAAE;IAEL,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACrC,sBAAsB;QACtB,sBAAsB;IACxB,GAAG,EAAE;IAEL,kEAAkE;IAClE,MAAM,uBAAuB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC,YAAY;QACpD,eAAe,CAAC;YACd,MAAM,SAAS,IAAI,IAAI;YACvB,IAAI,SAAS;gBACX,OAAO,GAAG,CAAC;YACb,OAAO;gBACL,OAAO,MAAM,CAAC;YAChB;YACA,OAAO;QACT;IACF,GAAG,EAAE;IAEL,MAAM,wBAAwB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EACtC,CAAC;QACC,IAAI,SAAS;YACX,iCAAiC;YACjC,MAAM,SAAS,aAAa,GAAG,CAAC,CAAC,IAAM,EAAE,EAAE;YAC3C,eAAe,IAAI,IAAI;QACzB,OAAO;YACL,eAAe,IAAI;QACrB;IACF,GACA;QAAC;KAAa;IAGhB,uBAAuB;IACvB,MAAM,sBAAsB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACtC,IAAI,YAAY,IAAI,KAAK,GAAG;QAE5B,UAAU;YACR,OAAO,EAAE,6BAA6B;gBAAE,OAAO,YAAY,IAAI;YAAC;YAChE,SAAS,EAAE;YACX,WAAW;gBACT,aAAa;gBACb,IAAI;oBACF,MAAM,SAAS,MAAM,CAAA,GAAA,gKAAA,CAAA,8BAA2B,AAAD,EAAE,MAAM,IAAI,CAAC,cAAc;oBAE1E,IAAI,OAAO,OAAO,EAAE;wBAClB,MAAM;4BACJ,aAAa,EAAE,6BAA6B;gCAAE,OAAO,YAAY,IAAI;4BAAC;4BACtE,WAAW;wBACb;wBACA,mCAAmC;wBACnC,gBAAgB,iBAAiB,WAAW,GAAG,GAAG,eAAe,MAAM,GAAG;oBAC5E,OAAO;wBACL,MAAM;4BACJ,aAAa,OAAO,OAAO,IAAI,EAAE;4BACjC,SAAS;wBACX;oBACF;gBACF,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,kCAAkC;oBAChD,MAAM;wBACJ,aAAa,EAAE;wBACf,SAAS;oBACX;gBACF,SAAU;oBACR,aAAa;gBACf;YACF;QACF;IACF,GAAG;QAAC;QAAa;QAAW;QAAO;QAAiB;QAAc;QAAU;KAAE;IAE9E,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAClC,IAAI,YAAY,IAAI,KAAK,GAAG;QAE5B,uFAAuF;QACvF,MAAM;YACJ,aAAa,EAAE;YACf,SAAS;QACX;IAEA,kEAAkE;IAClE,qCAAqC;IACvC,GAAG;QAAC;QAAa;QAAO;KAAE;IAE1B,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACnC,IAAI,YAAY,IAAI,KAAK,GAAG;QAE5B,UAAU;YACR,OAAO,EAAE,0BAA0B;gBAAE,OAAO,YAAY,IAAI;YAAC;YAC7D,SAAS,EAAE;YACX,WAAW;gBACT,aAAa;gBACb,IAAI;oBACF,MAAM,SAAS,MAAM,CAAA,GAAA,gKAAA,CAAA,uBAAoB,AAAD,EAAE,MAAM,IAAI,CAAC;oBAErD,IAAI,OAAO,OAAO,EAAE;wBAClB,yBAAyB;wBACzB,eAAe,IAAI;wBAEnB,MAAM;4BACJ,aAAa,EAAE,0BAA0B;gCAAE,OAAO,YAAY,IAAI;4BAAC;4BACnE,WAAW;wBACb;wBAEA,mCAAmC;wBACnC,gBAAgB,iBAAiB,WAAW,GAAG,GAAG,eAAe,MAAM,GAAG;oBAC5E,OAAO;wBACL,MAAM;4BACJ,aAAa,OAAO,OAAO,IAAI,EAAE;4BACjC,SAAS;wBACX;oBACF;gBACF,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,8BAA8B;oBAC5C,MAAM;wBACJ,aAAa,EAAE;wBACf,SAAS;oBACX;gBACF,SAAU;oBACR,aAAa;gBACf;YACF;QACF;IACF,GAAG;QAAC;QAAa;QAAW;QAAO;QAAiB;QAAc;QAAU;QAAgB;KAAE;IAE9F,kDAAkD;IAClD,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QAC7B,MAAM,mBAAmB,aAAa,yBAAyB,CAAC;QAChE,MAAM,gBAAgB,aAAa,MAAM,GAAG,KAAK,YAAY,IAAI,KAAK,aAAa,MAAM;QACzF,MAAM,kBAAkB,YAAY,IAAI,GAAG,KAAK,YAAY,IAAI,GAAG,aAAa,MAAM;QACtF,MAAM,kBAAkB,CAAC,oBAAoB,YAAY,CAAC,WAAW,GAAG,CAAC,KAAK;QAC9E,MAAM,uBAAuB,CAAC,oBAAoB,YAAY,CAAC,WAAW,GAAG,CAAC,GAAG,KAAK,CAAC,CAAC,gBAAgB,aAAa,MAAM,KAAK,CAAC;QACjI,MAAM,aAAa,CAAC,oBAAoB,gBAAgB,aAAa,MAAM,GAAG;QAE9E,OAAO;YACL;YACA;YACA;YACA;YACA;YACA;QACF;IACF,GAAG;QAAC;QAAW;QAAuB;QAAe;QAAc,YAAY,IAAI;QAAE;KAAa;IAElG,MAAM,EACJ,gBAAgB,EAChB,aAAa,EACb,eAAe,EACf,eAAe,EACf,oBAAoB,EACpB,UAAU,EACX,GAAG;IAEJ,iBAAiB;IACjB,qBACE;;0BACE,8OAAC;;;;;0BAGD,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,sMAAA,CAAA,SAAM;4BAAC,WAAU;;;;;;sCAClB,8OAAC,0HAAA,CAAA,QAAK;4BACJ,MAAK;4BACL,aAAa,EAAE;4BACf,WAAU;4BACV,OAAO;4BACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;4BAC7C,UAAU;;;;;;;;;;;;;;;;;0BAMhB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,2HAAA,CAAA,SAAM;wBACL,SAAS,iBAAiB,WAAW,GAAG,GAAG,UAAU;wBACrD,MAAK;wBACL,SAAQ;wBACR,SAAS,IAAM,gBAAgB,WAAW,GAAG;wBAC7C,UAAU;;4BAET,EAAE;4BAAO;4BAAG,YAAY,CAAC,WAAW,GAAG,CAAC;4BAAC;;;;;;;kCAE5C,8OAAC,2HAAA,CAAA,SAAM;wBACL,SAAS,iBAAiB,WAAW,QAAQ,GAAG,UAAU;wBAC1D,MAAK;wBACL,SAAQ;wBACR,SAAS,IAAM,gBAAgB,WAAW,QAAQ;wBAClD,UAAU;;4BAET,EAAE;4BAAY;4BAAG,YAAY,CAAC,WAAW,QAAQ,CAAC;4BAAC;;;;;;;kCAEtD,8OAAC,2HAAA,CAAA,SAAM;wBACL,SAAS,iBAAiB,WAAW,gBAAgB,GAAG,UAAU;wBAClE,MAAK;wBACL,SAAQ;wBACR,SAAS,IAAM,gBAAgB,WAAW,gBAAgB;wBAC1D,UAAU;;4BAET,EAAE;4BAAmB;4BAAG,YAAY,CAAC,WAAW,gBAAgB,CAAC;4BAAC;;;;;;;kCAErE,8OAAC,2HAAA,CAAA,SAAM;wBACL,SAAS,iBAAiB,WAAW,iBAAiB,GAAG,UAAU;wBACnE,MAAK;wBACL,SAAQ;wBACR,SAAS,IAAM,gBAAgB,WAAW,iBAAiB;wBAC3D,UAAU;;4BAET,EAAE;4BAAmB;4BAAG,YAAY,CAAC,WAAW,iBAAiB,CAAC;4BAAC;;;;;;;kCAEtE,8OAAC,2HAAA,CAAA,SAAM;wBACL,SAAS,iBAAiB,WAAW,sBAAsB,GAAG,UAAU;wBACxE,MAAK;wBACL,SAAQ;wBACR,SAAS,IAAM,gBAAgB,WAAW,sBAAsB;wBAChE,UAAU;;4BAET,EAAE;4BAAuB;4BAAG,YAAY,CAAC,WAAW,sBAAsB,CAAC;4BAAC;;;;;;;kCAE/E,8OAAC,2HAAA,CAAA,SAAM;wBACL,SAAS,iBAAiB,WAAW,eAAe,GAAG,UAAU;wBACjE,MAAK;wBACL,SAAQ;wBACR,SAAS,IAAM,gBAAgB,WAAW,eAAe;wBACzD,UAAU;;4BAET,EAAE;4BAAkB;4BAAG,YAAY,CAAC,WAAW,eAAe,CAAC;4BAAC;;;;;;;kCAEnE,8OAAC,2HAAA,CAAA,SAAM;wBACL,SAAS,iBAAiB,WAAW,OAAO,GAAG,UAAU;wBACzD,MAAK;wBACL,SAAQ;wBACR,SAAS,IAAM,gBAAgB,WAAW,OAAO;wBACjD,UAAU;;4BAET,EAAE;4BAAW;4BAAG,YAAY,CAAC,WAAW,OAAO,CAAC;4BAAC;;;;;;;kCAEpD,8OAAC,2HAAA,CAAA,SAAM;wBACL,SAAS,iBAAiB,WAAW,KAAK,GAAG,UAAU;wBACvD,MAAK;wBACL,SAAQ;wBACR,SAAS,IAAM,gBAAgB,WAAW,KAAK;wBAC/C,UAAU;;4BAET,EAAE;4BAAS;4BAAG,YAAY,CAAC,WAAW,KAAK,CAAC;4BAAC;;;;;;;kCAEhD,8OAAC,2HAAA,CAAA,SAAM;wBACL,SAAS,iBAAiB,WAAW,IAAI,GAAG,UAAU;wBACtD,MAAK;wBACL,SAAQ;wBACR,SAAS,IAAM,gBAAgB,WAAW,IAAI;wBAC9C,UAAU;;4BAET,EAAE;4BAAQ;4BAAG,YAAY,CAAC,WAAW,IAAI,CAAC;4BAAC;;;;;;;;;;;;;0BAKhD,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,6HAAA,CAAA,WAAQ;wBACP,IAAG;wBACH,SAAS;wBACT,iBAAiB;wBACjB,cAAW;wBACX,cAAY,kBAAkB,kBAAkB,gBAAgB,YAAY;wBAC5E,UAAU;;;;;;kCAEZ,8OAAC;wBAAM,SAAQ;wBAAa,WAAW,CAAC,oBAAoB,EAAE,mBAAmB,kBAAkB,IAAI;kCACpG,EAAE;;;;;;oBAEJ,YAAY,IAAI,GAAG,mBAClB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,2HAAA,CAAA,SAAM;gCACL,MAAK;gCACL,SAAQ;gCACR,SAAS;gCACT,UAAU,oBAAoB,cAAc,iBAAiB,cAAc;;kDAE3E,8OAAC,gMAAA,CAAA,MAAG;wCAAC,WAAU;;;;;;oCAAiB;oCAAE,QAAQ;oCAAoB;oCAAG,YAAY,IAAI;oCAAC;;;;;;;0CAEpF,8OAAC,2HAAA,CAAA,SAAM;gCACL,MAAK;gCACL,SAAQ;gCACR,SAAS;gCACT,UAAU,oBAAoB,cAAc,iBAAiB,cAAc;;kDAE3E,8OAAC,kNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;oCAAiB;oCAAE,EAAE;oCAAS;oCAAG,YAAY,IAAI;oCAAC;;;;;;;0CAE1E,8OAAC,2HAAA,CAAA,SAAM;gCACL,MAAK;gCACL,SAAQ;gCACR,SAAS;gCACT,UAAU,oBAAoB,cAAc,iBAAiB,cAAc;;oCAE1E,cAAc,iBAAiB,cAAc,iCAC5C,8OAAC,sNAAA,CAAA,eAAY;wCAAC,WAAU;;;;;6DAExB,8OAAC,0MAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;oCAEnB,EAAE;oCAAU;oCAAG,YAAY,IAAI;oCAAC;;;;;;;;;;;;;;;;;;;YAOxC,iCACC,8OAAC,uIAAA,CAAA,iBAAc;;;;uBACb,gCACF,8OAAC;gBACC,iBAAiB;gBACjB,eAAe,EAAE;gBACjB,iBAAgB;gBAChB,mBAAmB,EAAE;;;;;uBAErB,qCACF,8OAAC;gBAAO,SAAS,EAAE;;;;;uBACjB,2BACF;;kCACE,8OAAC;wBAAI,WAAU;kCACZ,aAAa,GAAG,CAAC,CAAC,yBACjB,8OAAC;gCAEC,UAAU;gCACV,QAAQ;gCACR,UAAU;gCACV,gBAAgB;gCAChB,gBAAgB;gCAChB,eAAe;gCACf,WAAW;gCACX,YAAY,YAAY,GAAG,CAAC,SAAS,EAAE;gCACvC,kBAAkB;+BATb,SAAS,EAAE;;;;;;;;;;oBAerB,KAAK,MAAM,GAAG,0BACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,2HAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS,IAAM,gBAAgB,iBAAiB,WAAW,GAAG,GAAG,eAAe,MAAM,KAAK,GAAG,CAAC,GAAG,cAAc,IAAI;gCACpH,UAAU,eAAe,KAAK;0CAE7B,EAAE;;;;;;0CAEL,8OAAC;gCAAK,WAAU;;oCACb,EAAE;oCAAQ;oCAAE;;;;;;;0CAEf,8OAAC,2HAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS,IAAM,gBAAgB,iBAAiB,WAAW,GAAG,GAAG,eAAe,MAAM,cAAc,GAAG;gCACvG,UAAU,aAAa,MAAM,GAAG,YAAY;0CAE3C,EAAE;;;;;;;;;;;;;+BAKT;YAEH,oCACC,8OAAC;gBAAoB,YAAY;gBAAoB,MAAM;gBAAoB,SAAS,IAAM,sBAAsB;;;;;;YAGrH,oCAAsB,8OAAC;gBAAa,YAAY;gBAAoB,MAAM;gBAAoB,SAAS,IAAM,sBAAsB;;;;;;;;AAG1I;qDAEe,CAAA,GAAA,qMAAA,CAAA,OAAI,AAAD,EAAE", "debugId": null}}]}