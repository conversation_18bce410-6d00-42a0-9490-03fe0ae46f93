using RealEstate.Application.DTO;

namespace RealEstate.Application.Interfaces
{
    public interface IUserDashboardService
    {
        Task<UserDashboardDto> GetUserDashboardAsync(Guid userId);
        Task<WalletInfoDto> GetUserWalletInfoAsync(Guid userId);
        Task<PropertyStatsDto> GetUserPropertyStatsAsync(Guid userId);
        Task<List<WalletTransactionDto>> GetUserTransactionsAsync(Guid userId, int count = 10);
        Task<MemberRankingDto> GetUserMemberRankingInfoAsync(Guid userId);
        Task<decimal> GetHighlightFeeByUserIdAsync(Guid userId);

        // Additional methods for optional endpoints
        Task<MonthlySpendingDto> GetMonthlySpendingAsync(Guid userId, int year);
        Task<PropertyPerformanceDto> GetPropertyPerformanceAsync(Guid userId);
    }
}