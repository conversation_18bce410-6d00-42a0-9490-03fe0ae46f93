"use client";

import { memo } from "react";
import Image from "next/image";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Star, Trash2 } from "lucide-react";
import { useTranslations } from "next-intl";

// Create a memoized image card component outside the main component
const ImageCard = memo(
  ({
    file,
    caption,
    isAvatar,
    onCaptionChange,
    onSetAvatar,
    onDelete,
    onClick,
    showMoreCount = 0,
    isFormDisabled,
  }) => {
    const t = useTranslations("PropertyImageUploader"); // Assuming translations are needed here, might need adjustment
    return (
      <div className={`border rounded-md overflow-hidden cursor-pointer'}`} onClick={onClick}>
        <div className="relative h-[160px] w-full">
          <Image
            src={file.mediaURL || "/placeholder.svg"}
            alt={caption || "Property image"}
            fill
            className="object-cover"
            loading="lazy"
            sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
          />

          {isAvatar && (
            <div className="absolute top-2 left-2 z-10">
              <Badge rounded="full" className="bg-primary text-white">{t("avatar")}</Badge>
            </div>
          )}

          {showMoreCount > 0 && (
            <div className="absolute inset-0 bg-black/60 flex items-center justify-center">
              <span className="text-white text-2xl font-bold">+{showMoreCount}</span>
            </div>
          )}
        </div>

        {!isFormDisabled && (
          <div className="p-2 space-y-2">
            <Input
              placeholder={t("addCaptionPlaceholder")} // Using translation key
              value={caption || ""}
              onChange={(e) => onCaptionChange(file.id, e.target.value)}
              className="text-sm h-8"
              onClick={(e) => e.stopPropagation()} // Keep stopPropagation
              disabled={isFormDisabled}
            />
            <div className="flex justify-between">
              <Button
                type="button"
                size="sm"
                variant="ghost"
                className="h-8 w-8 p-0"
                onClick={(e) => {
                  e.stopPropagation(); // Keep stopPropagation
                  e.preventDefault(); // Prevent form submission
                  if (!isFormDisabled) onSetAvatar(file.id);
                }}
                disabled={isAvatar || isFormDisabled}
                title={t("setAsAvatar")}
              >
                <Star className={`w-4 h-4 ${isAvatar ? "fill-primary text-primary" : ""}`} />
              </Button>
              <Button
                type="button"
                size="sm"
                variant="ghost"
                className="h-8 w-8 p-0 text-red-500 hover:text-red-700"
                onClick={(e) => {
                  e.stopPropagation(); // Keep stopPropagation
                  e.preventDefault(); // Prevent form submission
                  if (!isFormDisabled) onDelete(file.id);
                }}
 
                disabled={isFormDisabled}
              >
                <Trash2 className="w-4 h-4" />
              </Button>
            </div>
          </div>
        )}
      </div>
    );
  }
);
ImageCard.displayName = "ImageCard";

export default ImageCard;
