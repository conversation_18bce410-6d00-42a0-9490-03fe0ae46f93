﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using RealEstate.Application.DTO;
using RealEstate.Application.Interfaces;

namespace RealEstate.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class UserController : BaseController
    {
        private readonly IUserService _userService;
        private readonly IUserDashboardService _dashboardService;
        private readonly ILogger<UserController> _logger;

        public UserController(IUserService userService, IUserDashboardService dashboardService, ILogger<UserController> logger)
        {
            _userService = userService;
            _dashboardService = dashboardService;
            _logger = logger;
        }

        [HttpPut("role")]
        public async Task<ActionResult<AddUserRoleDto>> AddUserRole(AddUserRoleDto addUserRoleDto)
        {
            try
            {
                _logger.LogInformation("Adding role {RoleId} to user {UserId}", addUserRoleDto.RoleId, addUserRoleDto.UserId);
                LogUserAction(_logger, "AddUserRole", addUserRoleDto);

                bool isOk = await _userService.AddUserRoleAsync(addUserRoleDto);

                if (isOk)
                {
                    _logger.LogInformation("Successfully added role {RoleId} to user {UserId}", addUserRoleDto.RoleId, addUserRoleDto.UserId);
                }
                else
                {
                    _logger.LogWarning("Failed to add role {RoleId} to user {UserId}", addUserRoleDto.RoleId, addUserRoleDto.UserId);
                }

                return Ok(isOk);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error adding role {RoleId} to user {UserId}", addUserRoleDto.RoleId, addUserRoleDto.UserId);
                return BadRequest(new { Message = "An error occurred while adding user role. Please try again later." });
            }
        }

        [HttpGet("{id}")]
        public async Task<IActionResult> GetUser(Guid id)
        {
            try
            {
                _logger.LogInformation("Retrieving user information for user {UserId}", id);
                LogUserAction(_logger, "GetUser", new { TargetUserId = id });

                var user = await _userService.GetUserByIdAsync(id);
                if (user == null)
                {
                    _logger.LogWarning("User {UserId} not found", id);
                    return NotFound(new { Message = "User not found" });
                }

                _logger.LogInformation("Successfully retrieved user information for user {UserId}", id);
                return Ok(user);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving user {UserId}", id);
                return StatusCode(500, new { Message = "An error occurred while retrieving user information. Please try again later." });
            }
        }

        [HttpGet("dashboard")]
        public async Task<ActionResult<UserDashboardDto>> GetUserDashboard()
        {
            try
            {
                var userId = GetUserId();
                if (!userId.HasValue)
                {
                    LogSecurityEvent(_logger, "UnauthorizedDashboardAccess", "User attempted to access dashboard without valid authentication");
                    return Unauthorized(new { Message = "User not authenticated" });
                }

                _logger.LogInformation("Retrieving dashboard for user {UserId}", userId.Value);
                LogUserAction(_logger, "GetUserDashboard");

                var dashboard = await _dashboardService.GetUserDashboardAsync(userId.Value);

                _logger.LogInformation("Successfully retrieved dashboard for user {UserId}", userId.Value);
                return Ok(dashboard);
            }
            catch (KeyNotFoundException ex)
            {
                _logger.LogWarning("Dashboard not found for user {UserId}: {Message}", GetUserId(), ex.Message);
                return NotFound(new { Message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving dashboard for user {UserId}", GetUserId());
                return StatusCode(500, new { Message = "An error occurred while retrieving dashboard. Please try again later." });
            }
        }

        [HttpGet("wallet")]
        public async Task<ActionResult<WalletInfoDto>> GetUserWallet()
        {
            try
            {
                var userId = GetUserId();
                if (!userId.HasValue)
                {
                    return Unauthorized("User not authenticated");
                }

                var walletInfo = await _dashboardService.GetUserWalletInfoAsync(userId.Value);
                return Ok(walletInfo);
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"An error occurred: {ex.Message}");
            }
        }

        [HttpGet("properties/stats")]
        public async Task<ActionResult<PropertyStatsDto>> GetUserPropertyStats()
        {
            try
            {
                var userId = GetUserId();
                if (!userId.HasValue)
                {
                    return Unauthorized("User not authenticated");
                }

                var propertyStats = await _dashboardService.GetUserPropertyStatsAsync(userId.Value);
                return Ok(propertyStats);
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"An error occurred: {ex.Message}");
            }
        }

        [HttpGet("transactions")]
        public async Task<ActionResult<List<WalletTransactionDto>>> GetUserTransactions([FromQuery] int count = 10)
        {
            try
            {
                var userId = GetUserId();
                if (!userId.HasValue)
                {
                    return Unauthorized("User not authenticated");
                }

                var transactions = await _dashboardService.GetUserTransactionsAsync(userId.Value, count);
                return Ok(transactions);
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"An error occurred: {ex.Message}");
            }
        }

        [HttpGet("ranking")]
        public async Task<ActionResult<MemberRankingDto>> GetUserRanking()
        {
            try
            {
                var userId = GetUserId();
                if (!userId.HasValue)
                {
                    return Unauthorized("User not authenticated");
                }

                var rankingInfo = await _dashboardService.GetUserMemberRankingInfoAsync(userId.Value);
                return Ok(rankingInfo);
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"An error occurred: {ex.Message}");
            }
        }

        [HttpGet("spending/monthly")]
        public async Task<ActionResult> GetMonthlySpending([FromQuery] int year)
        {
            try
            {
                var userId = GetUserId();
                if (!userId.HasValue)
                {
                    return Unauthorized("User not authenticated");
                }

                var spendingData = await _dashboardService.GetMonthlySpendingAsync(userId.Value, year);
                return Ok(spendingData);
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"An error occurred: {ex.Message}");
            }
        }

        [HttpGet("properties/performance")]
        public async Task<ActionResult> GetPropertyPerformance()
        {
            try
            {
                var userId = GetUserId();
                if (!userId.HasValue)
                {
                    return Unauthorized("User not authenticated");
                }

                var performanceData = await _dashboardService.GetPropertyPerformanceAsync(userId.Value);
                return Ok(performanceData);
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"An error occurred: {ex.Message}");
            }
        }

        [HttpPost("deactivate")]
        public async Task<ActionResult> DeactivateAccount([FromBody] DeactivateUserDto deactivateUserDto)
        {
            if (!ModelState.IsValid)
            {
                _logger.LogWarning("Invalid model state for account deactivation by user {UserId}: {ValidationErrors}",
                    GetUserId(), ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage));
                return BadRequest(ModelState);
            }

            try
            {
                var userId = GetUserId();
                if (!userId.HasValue)
                {
                    LogSecurityEvent(_logger, "UnauthorizedDeactivationAttempt", "User attempted to deactivate account without valid authentication");
                    return Unauthorized(new { Message = "User not authenticated" });
                }

                _logger.LogWarning("User {UserId} is attempting to deactivate their account", userId.Value);
                LogUserAction(_logger, "DeactivateAccount");

                var result = await _userService.DeactivateUserAsync(userId.Value, deactivateUserDto);
                if (!result)
                {
                    _logger.LogWarning("Failed to deactivate account for user {UserId} - invalid credentials", userId.Value);
                    return BadRequest(new { Message = "Failed to deactivate account. Please check your password and try again." });
                }

                _logger.LogWarning("Account successfully deactivated for user {UserId}", userId.Value);
                return Ok(new { Message = "Your account has been deactivated successfully." });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deactivating account for user {UserId}", GetUserId());
                return StatusCode(500, new { Message = "An error occurred while deactivating account. Please try again later." });
            }
        }


        [HttpDelete("permanent-delete")]
        public async Task<ActionResult> PermanentDeleteAccount([FromBody] DeactivateUserDto deactivateUserDto)
        {
            try
            {
                var userId = GetUserId();
                if (!userId.HasValue)
                {
                    LogSecurityEvent(_logger, "UnauthorizedDeletionAttempt", "User attempted to permanently delete account without valid authentication");
                    return Unauthorized(new { Message = "User not authenticated" });
                }

                _logger.LogCritical("User {UserId} is attempting to permanently delete their account", userId.Value);
                LogUserAction(_logger, "PermanentDeleteAccount");

                var result = await _userService.PermanentDeleteUserAsync(userId.Value, deactivateUserDto);
                if (!result)
                {
                    _logger.LogWarning("Failed to permanently delete account for user {UserId} - invalid credentials", userId.Value);
                    return BadRequest(new { Message = "Failed to delete account. Please check your password and try again." });
                }

                _logger.LogCritical("Account permanently deleted for user {UserId}", userId.Value);
                return Ok(new { Message = "Your account and all associated data have been permanently deleted." });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error permanently deleting account for user {UserId}", GetUserId());
                return StatusCode(500, new { Message = "An error occurred while deleting account. Please try again later." });
            }
        }

        [HttpGet("tax-info")]
        public async Task<ActionResult<UserInvoiceInfoDto>> GetUserTaxInfo()
        {
            try
            {
                var userId = GetUserId();
                if (!userId.HasValue)
                {
                    return Unauthorized("User not authenticated");
                }

                var user = await _userService.GetUserByIdAsync(userId.Value);
                if (user == null)
                {
                    return NotFound("User not found");
                }

                return Ok(new {
                    PersonalTaxCode = user.PersonalTaxCode,
                    InvoiceInfo = user.InvoiceInfo
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"An error occurred: {ex.Message}");
            }
        }

        [HttpPut("tax-info")]
        public async Task<ActionResult> UpdateUserTaxInfo([FromBody] UpdateUserTaxInfoDto taxInfoDto)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState); // Returns a 400 Bad Request with validation errors
                }

                var userId = GetUserId();
                if (!userId.HasValue)
                {
                    return Unauthorized("User not authenticated");
                }

                var result = await _userService.UpdateUserTaxInfoAsync(userId.Value, taxInfoDto);
                if (!result)
                {
                    return BadRequest("Failed to update tax information.");
                }

                return Ok(new { Message = "Tax information updated successfully." });
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"An error occurred: {ex.Message}");
            }
        }
    }
}
