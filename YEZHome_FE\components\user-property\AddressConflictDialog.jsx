"use client";

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { useTranslations } from "next-intl";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { AlertCircle } from "lucide-react";

export default function AddressConflictDialog({ open, onOpenChange, currentLocation, newLocation, onConfirmChange, onKeepCurrent }) {
  const t = useTranslations("PropertyForm");

  return (
    <AlertDialog open={open} onOpenChange={onOpenChange}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle className="text-lg font-semibold text-red-600">{t("locationConflictTitle")}</AlertDialogTitle>
          <AlertDialogDescription asChild className="text-base">
            <div className="flex-col items-center gap-2 ">
              <Alert className="bg-teal-100 mb-3">
                <AlertCircle className="h-4 w-4 text-teal-600" />
                <AlertDescription>{t("noteEnterAddress")}</AlertDescription>
              </Alert>
              <div>
                {t("locationConflictDescription", {
                  newLocation: newLocation?.name || "",
                  currentLocation: currentLocation?.name || "",
                })}
              </div>
            </div>
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter className="flex-col space-y-2 sm:space-y-0 sm:flex-row">
          <AlertDialogCancel onClick={onKeepCurrent} className="mt-2 sm:mt-0">
            {t("keepCurrentLocation", { location: currentLocation?.name || "" })}
          </AlertDialogCancel>
          <AlertDialogAction onClick={onConfirmChange} className="bg-teal-600 hover:bg-teal-700">
            {t("switchToNewLocation", { location: newLocation?.name || "" })}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
