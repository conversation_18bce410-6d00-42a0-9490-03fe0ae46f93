{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_8399fde0._.js", "server/edge/chunks/[root-of-the-server]__49146aab._.js", "server/edge/chunks/edge-wrapper_041f7dec.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|_vercel|.*\\.png|.*\\.svg|.*\\.webp|.*\\.jpg$).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|_vercel|.*\\.png|.*\\.svg|.*\\.webp|.*\\.jpg$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "ibuBR8gd9czNjVKcVb89mS9lA91pSiY5k7/21W4re7I=", "__NEXT_PREVIEW_MODE_ID": "c961bcdfbd234f13b790eb0018035946", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "7ca30ac71a18ac9183ef0588942899deba4b71c0f0c75f2a7ab2d09b813cb80f", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "7e2cc57706a6ae69e3a60e19e8ff63d9321e624f055aac961be5db5fd058505f"}}}, "sortedMiddleware": ["/"], "functions": {}}