{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_8399fde0._.js", "server/edge/chunks/[root-of-the-server]__49146aab._.js", "server/edge/chunks/edge-wrapper_041f7dec.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|_vercel|.*\\.png|.*\\.svg|.*\\.webp|.*\\.jpg$).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|_vercel|.*\\.png|.*\\.svg|.*\\.webp|.*\\.jpg$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "ibuBR8gd9czNjVKcVb89mS9lA91pSiY5k7/21W4re7I=", "__NEXT_PREVIEW_MODE_ID": "be2261f98fa0bba801aa4deb79c3c57a", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "4c032f42d0ebaa5bd550d17508a3c8d60d0662a181055c027088f683ba45ee10", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "7fa0a3552824b4e143feb76606c68aa97d8f14bd17f12d80246fc7675be9c4db"}}}, "sortedMiddleware": ["/"], "functions": {}}