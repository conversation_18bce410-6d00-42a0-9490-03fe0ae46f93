"use client"

import { <PERSON><PERSON>, DialogTrigger, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog"
import { But<PERSON> } from "@/components/ui/button"
import { Table, TableHeader, TableRow, TableHead, TableBody, TableCell } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { InfoIcon, CheckCircle2, Clock, Zap, Shield, User, Coins, Award } from "lucide-react"
import { useTranslations } from "next-intl";
import { getMemberRankColor, getMemberRankIcon, getAllMemberRankPrices, getMemberRankThresholds, getMemberRankTranslationKey } from "@/lib/memberRankUtils";

export default function PricingDialog() {
  const t = useTranslations("PricingPage");
  const tCommon = useTranslations("Common");

  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button variant="outline" size="sm" className="gap-1.5">
          <InfoIcon className="h-4 w-4" />
          {t('viewPricing')}
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto p-6 md:p-8">
        <DialogHeader>
          <DialogTitle className="text-2xl font-bold mb-2">{t('pricingTable')}</DialogTitle>
        </DialogHeader>

        <Tabs defaultValue="individual" className="mt-6">
          <TabsList className="grid w-full grid-cols-3 mb-6">
            <TabsTrigger value="individual">{t('individualCustomer')}</TabsTrigger>
            <TabsTrigger value="membership">{t('userRank')}</TabsTrigger>
            <TabsTrigger value="highlight">{t('highlightPost')}</TabsTrigger>
          </TabsList>

          <TabsContent value="individual" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <User className="h-5 w-5 text-primary" />
                  {t('individualCustomerService')}
                </CardTitle>
                <CardDescription>{t('servicePriceIncludes10VAT')}</CardDescription>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader className="bg-muted/50">
                    <TableRow>
                      <TableHead className="w-[40%]">{t('serviceHeader')}</TableHead>
                      <TableHead className="w-[30%]">{t('price')}</TableHead>
                      <TableHead className="w-[30%]">{t('displayTime')}</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    <TableRow className="hover:bg-muted/30">
                      <TableCell className="font-medium">{t('eachPost')}</TableCell>
                      <TableCell className="font-semibold text-primary">55,000</TableCell>
                      <TableCell className="flex items-center gap-1.5">
                        <Clock className="h-4 w-4 text-muted-foreground" />
                        <span>{t('10Days')}</span>
                      </TableCell>
                    </TableRow>
                  </TableBody>
                </Table>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <CheckCircle2 className="h-5 w-5 text-green-500" />
                  {t('memberBenefits')}
                </CardTitle>
                <CardDescription>{t('memberBenefitsDescription')}</CardDescription>
              </CardHeader>
              <CardContent>
                <ul className="space-y-3">
                  <li className="flex items-start gap-2">
                    <Badge rounded="full" variant="outline" className="mt-0.5">
                      <Shield className="h-3.5 w-3.5 mr-1" />
                      {t('trust')}
                    </Badge>
                    <span>{t('trustDescription')}</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <Badge rounded="full" variant="outline" className="mt-0.5">
                      <Zap className="h-3.5 w-3.5 mr-1" />
                      {t('quick')}
                    </Badge>
                    <span>{t('quickDescription')}</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <Badge rounded="full" variant="outline" className="mt-0.5">
                      <Coins className="h-3.5 w-3.5 mr-1" />
                      {t('save')}
                    </Badge>
                    <span>{t('saveDescription')}</span>
                  </li>
                </ul>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="membership">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Award className="h-5 w-5 text-primary" />
                  {t('userRank')}
                </CardTitle>
                <CardDescription>
                  {t('userRankDescription')}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid gap-4 md:grid-cols-2">
                  {getMemberRankThresholds().map((item, index) => (
                    <div
                      key={index}
                      className="flex items-center p-4 rounded-lg border border-border bg-card hover:bg-muted/30 transition-colors"
                    >
                      <div className="mr-4">
                        <div className={`p-2 rounded-full bg-muted ${getMemberRankColor(item.rank)}`}>
                          {getMemberRankIcon(item.rank)}
                        </div>
                      </div>
                      <div>
                        <h4 className={`font-semibold ${getMemberRankColor(item.rank)}`}>{tCommon(getMemberRankTranslationKey(item.rank))}</h4>
                        <p className="text-sm text-muted-foreground">{item.condition}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="highlight">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Zap className="h-5 w-5 text-primary" />
                  {t('highlightCheckpointPrice')}
                </CardTitle>
                <CardDescription>{t('highlightCheckpointDescription')}</CardDescription>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader className="bg-muted/50">
                    <TableRow>
                      <TableHead className="w-[50%]">{t('userRank')}</TableHead>
                      <TableHead className="w-[50%]">{t('price')}</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {getAllMemberRankPrices().map((item, index) => (
                      <TableRow key={index} className="hover:bg-muted/30">
                        <TableCell>
                          <div className="flex items-center gap-2">
                            {getMemberRankIcon(item.rank)}
                            <span className={`font-medium ${getMemberRankColor(item.rank)}`}>{tCommon(getMemberRankTranslationKey(item.rank))}</span>
                          </div>
                        </TableCell>
                        <TableCell className="font-semibold">{item.price}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  )
}

