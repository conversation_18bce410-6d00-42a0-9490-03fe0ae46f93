import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON> } from "@/i18n/navigation";
import PropertyList from "./PropertyList";
import { getPropertyByUser } from "@/app/actions/server/property";
import { Plus } from "lucide-react";
import { getTranslations } from "next-intl/server";

export default async function PropertyListPage() {
  const t = await getTranslations("UserPropertiesPage");
  // Get all properties by default (no status filter)
  const result = await getPropertyByUser(null, 1, 10);

  // Extract items array from the paginated response
  const propertyData = result && result?.success && result?.data && result?.data?.items ? result?.data?.items : [];

  return (
    <div className="min-h-screen bg-white p-6">
      <div className="mb-8 flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">{t("title")}</h1>
          <p className="mt-1 text-sm text-gray-500">{t("description")}</p>
        </div>
        <Button asChild className="gap-2 bg-coral-500 hover:bg-coral-600">
          <Link href="/user/bds/new">
            <Plus className="h-4 w-4" />
            {t("createButton")}
          </Link>
        </Button>
      </div>
      {/* Pass initial data from server to avoid duplicate API calls */}
      <PropertyList initialData={propertyData} />
    </div>
  );
}
