"use client"

import { motion } from "framer-motion"
import { Check } from "lucide-react"
import { Button } from "@/components/ui/button"
import { useRouter } from "next/navigation"
import { formatCurrency } from "@/lib/utils"

export function ConfirmationScreen({
  amount,
  selectedMethod,
  transactionId,
  paymentMethods,
  t
}) {
  const router = useRouter();

  const handleClose = () => {
    router.push('/user/wallet');
  };

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.95 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ duration: 0.3 }}
      className="text-center py-6"
    >
      <div className="mx-auto w-16 h-16 bg-teal-100 rounded-full flex items-center justify-center mb-4">
        <Check className="h-8 w-8 text-teal-600" />
      </div>
      <h3 className="text-xl font-semibold mb-2">{t('dialogTitleSuccess')}</h3>
      <p className="text-gray-500 mb-6">{t('dialogMessageSuccess', { amount: formatCurrency(amount) })}</p>

      <div className="bg-gray-50 rounded-lg p-4 mb-6">
        <div className="flex justify-between text-sm mb-2">
          <span className="text-gray-500">{t('dialogTransactionIdLabel')}</span>
          <span className="font-medium">{transactionId}</span>
        </div>
        <div className="flex justify-between text-sm mb-2">
          <span className="text-gray-500">{t('dialogDateLabel')}</span>
          <span className="font-medium">{new Date().toLocaleDateString()}</span>
        </div>
        <div className="flex justify-between text-sm">
          <span className="text-gray-500">{t('dialogPaymentMethodLabel')}</span>
          <span className="font-medium">{t(paymentMethods.find((m) => m.id === selectedMethod)?.name)}</span>
        </div>
      </div>

      <Button className="w-full bg-teal-600 hover:bg-teal-700" onClick={handleClose}>
        {t('dialogCloseButton')}
      </Button>
    </motion.div>
  )
}
