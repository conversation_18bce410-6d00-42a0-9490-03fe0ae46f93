export async function GET(request) {
  try {
    const API_URL = process.env.API_URL;
    const { searchParams } = new URL(request.url);
    const cityId = searchParams.get('cityId');
    
    if (!cityId) {
      return Response.json(
        { error: 'City ID is required' },
        { status: 400 }
      );
    }

    const response = await fetch(`${API_URL}/api/Address/cities/${cityId}/districts`, {
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch districts: ${response.status}`);
    }

    const data = await response.json();
    return Response.json(data);
  } catch (error) {
    console.error('Error fetching districts:', error);
    return Response.json(
      { error: 'Failed to fetch districts' },
      { status: 500 }
    );
  }
}
