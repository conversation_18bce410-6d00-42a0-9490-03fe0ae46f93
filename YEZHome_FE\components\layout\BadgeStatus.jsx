import { PropertyStatus } from "@/lib/enum";
import { cn, formatStatusText } from "@/lib/utils";
import { Badge } from "../ui/badge";

const STATUS_STYLES = {
  [PropertyStatus.DRAFT]: "bg-gray-100 text-gray-700",
  [PropertyStatus.SOLD]: "bg-sky-100 text-sky-700",
  [PropertyStatus.EXPIRED]: "bg-amber-100 text-amber-700",
  [PropertyStatus.APPROVED]: "bg-emerald-100 text-emerald-700",
  [PropertyStatus.PENDING_APPROVAL]: "bg-yellow-100 text-yellow-700",
  [PropertyStatus.REJECTED_BY_ADMIN]: "bg-rose-100 text-rose-700",
  [PropertyStatus.REJECTED_DUE_TO_UNPAID]: "bg-red-100 text-red-700",
  [PropertyStatus.WAITING_PAYMENT]: "bg-orange-100 text-orange-700",
};

export default function BadgeStatus({ status, statusText, className }) {
  const style = status ? STATUS_STYLES[status] : "bg-yellow-500 text-white";
  return (
    <Badge rounded="full" variant="ghost" className={cn(`inline-flex items-center justify-center text-sm font-medium px-2 mt-3 ml-3`, style, className)}>
      {statusText || formatStatusText(status)}
    </Badge>
  );
}
