using System.ComponentModel.DataAnnotations;

namespace RealEstate.Application.DTO
{
    public class UpdateUserTaxInfoDto
    {
        [RegularExpression(@"^(\d{10}|\d{10}-\d{3})$", ErrorMessage = "Personal Tax Code must be 10 digits or 10 digits followed by a hyphen and 3 digits")]
        public string? PersonalTaxCode { get; set; }
        
        public UserInvoiceInfoDto? InvoiceInfo { get; set; }
    }
}
