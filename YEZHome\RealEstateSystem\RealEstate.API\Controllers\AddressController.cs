﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using RealEstate.Domain.Entities;
using RealEstate.Infrastructure;

namespace RealEstate.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class AddressController : ControllerBase
    {
        private readonly ApplicationDbContext _context;
        private readonly ILogger<AddressController> _logger;

        public AddressController(ApplicationDbContext context, ILogger<AddressController> logger)
        {
            _context = context;
            _logger = logger;
        }

        [HttpGet("cities")]
        public async Task<ActionResult<IEnumerable<City>>> GetCitiesAsync()
        {
            try
            {
                _logger.LogInformation("Retrieving all cities from IP {IpAddress}", HttpContext.Connection.RemoteIpAddress?.ToString());

                var cities = await _context.City.OrderBy(x => x.Id).ToListAsync();

                _logger.LogInformation("Successfully retrieved {Count} cities", cities.Count);
                return Ok(cities);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving cities");
                return StatusCode(500, new { Message = "An error occurred while retrieving cities. Please try again later." });
            }
        }

        [HttpGet("cities/{cityId}/districts")]
        public async Task<ActionResult<IEnumerable<District>>> GetDistrictByCityAsync(int cityId)
        {
            try
            {
                _logger.LogInformation("Retrieving districts for city {CityId} from IP {IpAddress}",
                    cityId, HttpContext.Connection.RemoteIpAddress?.ToString());

                var districts = await _context.District.Where(d => d.CityId == cityId).OrderBy(x => x.NameWithType).ToListAsync();
                if (districts == null || !districts.Any())
                {
                    _logger.LogWarning("No districts found for city {CityId}", cityId);
                    return NotFound(new { Message = "No districts found for the specified city" });
                }

                _logger.LogInformation("Successfully retrieved {Count} districts for city {CityId}", districts.Count, cityId);
                return Ok(districts);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving districts for city {CityId}", cityId);
                return StatusCode(500, new { Message = "An error occurred while retrieving districts. Please try again later." });
            }
        }

        [HttpGet("districts/{districtId}/wards")]
        public async Task<ActionResult<IEnumerable<Ward>>> GetWardByDistrictAsync(int districtId)
        {
            try
            {
                _logger.LogInformation("Retrieving wards for district {DistrictId} from IP {IpAddress}",
                    districtId, HttpContext.Connection.RemoteIpAddress?.ToString());

                var wards = await _context.Ward.Where(d => d.DistrictId == districtId).OrderBy(x => x.NameWithType).ToListAsync();
                if (wards == null || !wards.Any())
                {
                    _logger.LogWarning("No wards found for district {DistrictId}", districtId);
                    return NotFound(new { Message = "No wards found for the specified district" });
                }

                _logger.LogInformation("Successfully retrieved {Count} wards for district {DistrictId}", wards.Count, districtId);
                return Ok(wards);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving wards for district {DistrictId}", districtId);
                return StatusCode(500, new { Message = "An error occurred while retrieving wards. Please try again later." });
            }
        }

        [HttpGet("districts/{districtId}/streets")]
        public async Task<ActionResult<IEnumerable<Street>>> GetStreetByDistrictAsync(int districtId)
        {
            try
            {
                _logger.LogInformation("Retrieving streets for district {DistrictId} from IP {IpAddress}",
                    districtId, HttpContext.Connection.RemoteIpAddress?.ToString());

                var streets = await _context.Street.Where(s => s.DistrictId == districtId).ToListAsync();
                if (streets == null || !streets.Any())
                {
                    _logger.LogWarning("No streets found for district {DistrictId}", districtId);
                    return NotFound(new { Message = "No streets found for the specified district" });
                }

                _logger.LogInformation("Successfully retrieved {Count} streets for district {DistrictId}", streets.Count, districtId);
                return Ok(streets);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving streets for district {DistrictId}", districtId);
                return StatusCode(500, new { Message = "An error occurred while retrieving streets. Please try again later." });
            }
        }

        [HttpGet("wards/{wardId}/streets/{streetId}/projects")]
        public async Task<ActionResult<IEnumerable<Project>>> GetProjectsByWardStreetAsync(int wardId, int streetId)
        {
            var projects = await _context.Project.Where(p => p.WardId == wardId && p.StreetId == streetId).ToListAsync();
            if (projects == null || !projects.Any())
            {
                return NotFound();
            }
            return projects;
        }
    }
}
