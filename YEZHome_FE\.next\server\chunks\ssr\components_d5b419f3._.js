module.exports = {

"[project]/components/layout/NoData.jsx [app-ssr] (ecmascript, next/dynamic entry, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/_c1d952b2._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/components/layout/NoData.jsx [app-ssr] (ecmascript, next/dynamic entry)");
    });
});
}}),
"[project]/components/user-property/PropertyCard.jsx [app-ssr] (ecmascript, next/dynamic entry, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/[root-of-the-server]__f9e92b3f._.js",
  "server/chunks/ssr/node_modules_1fa43798._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/components/user-property/PropertyCard.jsx [app-ssr] (ecmascript, next/dynamic entry)");
    });
});
}}),

};