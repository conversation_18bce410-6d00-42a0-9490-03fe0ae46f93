﻿using System.Linq.Expressions;

namespace RealEstate.Domain.Interfaces
{
    public interface IRepositoryInt<T> where T : IEntity
    {
        Task<IEnumerable<T>> GetAllAsync(bool asNoTracking = true, params Expression<Func<T, object>>[] includes);
        Task<T> GetByIdAsync(int id, bool asNoTracking = true, params Expression<Func<T, object>>[] includes);
        Task<IEnumerable<T>> FindAsync(Expression<Func<T, bool>> predicate, bool asNoTracking = true, params Expression<Func<T, object>>[] includes);
        Task AddAsync(T entity);
        void Update(T entity);
        void Remove(T entity);
        void RestoreAsync(T entity);
    }
}
