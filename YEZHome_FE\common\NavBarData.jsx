import { Chart<PERSON>ie, Layers, MessageSquareDot, Settings, User } from "lucide-react";

export const NavBarData = [
  {
    id: "home",
    name: "Trang chủ",
    url: "/",
    subnav: [],
  },
  {
    id: "post",
    name: "<PERSON> tức",
    url: "/tin-tuc",
    subnav: [],
  },
  {
    id: "about",
    name: "<PERSON>iớ<PERSON> thiệu",
    url: "/gioi-thieu",
    subnav: [],
  },
  {
    id: "contact",
    name: "<PERSON><PERSON><PERSON> hệ",
    url: "/lien-he",
    subnav: [],
  },
];

export const sidebarMenuItems = [
  { href: "/user/dashboard", label: "Tổng quan", icon: Chart<PERSON>ie },
  { href: "/user/profile", label: "Thông tin cá nhân", icon: User },
  { href: "/user/bds", label: "BĐS của tôi", icon: Layers },
  { href: "/user/notifications", label: "Thông báo", icon: MessageSquareDot },
  { href: "/user/setting", label: "Ti<PERSON>n ích", icon: Settings },
];
