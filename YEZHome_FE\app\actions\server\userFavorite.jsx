"use server";

import { handleErrorResponse, logError } from "@/lib/apiUtils";
import { fetchWithAuth } from "@/lib/sessionUtils";

const API_BASE_URL = `${process.env.API_URL}/api/UserFavorites`;

export async function addToFavorites(propertyId) {
  try {
    return await fetchWithAuth(`${API_BASE_URL}/add`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ propertyId }),
    });
  } catch (error) {
    logError("UserService", error, {
      action: "addToFavorites",
      propertyId,
    });
    return handleErrorResponse(false, null, "Đã xảy ra lỗi khi thêm vào danh sách yêu thích");
  }
}

export async function removeFromFavorites(propertyId) {
  try {
    return await fetchWithAuth(`${API_BASE_URL}/remove/${propertyId}`, {
      method: "DELETE",
      headers: {
        "Content-Type": "application/json",
      },
    });
  } catch (error) {
    logError("UserService", error, {
      action: "removeFromFavorites",
      propertyId,
    });
    return handleErrorResponse(false, null, "Đã xảy ra lỗi khi xóa khỏi danh sách yêu thích");
  }
}

export async function checkFavoriteStatus(propertyIds) {
  try {
    return await fetchWithAuth(`${API_BASE_URL}/check`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ propertyIds: Array.isArray(propertyIds) ? propertyIds : [propertyIds] }),
    });
  } catch (error) {
    logError("UserService", error, {
      action: "checkFavoriteStatus",
      propertyIds,
    });
    return handleErrorResponse(false, null, "Đã xảy ra lỗi khi kiểm tra trạng thái yêu thích");
  }
}

export async function getFavoritesCount() {
  try {
    return await fetchWithAuth(`${API_BASE_URL}/count`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
    });
  } catch (error) {
    logError("UserService", error, {
      action: "getFavoritesCount",
    });
    return handleErrorResponse(false, null, "Đã xảy ra lỗi khi lấy số lượng bất động sản yêu thích");
  }
}

/**
 * Gets the user's favorite properties
 * @returns {Promise<{success: boolean, data: Array<UserFavoriteDto>, message: string}>} Response with array of UserFavoriteDto objects
 * @description UserFavoriteDto contains: id, propertyId, createdAt
 */
export async function getUserFavorites() {
  try {
    return await fetchWithAuth(`${API_BASE_URL}/favorites`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
    });
  } catch (error) {
    logError("UserService", error, {
      action: "getUserFavorites",
    });
    return handleErrorResponse(false, null, "Đã xảy ra lỗi khi lấy danh sách bất động sản yêu thích");
  }
}

/**
 * Gets the user's favorite properties with full property details, pagination and filtering
 * @param {Object} filters - Filter options
 * @param {number} filters.minPrice - Minimum price filter
 * @param {number} filters.maxPrice - Maximum price filter
 * @param {string} filters.fromDate - Start date filter (ISO string)
 * @param {string} filters.toDate - End date filter (ISO string)
 * @param {string} filters.sortBy - Sort field (CreatedAt, Price)
 * @param {boolean} filters.sortDescending - Sort direction
 * @param {number} filters.page - Page number
 * @param {number} filters.pageSize - Items per page
 * @returns {Promise<{success: boolean, data: PagedFavoriteResultDto, message: string}>} Response with paginated favorites and property details
 */
export async function getUserFavoritesWithDetails(filters = {}) {
  try {
    const queryParams = new URLSearchParams();

    if (filters.minPrice !== undefined && filters.minPrice !== null) {
      queryParams.append('minPrice', filters.minPrice.toString());
    }
    if (filters.maxPrice !== undefined && filters.maxPrice !== null) {
      queryParams.append('maxPrice', filters.maxPrice.toString());
    }
    if (filters.fromDate) {
      queryParams.append('fromDate', filters.fromDate);
    }
    if (filters.toDate) {
      queryParams.append('toDate', filters.toDate);
    }
    if (filters.sortBy) {
      queryParams.append('sortBy', filters.sortBy);
    }
    if (filters.sortDescending !== undefined) {
      queryParams.append('sortDescending', filters.sortDescending.toString());
    }
    if (filters.page) {
      queryParams.append('page', filters.page.toString());
    }
    if (filters.pageSize) {
      queryParams.append('pageSize', filters.pageSize.toString());
    }

    const url = `${API_BASE_URL}/favorites-with-details${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;

    return await fetchWithAuth(url, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
    });
  } catch (error) {
    logError("UserService", error, {
      action: "getUserFavoritesWithDetails",
      filters,
    });
    return handleErrorResponse(false, null, "Đã xảy ra lỗi khi lấy danh sách bất động sản yêu thích");
  }
}
