"use server";

import { cookies } from "next/headers";
import { jwtDecode } from "jwt-decode";
import { redirect } from "next/navigation";
import { jwtVerify } from "jose";

const SECRET_KEY_STRING = process.env.JWT_SECRET;

const SECRET_KEY = new TextEncoder().encode(SECRET_KEY_STRING);

export async function createSession(name, value, options) {
  const cookieStore = await cookies();

  const defaultOptions = {
    secure: true,
    httpOnly: true,
    expires: Date.now() + 24 * 60 * 60 * 1000, // 1 days
    path: "/",
    sameSite: "strict",
  };

  cookieStore.set(name, value, {
    ...defaultOptions,
    ...options,
  });
}

export async function getSession(name) {
  const cookieStore = await cookies();

  return cookieStore.get(name)?.value;
}

export async function deleteSession(name) {
  const cookieStore = await cookies();
  cookieStore.delete(name);
}

export async function fetchWithAuth(url, options = {}) {
  try {
    const token = await getSession("Authorization"); // Lấy token từ session
    if (!token) {
      console.log("No token found");
      return {
        success: false,
        errorType: "no_token",
        message: "User is not logged in.",
      };
    }

    console.log(`[${options.method}]: ${url} - Fetching with auth token: ${url} `);
    console.log(`BODY: ${options?.body}`);

    const response = await fetch(url, {
      ...options,
      headers: {
        Authorization: `Bearer ${token}`,
        ...options.headers,
      },
      credentials: "include",
    });

    // Xử lý lỗi 401 (Unauthorized)
    if (response.status === 401) {
      try {
        console.log(response);
        // Xử lý lỗi token hết hạn nếu có 401 nhưng token vẫn có trong cooike thì xóa token => vì token đã hết hạn
        if (token) {
          console.warn(`Token expired. Clearing session...${url}`);
          return {
            success: false,
            errorType: "token_expired",
            message: "Phiên đăng nhập của bạn đã hết hạn. Vui lòng đăng nhập lại.",
          };
        }
      } catch (error) {
        console.warn("Lỗi khi xử lý phản hồi 401:", error);
      }

      return {
        success: false,
        errorType: "unauthorized",
        message: "401. Vui lòng đăng nhập.",
      };
    }

    // Xử lý lỗi chung
    if (!response.ok) {
      console.error(`API request failed: ${response.status} ${response.statusText}`);

      const errorData = await response.json();
      console.error("Error data:", errorData);

      let message = `Code: ${response.status} - ${response.statusText}: Đã xảy ra lỗi khi xử lý dữ liệu.`;
      if (errorData?.detail || errorData?.message) {
        message = errorData?.detail || errorData?.message;
      } else if (typeof errorData === "object") {
        for (const key in errorData) {
          if (errorData.hasOwnProperty(key)) {
            message = errorData[key];
            break;
          }
        }
      }

      return {
        success: false,
        errorType: (errorData && errorData?.errorType) || "api_error",
        message: message,
      };
    }

    // Nếu gọi API thành công, trả về No Content 204 hoặc 201
    if (response.status === 204 || response.status === 201) {
      return {
        success: true,
        data: null,
      };
    } else {
      // Nếu gọi API thành công, trả về dữ liệu chuẩn
      const data = await response.json();
      return {
        success: true,
        data,
      };
    }
  } catch (error) {
    console.error("Fetch error:", error);
    return {
      success: false,
      errorType: "network_error",
      message:
        errorData &&
        (errorData?.detail ||
          errorData?.message ||
          `Code: ${response?.status} - ${response?.statusText}: Failed to connect to the server. Please try again later.`),
    };
  }
}

export async function fetchWithoutAuth(url, options = {}) {
  try {
    console.log(`[${options.method || "GET"}]: ${url} - Fetching without auth`);
    if (options?.body) {
      console.log(`BODY: ${options.body}`);
    }

    const response = await fetch(url, {
      ...options,
      headers: {
        ...(options.headers || {}),
      },
    });

    if (!response.ok) {
      console.error(`Public API request failed: ${response.status} ${response.statusText}`);
      let errorData;
      try {
        errorData = await response.json();
      } catch (e) {
        errorData = { message: response.statusText };
      }
      return {
        success: false,
        errorType: (errorData && errorData?.errorType) || "api_error",
        message: errorData?.detail || errorData?.message || `Code: ${response.status} - ${response.statusText}: Đã xảy ra lỗi khi xử lý dữ liệu.`,
      };
    }

    if (response.status === 204 || response.status === 201) {
      return {
        success: true,
        data: null,
      };
    } else {
      const data = await response.json();
      return {
        success: true,
        data,
      };
    }
  } catch (error) {
    console.error("Fetch error (without auth):", error);
    return {
      success: false,
      errorType: "network_error",
      message: `Failed to connect to the server. Please try again later. (${error.message || "Unknown network error"})`,
    };
  }
}

export async function clearSessionAndBackToLogin() {
  const cookieStore = await cookies();
  cookieStore.delete("Authorization"); // Remove auth token
  redirect("/dang-nhap"); // Redirect to login page
}

export async function getJwtInfo() {
  const cookieStore = await cookies();
  const decodedToken = jwtDecode(cookieStore.get("Authorization")?.value);
  return decodedToken;
}

export async function verifyJwtToken(token) {
  try {
    const { payload } = await jwtVerify(token, SECRET_KEY);

    return payload;
  } catch (error) {
    // Xử lý các lỗi cụ thể do 'jose' ném ra (TokenExpiredError, JWSInvalid...)
    // Các lỗi này là runtime error do bản thân token, không phải lỗi cấu hình
    // Log các lỗi này ở mức độ thấp hơn (warn, error)
    if (error.name === 'JOSEError' && error.message === 'signature verification failed') {
         console.warn('JWT verification failed: Invalid signature.');
    } else if (error.name === 'JWTExpired') {
         console.warn('JWT verification failed: Token has expired.');
         // Bạn có thể return một giá trị đặc biệt hoặc ném lỗi khác
         // nếu middleware cần phân biệt hết hạn và invalid signature
    } else {
      console.error('Unexpected JWT verification error:', error);
    }

    return null; // Trả về null nếu token không hợp lệ hoặc hết hạn
  }
}
