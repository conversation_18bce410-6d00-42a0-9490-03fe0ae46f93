"use server";

import { handleErrorResponse, logError } from "@/lib/apiUtils";
import { fetchWithAuth } from "@/lib/sessionUtils";

const API_USER_BASE_URL = `${process.env.API_URL}/api/user`;
const API_WALLET_TRANSACTION_BASE_URL = `${process.env.API_URL}/api/WalletTransaction`;

export async function getUserDashboard() {
  try {
    return await fetchWithAuth(`${API_USER_BASE_URL}/dashboard`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
    });
  } catch (error) {
    logError("UserService", error, {
      action: "getUserDashboard",
    });
    return handleErrorResponse(false, null, "Đã xảy ra lỗi khi lấy thông tin dashboard");
  }
}

export async function getUserWallet() {
  try {
    return await fetchWithAuth(`${API_WALLET_TRANSACTION_BASE_URL}/balance`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
    });
  } catch (error) {
    logError("UserService", error, {
      action: "getUserWallet",
    });
    return handleErrorResponse(false, null, "Đã xảy ra lỗi khi lấy thông tin ví");
  }
}

export async function getUserPropertyStats() {
  try {
    return await fetchWithAuth(`${API_USER_BASE_URL}/properties/stats`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
    });
  } catch (error) {
    logError("UserService", error, {
      action: "getUserPropertyStats",
    });
    return handleErrorResponse(false, null, "Đã xảy ra lỗi khi lấy thống kê bất động sản");
  }
}

export async function getUserTransactions(count = 10) {
  try {
    return await fetchWithAuth(`${API_USER_BASE_URL}/transactions?pageSize=${count}`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
    });
  } catch (error) {
    logError("UserService", error, {
      action: "getUserTransactions",
      count,
    });
    return handleErrorResponse(false, null, "Đã xảy ra lỗi khi lấy lịch sử giao dịch");
  }
}

export async function getUserRanking() {
  try {
    return await fetchWithAuth(`${API_USER_BASE_URL}/ranking`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
    });
  } catch (error) {
    logError("UserService", error, {
      action: "getUserRanking",
    });
    return handleErrorResponse(false, null, "Đã xảy ra lỗi khi lấy thông tin hạng thành viên");
  }
}

export async function getMonthlySpending(year) {
  try {
    return await fetchWithAuth(`${API_USER_BASE_URL}/spending/monthly?year=${year}`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
    });
  } catch (error) {
    logError("UserService", error, {
      action: "getMonthlySpending",
      year,
    });
    return handleErrorResponse(false, null, "Đã xảy ra lỗi khi lấy dữ liệu chi tiêu theo tháng");
  }
}

export async function getPropertyPerformance() {
  try {
    return await fetchWithAuth(`${API_USER_BASE_URL}/properties/performance`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
    });
  } catch (error) {
    logError("UserService", error, {
      action: "getPropertyPerformance",
    });
    return handleErrorResponse(false, null, "Đã xảy ra lỗi khi lấy hiệu suất bất động sản");
  }
}

export async function topUpWallet(amount, paymentMethod) {
  try {
    // Create a pending transaction
    const pendingTransaction = await fetchWithAuth(`${API_WALLET_TRANSACTION_BASE_URL}/topup`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        amount,
        paymentMethod,
      }),
    });

    if (!pendingTransaction.success) {
      return pendingTransaction;
    }

    const { orderId, callbackUrl, redirectUrl } = pendingTransaction.data;

    // For bank transfers, we don't need to create a payment gateway transaction
    if (paymentMethod === "banking") {
      return pendingTransaction;
    }

    // For other payment methods, create a payment gateway transaction
    let paymentResult;

    if (paymentMethod === "momo") {
      const { createMomoPayment } = await import("@/app/services/payment");
      paymentResult = await createMomoPayment(
        amount,
        orderId,
        redirectUrl,
        callbackUrl,
        { userId: pendingTransaction.data.userId }
      );
    } else if (paymentMethod === "card") {
      paymentResult = {
        success: true,
        paymentUrl: `/user/wallet/card-payment?orderId=${orderId}`,
      };
    }

    if (!paymentResult.success) {
      // If payment gateway fails, cancel the pending transaction
      await fetchWithAuth(`${API_WALLET_TRANSACTION_BASE_URL}/${orderId}/cancel`, {
        method: "POST",
      });

      return {
        success: false,
        message: paymentResult.error || "Không thể xử lý thanh toán",
        errorType: "payment_gateway_error",
      };
    }

    // Update the transaction with payment gateway info
    await fetchWithAuth(`${API_WALLET_TRANSACTION_BASE_URL}/${orderId}/update`, {
      method: "PATCH",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        gatewayTransactionId: paymentResult.transactionId,
        paymentUrl: paymentResult.paymentUrl,
      }),
    });

    return {
      success: true,
      data: {
        orderId,
        paymentUrl: paymentResult.paymentUrl,
        message: "Giao dịch đã được tạo thành công",
      },
    };
  } catch (error) {
    logError("UserService", error, {
      action: "topUpWallet",
      amount,
      paymentMethod,
    });
    return handleErrorResponse(false, null, "Đã xảy ra lỗi khi nạp tiền vào ví");
  }
}

// Verify bank transfer
export async function verifyBankTransfer(orderId, transactionInfo) {
  try {
    return await fetchWithAuth(`${API_WALLET_TRANSACTION_BASE_URL}/${orderId}/verify-transfer`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(transactionInfo),
    });
  } catch (error) {
    logError("UserService", error, {
      action: "verifyBankTransfer",
      orderId,
    });
    return handleErrorResponse(false, null, "Đã xảy ra lỗi khi xác minh giao dịch chuyển khoản");
  }
}

// Check payment status
export async function checkPaymentStatus(orderId) {
  try {
    return await fetchWithAuth(`${API_WALLET_TRANSACTION_BASE_URL}/${orderId}/status`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
    });
  } catch (error) {
    logError("UserService", error, {
      action: "checkPaymentStatus",
      orderId,
    });
    return handleErrorResponse(false, null, "Đã xảy ra lỗi khi kiểm tra trạng thái thanh toán");
  }
}

// Get payment methods and settings
export async function getPaymentSettings() {
  try {
    return await fetchWithAuth(`${API_USER_BASE_URL}/payment-settings`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
    });
  } catch (error) {
    logError("UserService", error, {
      action: "getPaymentSettings",
    });
    return handleErrorResponse(false, null, "Đã xảy ra lỗi khi lấy cài đặt thanh toán");
  }
}

export async function verifyUserRank() {
  try {
    const response = await fetchWithAuth(`${API_USER_BASE_URL}/verify-user-rank`, {
      method: "GET",
    });

    return response;
  } catch (error) {
    console.error("Error verifying user rank:", error);
    return {
      success: false,
      message: "Đã xảy ra lỗi khi xác minh thứ hạng người dùng",
      errorType: "network_error",
    };
  }
}

// Get user tax information
export async function getUserTaxInfo() {
  try {
    return await fetchWithAuth(`${API_USER_BASE_URL}/tax-info`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
    });
  } catch (error) {
    logError("UserService", error, {
      action: "getUserTaxInfo",
    });
    return handleErrorResponse(false, null, "Đã xảy ra lỗi khi lấy thông tin thuế");
  }
}

// Update user tax information
export async function updateUserTaxInfo(taxInfo) {
  try {
    return await fetchWithAuth(`${API_USER_BASE_URL}/tax-info`, {
      method: "PUT",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(taxInfo),
    });
  } catch (error) {
    logError("UserService", error, {
      action: "updateUserTaxInfo",
    });
    return handleErrorResponse(false, null, "Đã xảy ra lỗi khi cập nhật thông tin thuế");
  }
}

// Deactivate user account
export async function deactivateUserAccount(data) {
  try {
    return await fetchWithAuth(`${API_USER_BASE_URL}/deactivate`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(data),
    });
  } catch (error) {
    logError("UserService", error, {
      action: "deactivateUserAccount",
    });
    return handleErrorResponse(false, null, "Đã xảy ra lỗi khi khóa tài khoản");
  }
}

// Request permanent account deletion
export async function requestAccountDeletion(data) {
  try {
    return await fetchWithAuth(`${API_USER_BASE_URL}/permanent-delete`, {
      method: "DELETE",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(data),
    });
  } catch (error) {
    logError("UserService", error, {
      action: "requestAccountDeletion",
    });
    return handleErrorResponse(false, null, "Đã xảy ra lỗi khi yêu cầu xóa tài khoản");
  }
}

export async function uploadAvatar(file) {
  try {
    const formData = new FormData();
    formData.append('file', file);

    return await fetchWithAuth(`${process.env.API_URL}/api/UserAvatar/upload`, {
      method: "POST",
      body: formData,
    });
  } catch (error) {
    logError("UserService", error, {
      action: "uploadAvatar",
    });
    return handleErrorResponse(false, null, "Đã xảy ra lỗi khi tải lên ảnh đại diện");
  }
}

export async function deleteAvatar() {
  try {
    return await fetchWithAuth(`${process.env.API_URL}/api/UserAvatar`, {
      method: "DELETE",
    });
  } catch (error) {
    logError("UserService", error, {
      action: "deleteAvatar",
    });
    return handleErrorResponse(false, null, "Đã xảy ra lỗi khi xóa ảnh đại diện");
  }
}
