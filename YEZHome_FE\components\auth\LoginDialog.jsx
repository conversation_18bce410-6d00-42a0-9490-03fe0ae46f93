"use client";
import { useTranslations } from "next-intl";
import { Link } from "@/i18n/navigation";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogClose } from "@/components/ui/dialog";
import LoginForm from "@/components/auth/LoginForm";

function LoginDialog({ open, onOpenChange }) {
  const t = useTranslations("PropertyList");

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>{t("loginRequired")}</DialogTitle>          
        </DialogHeader>
        <div className="px-6 py-4">
          <LoginForm />
          <div className="mt-4 text-center">
            <p className="text-sm text-gray-600">
              {t("dontHaveAccount")}{" "}
              <Link href="/dang-ky" className="text-coral-500 hover:text-coral-600 font-medium">
                {t("signUpHere")}
              </Link>
            </p>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}

export default LoginDialog; 