﻿using RealEstate.Application.DTO;
using RealEstate.Domain.CustomModel;

namespace RealEstate.Application.Interfaces
{
    public interface IBlogService
    {
        Task<BlogPostDto> GetBlogByIdAsync(Guid id);
        Task<BlogPostDto> GetBlogBySlugAsync(string slug);
        Task<IEnumerable<BlogPostDto>> GetAllBlogAsync();
        Task<PagedResult<BlogPostDto>> GetBlogAsync(PagingRequest paging, string title);
        Task<BlogPostDto> CreateBlogAsync(CreateBlogPostDto BlogDto, Guid userId);
        Task<bool> UpdateBlogAsync(Guid id, CreateBlogPostDto BlogDto, Guid userId);
        Task<bool> DeleteBlogAsync(Guid id, Guid userId);
    }
}
