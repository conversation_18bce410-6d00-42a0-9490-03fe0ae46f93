# Project Documentation
YEZHOME is a platform for buying, selling, and renting real estate, designed for users with genuine real estate transaction needs.


## Table of Contents
1. [Project Structure](#project-structure)
2. [Technology Stack](#technology-stack)
3. [Component Architecture](#component-architecture)
4. [Naming Conventions](#naming-conventions)
5. [UI Components](#ui-components)
6. [State Management](#state-management)
7. [Utilities and Helpers](#utilities-and-helpers)

## Project Structure

project-root/
├── app/
│ ├── actions/
│ │ └── server/
│ │ └── property.js
│ └── page.jsx
├── components/
│ ├── layout/
│ │ ├── Navbar.jsx
│ │ └── NoData.jsx
│ └── ui/
│ ├── button.jsx
│ ├── input.jsx
│ ├── MapSection.jsx
│ ├── PropertyList.jsx
│ ├── SearchFilter.jsx
│ └── [other UI components]
├── common/
│ └── NavBarData.js
└── hooks/

## Technology Stack

### Core Technologies
- **Next.js** - React framework with server-side rendering capabilities
- **React** - Frontend library for building user interfaces
- **Tailwind CSS** - Utility-first CSS framework

### UI Libraries
- **Lucide React** - Icon library
- **Shadcn UI** - Component library (customized components)
- **Goong Maps** - Mapping service for Vietnam

### Development Tools
- ESLint - Code linting
- Prettier - Code formatting

## Component Architecture

### Layout Components
1. **Navbar.jsx**
   - Main navigation component
   - Handles user authentication state
   - Responsive design with mobile menu
   - Uses Tooltip, Dropdown, and Sheet components

2. **NoData.jsx**
   - Reusable empty state component
   - Configurable message and action button
   - Uses Image component for illustration

### UI Components
1. **SearchFilter.jsx**
   - Complex filter component with multiple filter options
   - Uses Popover for advanced filters
   - Implements controlled form inputs
   - Handles multiple filter states

2. **MapSection.jsx**
   - Integrates with Goong Maps
   - Displays property locations
   - Handles map interactions and events

3. **PropertyList.jsx**
   - Displays filtered property listings
   - Implements pagination or infinite scroll

## Naming Conventions

### Components
- PascalCase for component names (e.g., `SearchFilter`, `PropertyList`)
- Suffix with `.jsx` for React components
- Descriptive names indicating functionality

### Variables and Functions
- camelCase for variables and functions
- Boolean variables prefixed with 'is' or 'has' (e.g., `isLoggedIn`, `hasCreateButton`)
- Event handlers prefixed with 'on' (e.g., `onFilterChange`, `onOpenChange`)

### CSS Classes
- Uses Tailwind CSS utility classes
- Custom classes in kebab-case when needed
- BEM-like naming for custom components

## UI Components

### Base Components
- Button
- Input
- Select
- Popover
- Tooltip
- Dialog
- Sheet
- Badge
- Separator

### Form Components
- Radio Group
- Checkbox
- Label
- Input Range

### Layout Components
- Avatar
- Card
- Sheet
- Dropdown Menu

## State Management

### Local State
- Uses React's useState for component-level state
- Implements controlled components for form inputs
- Uses temporary states for popover contents

### Server State
- Implements server actions for data fetching
- Uses async/await for API calls
- Handles loading and error states

### Props Pattern
- Follows prop drilling for shallow component trees
- Uses callback props for parent-child communication
- Implements prop validation where necessary

## Utilities and Helpers

### Toast Notifications
- Custom hook: `use-toast`
- Provides feedback for user actions
- Handles success and error states

### Navigation
- Uses Next.js Link component
- Implements dynamic routing
- Handles authentication-based navigation

### Data Formatting
- Implements consistent data transformation
- Handles currency and measurement formatting
- Validates input data

## Best Practices

1. **Component Organization**
   - Separate layout and UI components
   - Keep components focused and single-responsibility
   - Use composition over inheritance

2. **Performance Optimization**
   - Implement debouncing for search inputs
   - Use memo for expensive computations
   - Lazy load components when appropriate

3. **Error Handling**
   - Implement try-catch blocks for async operations
   - Display user-friendly error messages
   - Log errors for debugging

4. **Accessibility**
   - Use semantic HTML elements
   - Implement ARIA labels
   - Ensure keyboard navigation

5. **Responsive Design**
   - Mobile-first approach
   - Use Tailwind breakpoints consistently
   - Implement responsive navigation

## Contributing Guidelines

1. **Code Style**
   - Follow existing naming conventions
   - Use consistent formatting
   - Add comments for complex logic

2. **Component Creation**
   - Create reusable components
   - Document props and usage
   - Include error boundaries

3. **Testing**
   - Write unit tests for utilities
   - Test component rendering
   - Implement integration tests

## Environment Setup

1. **Development Environment**
   - Node.js
   - npm or yarn
   - Git

2. **Required Environment Variables**
   - API keys
   - Authentication tokens
   - Service endpoints
