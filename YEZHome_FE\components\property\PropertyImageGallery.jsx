"use client";

import { useState, useMemo } from "react";
import Image from "next/image";
import { Expand } from "lucide-react";
import { RowsPhotoAlbum } from "react-photo-album";
import Lightbox from "yet-another-react-lightbox";
import "yet-another-react-lightbox/styles.css";
import "react-photo-album/rows.css";

// Import lightbox plugins
import Fullscreen from "yet-another-react-lightbox/plugins/fullscreen";
import Slideshow from "yet-another-react-lightbox/plugins/slideshow";
import Zoom from "yet-another-react-lightbox/plugins/zoom";

// Import Dialog components
import { Dialog, DialogContent, DialogTitle } from "@/components/ui/dialog";
import { ScrollArea } from "../ui/scroll-area";

export default function PropertyImageGallery({ images = [], propertyName = ""}) {
  const [isGalleryOpen, setIsGalleryOpen] = useState(false);
  const [lightboxIndex, setLightboxIndex] = useState(-1);

  // Convert image URLs to the format required by react-photo-album
  const photos = useMemo(() => {
    return images.map((src) => ({
      src,
      width: 1200, // Default width for calculation
      height: 800, // Default height for calculation
      alt: propertyName || "Property image",
    }));
  }, [images, propertyName]);

  function renderNextImage({ alt = "", title, sizes }, { photo, width, height }) {
    return (
      <div
        style={{
          width: "100%",
          position: "relative",
          aspectRatio: `${width} / ${height}`,
        }}
      >
        <Image
          fill
          src={photo}
          alt={alt}
          title={title}
          sizes={sizes}
          placeholder={"blurDataURL" in photo ? "blur" : undefined}
        />
      </div>
    );
  }

  // Prepare slides for the lightbox
  const slides = useMemo(() => {
    return images.map((src) => ({
      src,
      alt: propertyName || "Property image",
    }));
  }, [images, propertyName]);

  if (!images || images.length === 0) {
    return (
      <div className="relative w-full h-[400px] bg-gray-200 rounded-lg">
        <div className="absolute inset-0 flex items-center justify-center text-gray-500">
          Không có ảnh
        </div>
      </div>
    );
  }

  // Show up to 4 images in the grid layout
  const displayImages = images.slice(0, Math.min(4, images.length));

  return (
    <>
      <div className="relative grid grid-cols-1 md:grid-cols-3 gap-2">
        {/* Main large image */}
        <div className="relative md:col-span-2 h-80 md:h-96 rounded-md overflow-hidden">          
          <div
            className="relative w-full h-full cursor-pointer"
            onClick={() => setIsGalleryOpen(true)}
          >
            <Image
              src={displayImages[0] || "/placeholder.svg?height=400&width=600"}
              alt={propertyName || "Property image"}
              fill
              className="object-cover"
              priority
            />
          </div>
        </div>

        {/* Thumbnail grid */}
        <div className="hidden md:grid grid-rows-4 gap-2 h-96">
          {displayImages.length > 1 ? (
            <>
              {displayImages.length > 1 && (
                <div
                  className="row-span-2 relative rounded-md overflow-hidden cursor-pointer"
                  onClick={() => setIsGalleryOpen(true)}
                >
                  <Image
                    src={displayImages[1] || "/placeholder.svg?height=200&width=300"}
                    alt={`${propertyName || "Property"} image 2`}
                    fill
                    className="object-cover hover:opacity-90 transition-opacity"
                  />
                </div>
              )}
              {displayImages.length > 2 && (
                <div
                  className="relative rounded-md overflow-hidden cursor-pointer"
                  onClick={() => setIsGalleryOpen(true)}
                >
                  <Image
                    src={displayImages[2] || "/placeholder.svg?height=100&width=300"}
                    alt={`${propertyName || "Property"} image 3`}
                    fill
                    className="object-cover hover:opacity-90 transition-opacity"
                  />
                </div>
              )}
              <div className="relative rounded-md overflow-hidden">
                {displayImages.length > 3 && (
                  <div
                    className="relative w-full h-full cursor-pointer"
                    onClick={() => setIsGalleryOpen(true)}
                  >
                    <Image
                      src={displayImages[3] || "/placeholder.svg?height=100&width=300"}
                      alt={`${propertyName || "Property"} image 4`}
                      fill
                      className="object-cover hover:opacity-90 transition-opacity"
                    />
                  </div>
                )}
                <button
                  onClick={() => setIsGalleryOpen(true)}
                  className="absolute bottom-2 right-2 bg-white bg-opacity-90 text-gray-800 p-2 rounded-md flex items-center gap-1 text-xs font-medium"
                >
                  <Expand className="h-4 w-4" />
                  Xem tất cả {images.length} ảnh
                </button>
              </div>
            </>
          ) : (
            <div className="row-span-4 relative rounded-md overflow-hidden bg-gray-100 flex items-center justify-center">
              <p className="text-gray-500 text-sm">Không có ảnh bổ sung</p>
            </div>
          )}
        </div>
      </div>

      {/* Gallery Modal */}
      <Dialog open={isGalleryOpen} onOpenChange={setIsGalleryOpen}>
        <DialogContent
          className="max-w-7xl p-6 h-[94vh] w-[90vw] bg-white"
        >
          <DialogTitle className="sr-only">
            {propertyName ? `${propertyName} Images` : "Property Images"}
          </DialogTitle>
          <div className="relative h-full w-full flex flex-col">
            <ScrollArea className="h-[90vh]">
              <RowsPhotoAlbum
                photos={photos}
                renderNextImage={renderNextImage}
                defaultContainerWidth={1200}
                sizes={{
                  size: "1168px",
                  sizes: [{ viewport: "(max-width: 1200px)", size: "calc(100vw - 32px)" }],
                }}
                onClick={({ index }) => setLightboxIndex(index)}
              />
            </ScrollArea>
          </div>
        </DialogContent>
      </Dialog>

      {/* Lightbox for individual image viewing */}
      <Lightbox
        slides={slides}
        open={lightboxIndex >= 0}
        index={lightboxIndex}
        close={() => setLightboxIndex(-1)}
        plugins={[Fullscreen, Slideshow, Zoom]}
      />
    </>
  );
}
