﻿using AutoMapper;
using RealEstate.Application.DTO;
using RealEstate.Application.Interfaces;
using RealEstate.Domain.Entities;
using RealEstate.Domain.Interfaces;
using RealEstate.Domain.CustomModel;

namespace RealEstate.Application.Services
{
    public class BlogService : IBlogService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly IMapper _mapper;

        public BlogService(IUnitOfWork unitOfWork, IMapper mapper)
        {
            _unitOfWork = unitOfWork;
            _mapper = mapper;
        }

        public async Task<BlogPostDto> GetBlogByIdAsync(Guid id)
        {
            var blogPost = await _unitOfWork.BlogPosts.GetByIdAsync(id);
            return _mapper.Map<BlogPostDto>(blogPost);
        }

        public async Task<BlogPostDto> GetBlogBySlugAsync(string slug)
        {
            var blogPost = await _unitOfWork.BlogPosts.GetBlogPostBySlugAsync(slug);
            return _mapper.Map<BlogPostDto>(blogPost);
        }

        public async Task<IEnumerable<BlogPostDto>> GetAllBlogAsync()
        {
            var blogPost = await _unitOfWork.BlogPosts.GetAllAsync();
            return _mapper.Map<IEnumerable<BlogPostDto>>(blogPost);
        }

        public async Task<PagedResult<BlogPostDto>> GetBlogAsync(PagingRequest paging, string? title)
        {
            var blogs = await _unitOfWork.BlogPosts.GetPagedAsync(
                paging,
                p => string.IsNullOrEmpty(title) || p.Title.ToLower().Contains(title.ToLower()) // Filtering
            );

            // Convert to DTO using AutoMapper
            var dtoList = _mapper.Map<IEnumerable<BlogPostDto>>(blogs.Items);

            return new PagedResult<BlogPostDto>
            {
                Items = dtoList,
                TotalCount = blogs.TotalCount
            };
        }

        public async Task<BlogPostDto> CreateBlogAsync(CreateBlogPostDto blogPostDto, Guid userId)
        {
            var blogPost = _mapper.Map<BlogPost>(blogPostDto);

            blogPost.CreatedBy = userId;
            blogPost.CreatedAt = DateTime.UtcNow;
            await _unitOfWork.BlogPosts.AddAsync(blogPost);
            await _unitOfWork.SaveChangesAsync();
            return _mapper.Map<BlogPostDto>(blogPost);
        }

        public async Task<bool> UpdateBlogAsync(Guid id, CreateBlogPostDto blogPostDto, Guid userId)
        {
            var blogPost = await _unitOfWork.BlogPosts.GetByIdAsync(id);
            if (blogPost == null) return false;
            _mapper.Map(blogPostDto, blogPost);

            blogPost.UpdatedBy = userId;
            _unitOfWork.BlogPosts.Update(blogPost);
            await _unitOfWork.SaveChangesAsync();
            return true;
        }

        public async Task<bool> DeleteBlogAsync(Guid id, Guid userId)
        {
            var blogPost = await _unitOfWork.BlogPosts.GetByIdAsync(id);
            if (blogPost == null) return false;

            blogPost.UpdatedBy = userId;
            _unitOfWork.BlogPosts.Remove(blogPost);
            await _unitOfWork.SaveChangesAsync();
            return true;
        }
    }
}
