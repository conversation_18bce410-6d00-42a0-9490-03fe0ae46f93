"use client";
import { useState, useEffect, useCallback } from "react";
import { MemberRank } from "@/lib/enum";
import { cn } from "@/lib/utils";
import { useAuth } from "@/contexts/AuthContext";
import { Button } from "../ui/button";
import { RefreshCw } from "lucide-react";
import { useTranslations } from "next-intl";
import { getMemberRankTranslationKey } from "@/lib/memberRankUtils";
import { useToast } from "@/hooks/use-toast";

// Use translation keys for rank names from Common namespace
const getRankDetails = (tCommon) => ({
  [MemberRank.DIAMOND]: {
    icon: "💎",
    label: tCommon(getMemberRankTranslationKey(MemberRank.DIAMOND)),
    color: "text-blue-600",
  },
  [MemberRank.PLATINUM]: {
    icon: "🏅",
    label: tCommon(getMemberRankTranslationKey(MemberRank.PLATINUM)),
    color: "text-slate-600",
  },
  [MemberRank.GOLD]: {
    icon: "🥇",
    label: tCommon(getMemberRankTranslationKey(MemberRank.GOLD)),
    color: "text-yellow-600",
  },
  [MemberRank.SILVER]: {
    icon: "🥈",
    label: tCommon(getMemberRankTranslationKey(MemberRank.SILVER)),
    color: "text-gray-600",
  },
  [MemberRank.BRONZE]: {
    icon: "🥉",
    label: tCommon(getMemberRankTranslationKey(MemberRank.BRONZE)),
    color: "text-amber-700",
  },
  [MemberRank.DEFAULT]: {
    icon: "🌟",
    label: tCommon(getMemberRankTranslationKey(MemberRank.DEFAULT)),
    color: "text-gray-500",
  },
});

export default function BadgeUserRank({
  className,
  showRefreshButton = false,
  onRankChange = null,
  onRefreshRef = null,
  externalRank = null, // Optional prop to override the rank from context
}) {
  const tCommon = useTranslations("Common");
  const t = useTranslations("PropertyForm");
  const { toast } = useToast();
  const { profile, refreshProfile } = useAuth();
  const [initialRank, setInitialRank] = useState(null);
  const [isRefreshing, setIsRefreshing] = useState(false);

  // Use external rank if provided, otherwise use memberRank from profile
  const userRank = externalRank !== null ? externalRank : profile?.user?.memberRank;

  // Function to manually refresh user rank data
  const refreshRankData = useCallback(async () => {
    if (isRefreshing) return; // Prevent multiple simultaneous refreshes

    setIsRefreshing(true);
    try {
      await refreshProfile();
      toast({
        title: t("rankUpdated"),
        description: t("rankRefreshed"),
        className: "bg-teal-600 text-white",
      });
    } catch (error) {
      console.error("Error refreshing rank data:", error);
      toast({
        variant: "destructive",
        title: t("error") || "Error",
        description: t("errorRefreshingRank") || "Error refreshing rank data",
      });
    } finally {
      setIsRefreshing(false);
    }
  }, [refreshProfile, toast, t, isRefreshing]);

  // Expose the refresh function via ref if provided
  useEffect(() => {
    if (onRefreshRef) {
      onRefreshRef.current = refreshRankData;
    }
  }, [onRefreshRef, refreshRankData]);

  // Handle rank changes
  useEffect(() => {
    if (userRank && onRankChange) {
      // Set initial rank on first load
      if (initialRank === null) {
        setInitialRank(userRank);
      }

      // Check if rank has changed since component mounted
      if (initialRank !== null && initialRank !== userRank) {
        onRankChange({
          previousRank: initialRank,
          currentRank: userRank,
        });
      }
    }
  }, [userRank, initialRank, onRankChange]);

  // Get badge details based on rank with translations
  const rankMeta = getRankDetails(tCommon)[userRank] || getRankDetails(tCommon)[MemberRank.DEFAULT];

  return (
    <div className="flex items-center gap-2">
      {userRank && (
        <div className={cn("flex items-center text-sm font-semibold", rankMeta.color)}>
          <span className="mr-1">{rankMeta.icon}</span>
          <span>{rankMeta.label}</span>
        </div>
      )}

      {!userRank && (
        <span className="text-sm font-semibold text-gray-500">...loading</span> 
      )}

      {showRefreshButton && (
        <Button
          type="button"
          variant="outline"
          size="sm"
          onClick={(e) => {
            e.stopPropagation();
            e.preventDefault();
            refreshRankData();
          }}
          disabled={isRefreshing}
          className="flex items-center gap-1 text-xs h-6 px-2"
        >
          {isRefreshing ? (
            <>
              <RefreshCw className="h-3 w-3 animate-spin" />
              {t("refreshing")}
            </>
          ) : (
            <>
              <RefreshCw className="h-3 w-3" />
              {t("refreshRank")}
            </>
          )}
        </Button>
      )}
    </div>
  );
}
