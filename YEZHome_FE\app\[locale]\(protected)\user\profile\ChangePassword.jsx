"use client";
import { useEffect, useState } from "react";
import { Circle<PERSON>lert, Eye, EyeOff } from "lucide-react";
import ButtonLoading from "@/components/ui/ButtonLoading";
import { useActionState } from "react";
import { changePassword, logout } from "@/app/actions/server/authenticate";
import { useAlert } from "@/contexts/AlertContext";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { useTranslations } from 'next-intl';

const initialState = {
  message: null,
  success: false,
  errors: {},
  errorType: null,
};

export default function ChangePassword() {
  const t = useTranslations('UserProfilePage');
  const [showOldPassword, setShowOldPassword] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [state, formAction, isPending] = useActionState(changePassword, initialState);
  const { showAlert } = useAlert();

  useEffect(() => {
    if (state.success) {
      showAlert({
        title: t('passwordSuccessTitle'),
        message: t('passwordSuccessMessage'),
        hasCancel: false,
        onConfirm: async () => {
          await logout();
        },
      });
    } else if (state.message && (state.errorType === "token_expired" || state.errorType === "unauthorized" || state.errorType === "no_token")) {
      showAlert(state);
    }
  }, [state, showAlert, t]);

  return (
    <section className="border-t border-gray-900/10 pt-12">
      <h3 className="text-base/7 font-semibold text-gray-900">{t('passwordSectionTitle')}</h3>
      <p className="mt-1 text-sm leading-6 text-gray-600">
        {t('passwordSectionDescription')}
      </p>

      <form action={formAction} className="mt-10 grid grid-cols-1 gap-x-6 gap-y-8 sm:grid-cols-6">
        <div className="sm:col-span-4">
          <div className="space-y-4">
            {state.message && !state.success && state.errorType !== "token_expired" && state.errorType !== "unauthorized" && state.errorType !== "no_token" && (
              <Alert variant="destructive">
                <CircleAlert className="h-4 w-4" />
                <AlertTitle>{t('passwordErrorTitle')}</AlertTitle>
                <AlertDescription>{state.message}</AlertDescription>
              </Alert>
            )}
            <div>
              <label htmlFor="oldPassword" className="block text-sm font-medium text-gray-700">
                {t('oldPasswordLabel')}
              </label>
              <div className="relative mt-1">
                <input
                  id="oldPassword"
                  name="oldPassword"
                  type={showOldPassword ? "text" : "password"}
                  required
                  className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-teal-500 focus:border-teal-500 sm:text-sm"
                />
                <button
                  type="button"
                  className="absolute inset-y-0 right-0 pr-3 flex items-center"
                  onClick={() => setShowOldPassword(!showOldPassword)}
                  aria-label={showOldPassword ? "Hide password" : "Show password"}
                >
                  {showOldPassword ? (
                    <EyeOff className="h-5 w-5 text-gray-400" />
                  ) : (
                    <Eye className="h-5 w-5 text-gray-400" />
                  )}
                </button>
              </div>
              {state.errors?.oldPassword && (
                <p className="mt-1 text-xs text-red-500">{state.errors.oldPassword[0]}</p>
              )}
            </div>

            <div>
              <label htmlFor="newPassword" className="block text-sm font-medium text-gray-700">
                {t('newPasswordLabel')}
              </label>
              <div className="relative mt-1">
                <input
                  id="newPassword"
                  name="newPassword"
                  type={showNewPassword ? "text" : "password"}
                  required
                  minLength={6}
                  className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-teal-500 focus:border-teal-500 sm:text-sm"
                />
                <button
                  type="button"
                  className="absolute inset-y-0 right-0 pr-3 flex items-center"
                  onClick={() => setShowNewPassword(!showNewPassword)}
                  aria-label={showNewPassword ? "Hide password" : "Show password"}
                >
                  {showNewPassword ? (
                    <EyeOff className="h-5 w-5 text-gray-400" />
                  ) : (
                    <Eye className="h-5 w-5 text-gray-400" />
                  )}
                </button>
              </div>
              {state.errors?.newPassword && (
                <p className="mt-1 text-xs text-red-500">{state.errors.newPassword[0]}</p>
              )}
            </div>

            <div>
              <label htmlFor="confirmPassword" className="block text-sm font-medium text-gray-700">
                {t('confirmNewPasswordLabel')}
              </label>
              <div className="relative mt-1">
                <input
                  id="confirmPassword"
                  name="confirmPassword"
                  type={showConfirmPassword ? "text" : "password"}
                  required
                  minLength={6}
                  className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-teal-500 focus:border-teal-500 sm:text-sm"
                />
                <button
                  type="button"
                  className="absolute inset-y-0 right-0 pr-3 flex items-center"
                  onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                  aria-label={showConfirmPassword ? "Hide password" : "Show password"}
                >
                  {showConfirmPassword ? (
                    <EyeOff className="h-5 w-5 text-gray-400" />
                  ) : (
                    <Eye className="h-5 w-5 text-gray-400" />
                  )}
                </button>
              </div>
              {state.errors?.confirmPassword && (
                <p className="mt-1 text-xs text-red-500">{state.errors.confirmPassword[0]}</p>
              )}
            </div>
          </div>

          <div className="mt-6">
            <ButtonLoading type="submit" showLoading={isPending} title={t('saveButton')} />
          </div>
        </div>
      </form>
    </section>
  );
}
