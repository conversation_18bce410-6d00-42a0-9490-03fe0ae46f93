"use client";
import { <PERSON> } from "@/i18n/navigation";
import { cn } from "@/lib/utils";
import { sidebarMenuItems } from "@/common/NavBarData";
import { usePathname } from "next/navigation";
import { useTranslations, useLocale } from "next-intl";
import { useAuth } from "@/contexts/AuthContext";
import { formatCurrency } from "@/lib/utils";
import { Copy, Wallet } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { Sidebar, SidebarContent, SidebarMenu, SidebarMenuItem, SidebarMenuButton, SidebarRail } from "@/components/ui/sidebar";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import BadgeUserRank from "./BadgeUserRank";
import { Avatar, AvatarFallback, AvatarImage } from "../ui/avatar";

export default function LeftSideBar({ ...props }) {
  const pathname = usePathname();
  const t = useTranslations("UserSidebar");
  const locale = useLocale();
  const { toast } = useToast();
  const { profile } = useAuth();

  const walletInfo = profile?.user?.wallet;

  const getTranslationKey = (href) => {
    if (href.includes("/profile")) return "profile";
    if (href.includes("/payments")) return "payments";
    if (href.includes("/notifications")) return "notifications";
    if (href.includes("/bds")) return "properties";
    if (href.includes("/dashboard")) return "dashboard";
    if (href.includes("/setting")) return "setting";
    return "";
  };

  const handleCopyToClipboard = (text) => {
    navigator.clipboard.writeText(text);
    toast({
      title: "Sao chép thành công",
      description: "Đã sao chép mã chuyển khoản vào clipboard",
    });
  };

  return (
    <Sidebar className="border-none" {...props}>
      <SidebarContent className="bg-white pt-4">
        <Card className="mx-2 mb-4 bg-slate-50 rounded-lg shadow-sm">
          <CardContent className="p-4">
            {/* Rank */}
            <div className="flex flex-row items-center text-center mb-4 gap-2">
              <Avatar className="h-8 w-8">
                <AvatarImage src={profile?.user?.avatarURL} alt={profile?.user?.fullName} />
                <AvatarFallback>{profile?.fullName?.substring(0, 2) || "U"}</AvatarFallback>
              </Avatar>
              <div className="flex flex-col">
                <p className="font-medium text-gray-800 text-sm mb-1">{profile?.user?.fullName || "Người dùng"}</p>
                <BadgeUserRank />
              </div>
            </div>

            {/* Wallet Info */}
            <div className="text-sm text-gray-600 mb-2 flex items-center justify-between gap-2">
              <div className="flex items-center gap-2">
                <Wallet className="w-6 h-6 text-teal-600" />
                <span className="text-xl font-semibold text-gray-800">{walletInfo ? formatCurrency(walletInfo.balance) : "0 ₫"}</span>
              </div>
              {/* Top-up */}
              <Button className="bg-teal-600 hover:bg-teal-700 text-white font-semibold text-xs" size="sm" asChild>
                <Link href="/user/wallet">{t("topUpWallet")}</Link>
              </Button>
            </div>

            {/* Transfer Code */}
            {profile?.user?.transferCode && (
              <div className="flex items-center justify-between p-2 bg-white border rounded text-xs mb-3">
                <span className="text-gray-600">
                  {t("transferCode")} <span className="text-sm font-semibold ml-1">{profile?.user.transferCode}</span>
                </span>
                <Button
                  variant="ghost"
                  size="icon"
                  className="text-teal-600 hover:text-teal-800"
                  onClick={() => handleCopyToClipboard(profile?.user.transferCode)}
                >
                  <Copy className="w-4 h-4" />
                </Button>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Navigation Menu */}
        <div className="p-2">
          <SidebarMenu>
            {sidebarMenuItems.map((item) => {
              const href = `/${locale}${item.href}`;
              const isActive = pathname === item.href;

              return (
                <SidebarMenuItem key={item.href}>
                  <SidebarMenuButton
                    isActive={isActive}
                    tooltip={t(getTranslationKey(item.href)) || item.label}
                    className={cn(
                      "flex items-center gap-3 px-3 py-2 text-sm font-semibold transition-all rounded-none",
                      pathname === href ? "border-l-4 border-teal-600 text-teal-600" : "hover:bg-gray-100 text-gray-700"
                    )}
                    asChild
                  >
                    <Link href={item.href}>
                      <item.icon size={20} />
                      {t(getTranslationKey(item.href)) || item.label}
                    </Link>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              );
            })}
          </SidebarMenu>
        </div>
      </SidebarContent>
      <SidebarRail />
    </Sidebar>
  );
}
