import { <PERSON><PERSON><PERSON>, <PERSON><PERSON> } from 'next/font/google';
import "../globals.css";
import Footer from "@/components/layout/Footer";
import Navbar from "@/components/layout/Navbar";
import AlertPopup from "@/components/layout/AlertPopup";

import { AlertProvider } from "@/contexts/AlertContext";
import { Toaster } from "@/components/ui/toaster";
import { NextIntlClientProvider, hasLocale } from "next-intl";
import { notFound } from "next/navigation";
import { routing } from "@/i18n/routing";
import { cookies } from "next/headers";
import { getMessages } from "next-intl/server";
import { AuthProvider } from "@/contexts/AuthContext";
import { validateTokenServer } from "../actions/server/authenticate";

const montserrat = Montserrat({
  subsets: ['vietnamese', 'latin'],
  weight: ['400', '500', '600', '700', '800'], // Regular, Medium, SemiBold, Bold, ExtraBold
  variable: '--font-montserrat', // Biến CSS cho Tailwind/CSS Modules
});

const roboto = Roboto({
  subsets: ['vietnamese', 'latin'],
  weight: ['400', '500', '700'], // Regular, Medium, Bold
  variable: '--font-roboto', // Biến CSS cho Tailwind/CSS Modules
});

export const metadata = {
  title: "YEZHOME",
  description: "YEZHOME: Kết nối nhanh chóng, giao dịch tin cậy.",
};

export default async function RootLayout({ children, params }) {
  // Ensure that the incoming `locale` is valid
  const { locale } = await params;
  if (!hasLocale(routing.locales, locale)) {
    notFound();
  }

  const messages = await getMessages();

  // Check if user is logged in
  const initialAuthState = await validateTokenServer();
  const isLoggedIn = initialAuthState.isLoggedIn;

  let userData = null;
  if (isLoggedIn) {
    const userCookie = (await cookies()).get("UserProfile");
    if (userCookie) {
      try {
        userData = JSON.parse(userCookie.value);
      } catch (e) {
        console.error("Failed to parse user data from cookie:", e);
      }
    }
  }

  return (
    <html lang={locale}>
      <body className={`${montserrat.variable} ${roboto.variable} antialiased`} suppressHydrationWarning>
        <NextIntlClientProvider locale={locale} messages={messages}>
          <Toaster />
          <AlertProvider>
            <AuthProvider initialAuthState={initialAuthState} initialUserData={userData}>
              <div className="min-h-screen [--header-height:calc(theme(spacing.14))]">
                <Navbar isLoggedIn={isLoggedIn}></Navbar>
                <div className="relative">{children}</div>
              </div>
              <Footer></Footer>
              <AlertPopup></AlertPopup>
            </AuthProvider>
          </AlertProvider>
        </NextIntlClientProvider>
      </body>
    </html>
  );
}
