export async function GET() {
  try {
    const API_URL = process.env.API_URL;
    
    const response = await fetch(`${API_URL}/api/Address/cities`, {
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch cities: ${response.status}`);
    }

    const data = await response.json();
    return Response.json(data);
  } catch (error) {
    console.error('Error fetching cities:', error);
    return Response.json(
      { error: 'Failed to fetch cities' },
      { status: 500 }
    );
  }
}
