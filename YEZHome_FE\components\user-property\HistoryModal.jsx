import { useEffect, useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Di<PERSON>Footer,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { getPropertyStatusHistory } from "@/app/actions/server/property";
import { useToast } from "@/hooks/use-toast";
import { format } from "date-fns";
import { Card } from "@/components/ui/card";

export default function HistoryModal({ propertyId, open, onClose }) {
  const { toast } = useToast();
  const [history, setHistory] = useState([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (open && propertyId) {
      loadHistory();
    }
  }, [open, propertyId]);

  const loadHistory = async () => {
    setLoading(true);
    try {
      const response = await getPropertyStatusHistory(propertyId);
      if (response.success) {
        setHistory(response.data);
      } else {
        toast({
          title: "Lỗi",
          description: response.message || "Không thể tải lịch sử hoạt động",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error("Error loading history:", error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-w-[600px]" onPointerDownOutside={(e) => e.preventDefault()}>
        <DialogHeader>
          <DialogTitle>Lịch sử hoạt động</DialogTitle>
        </DialogHeader>

        <ScrollArea className="flex-1 w-full rounded-md border max-h-[400px]">
          {loading ? (
            <div className="text-center text-gray-500 py-8">
              Đang tải...
            </div>
          ) : (
            <div className="p-4 space-y-4">
              {history.length > 0 ? (
                history.map((item, index) => (
                  <Card key={index} className="p-4 relative">
                    <div className="flex items-start gap-4">
                      <div className="min-w-[120px] text-sm text-gray-500">
                        {format(new Date(item.changedAt), "dd/MM/yyyy HH:mm")}
                      </div>
                      <div className="flex-1">
                        <div className="font-medium">{item.status}</div>
                        {item.comment && (
                          <p className="text-sm text-gray-600 mt-1">{item.comment}</p>
                        )}
                      </div>
                    </div>
                  </Card>
                ))
              ) : (
                <div className="text-center text-gray-500 py-8">
                  Chưa có lịch sử hoạt động nào
                </div>
              )}
            </div>
          )}
        </ScrollArea>

        <DialogFooter>
          <Button variant="outline" onClick={onClose}>
            Đóng
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
} 