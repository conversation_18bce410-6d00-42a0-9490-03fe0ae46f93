"use client";

import { useEffect, useState } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { checkPaymentStatus } from "@/app/actions/server/user";
import { formatCurrency } from "@/lib/utils";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { AlertCircle, CheckCircle, XCircle, Loader2 } from "lucide-react";
import {Link} from '@/i18n/navigation';;
import { useTranslations } from 'next-intl';

export default function PaymentResultPage() {
  const t = useTranslations('UserWalletPage');
  const [status, setStatus] = useState("loading"); // loading, success, pending, failed
  const [paymentData, setPaymentData] = useState(null);
  const router = useRouter();
  const searchParams = useSearchParams();
  
  const orderId = searchParams.get("orderId");
  const resultCode = searchParams.get("resultCode") || searchParams.get("status");
  
  useEffect(() => {
    async function verifyPayment() {
      if (!orderId) {
        setStatus("failed");
        return;
      }
      
      try {
        const result = await checkPaymentStatus(orderId);
        
        if (result.success) {
          setPaymentData(result.data);
          
          if (result.data.status === "completed") {
            setStatus("success");
          } else if (result.data.status === "pending") {
            setStatus("pending");
          } else {
            setStatus("failed");
          }
        } else {
          setStatus("failed");
        }
      } catch (error) {
        console.error("Payment verification error:", error);
        setStatus("failed");
      }
    }
    
    verifyPayment();
    
    // After 10 seconds, redirect to wallet page if still loading
    const timeout = setTimeout(() => {
      if (status === "loading") {
        router.push("/user/wallet");
      }
    }, 10000);
    
    return () => clearTimeout(timeout);
  }, [orderId, resultCode, router]);
  
  return (
    <div className="p-6 max-w-lg mx-auto">
      <Card className="shadow-lg">
        <CardContent className="pt-6">
          <div className="flex flex-col items-center justify-center space-y-4 p-6 text-center">
            {status === "loading" && (
              <>
                <Loader2 className="h-16 w-16 text-primary animate-spin" />
                <h2 className="text-2xl font-semibold">{t('resultLoadingTitle')}</h2>
                <p className="text-gray-500">
                  {t('resultLoadingMessage')}
                </p>
              </>
            )}
            
            {status === "success" && (
              <>
                <CheckCircle className="h-16 w-16 text-green-500" />
                <h2 className="text-2xl font-semibold text-green-600">{t('resultSuccessTitle')}</h2>
                <p className="text-gray-600">
                  {t('resultSuccessMessage')}
                </p>
                
                <div className="w-full bg-gray-50 p-4 rounded-md mt-4 text-left space-y-2">
                  <div className="flex justify-between">
                    <span className="text-gray-500">{t('resultTransactionIdLabel')}</span>
                    <span className="font-medium">{paymentData?.orderId}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-500">{t('resultAmountLabel')}</span>
                    <span className="font-medium">{formatCurrency(paymentData?.amount || 0)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-500">{t('resultTimeLabel')}</span>
                    <span className="font-medium">
                      {paymentData?.updatedAt 
                        ? new Date(paymentData.updatedAt).toLocaleString(router.locale || "vi-VN") 
                        : new Date().toLocaleString(router.locale || "vi-VN")}
                    </span>
                  </div>
                </div>
              </>
            )}
            
            {status === "pending" && (
              <>
                <AlertCircle className="h-16 w-16 text-amber-500" />
                <h2 className="text-2xl font-semibold text-amber-600">{t('resultPendingTitle')}</h2>
                <p className="text-gray-600">
                  {t('resultPendingMessage')}
                </p>
                
                <div className="w-full bg-gray-50 p-4 rounded-md mt-4 text-left space-y-2">
                  <div className="flex justify-between">
                    <span className="text-gray-500">{t('resultTransactionIdLabel')}</span>
                    <span className="font-medium">{paymentData?.orderId}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-500">{t('resultAmountLabel')}</span>
                    <span className="font-medium">{formatCurrency(paymentData?.amount || 0)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-500">{t('resultTimeLabel')}</span>
                    <span className="font-medium">
                      {paymentData?.createdAt 
                        ? new Date(paymentData.createdAt).toLocaleString(router.locale || "vi-VN") 
                        : new Date().toLocaleString(router.locale || "vi-VN")}
                    </span>
                  </div>
                </div>
              </>
            )}
            
            {status === "failed" && (
              <>
                <XCircle className="h-16 w-16 text-red-500" />
                <h2 className="text-2xl font-semibold text-red-600">{t('resultFailedTitle')}</h2>
                <p className="text-gray-600">
                  {t('resultFailedMessage')}
                </p>
              </>
            )}
            
            <div className="flex flex-col sm:flex-row gap-3 mt-6 w-full">
              <Button className="flex-1" asChild>
                <Link href="/user/wallet">
                  {status === "success" ? t('resultActionSuccess') : t('resultActionRetry')}
                </Link>
              </Button>
              
              {status !== "success" && (
                <Button variant="outline" className="flex-1" asChild>
                  <Link href="/user/transactions">
                    {t('resultActionHistory')}
                  </Link>
                </Button>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
} 