---
description: 
globs: 
alwaysApply: false
---
 Frontend Architecture Rules

## Component Structure
- Use functional components with hooks
- Implement proper prop validation
- Use React.memo() for performance optimization
- Follow single responsibility principle
- Separate UI components from business logic

## File Organization
- Group related components in folders
- Use index files for clean imports
- Separate pages from components
- Organize by feature rather than type
- Use consistent file naming (PascalCase for components)

## Example Structure:
```
components/
├── layout/
│   ├── Navbar.jsx
│   └── Footer.jsx
├── property/
│   ├── PropertyCard.jsx
│   └── PropertyList.jsx
└── ui/
    ├── Button.jsx
    └── Input.jsx
```

# State Management Rules

## Context Usage
- Use React Context for global state
- Implement proper context providers
- Avoid context overuse for local state
- Use useMemo for expensive computations
- Implement proper cleanup in useEffect

## Local State
- Use useState for component-level state
- Implement controlled components
- Use useReducer for complex state logic
- Avoid prop drilling beyond 2-3 levels

## Server State
- Use server actions for data fetching
- Implement proper loading states
- Handle errors gracefully
- Use optimistic updates where appropriate

# API Communication Rules

## Authentication
- Use fetchWithAuth for authenticated requests
- Use fetchWithoutAuth for unauthenticated requests
- Implement proper token management
- Handle token expiration gracefully
- Use secure cookie storage
- Implement automatic logout on auth failure

## Error Handling
- Implement consistent error handling
- Show user-friendly error messages
- Log errors for debugging
- Implement retry mechanisms
- Handle network failures gracefully

## Example:
```javascript
// Use sessionUtils.js for API calls
const response = await fetchWithAuth('/api/properties', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify(data)
});

if (!response.success) {
  showAlert(response);
  return;
}
```

# Form Rules

## Form Libraries
- Use react-hook-form for form management
- Implement Zod for schema validation
- Use controlled components
- Implement proper form state management

## Validation
- Validate on both client and server
- Show real-time validation feedback
- Implement proper error messages
- Use consistent validation patterns

## Example:
```javascript
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';

const schema = z.object({
  email: z.string().email('Invalid email'),
  password: z.string().min(6, 'Password too short')
});

const { register, handleSubmit, formState: { errors } } = useForm({
  resolver: zodResolver(schema)
});
```

# Performance Rules

## Code Splitting
- Use dynamic imports for large components
- Implement proper loading states
- Use Next.js built-in optimizations
- Optimize bundle size

## Image Optimization
- Use Next.js Image component
- Implement proper image sizing
- Use WebP format when possible
- Implement lazy loading

## Caching
- Implement proper caching strategies
- Use SWR or React Query for data caching
- Cache static assets
- Implement service worker for offline support

# Accessibility Rules

## Semantic HTML
- Use proper HTML elements
- Implement ARIA labels
- Ensure keyboard navigation
- Maintain proper heading hierarchy

## Responsive Design
- Mobile-first approach
- Use Tailwind breakpoints consistently
- Test on multiple devices
- Implement touch-friendly interactions

## Internationalization
- Use next-intl for translations
- Implement proper locale handling
- Support RTL languages if needed

- Use proper date/number formatting