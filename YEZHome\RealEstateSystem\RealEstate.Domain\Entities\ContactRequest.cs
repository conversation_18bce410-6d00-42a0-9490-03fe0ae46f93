using RealEstate.Domain.Interfaces;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace RealEstate.Domain.Entities
{
    public class ContactRequest : BaseEntityWithAuditable
    {
        [Required]
        [MaxLength(255)]
        public string? Name { get; set; }

        [Required]
        [EmailAddress]
        [MaxLength(255)]
        public string? Email { get; set; }

        [Required]
        [MaxLength(20)]
        public string? Phone { get; set; }

        public DateTime SentAt { get; set; } = DateTime.UtcNow;

        [Required]
        public Guid PropertyId { get; set; }
        [ForeignKey("PropertyId")]
        public Property? Property { get; set; }

        [Required]
        public Guid PropertyOwnerId { get; set; }
        [ForeignKey("PropertyOwnerId")]
        public AppUser? Agent { get; set; }

        public Guid? UserId { get; set; }
        [ForeignKey("UserId")]
        public AppUser? User { get; set; }

        public string? Note { get; set; }

        [Required]
        [MaxLength(20)]
        public string Status { get; set; } = "Pending"; // Possible values: Pending, Contacted, Rejected, Completed

    }
} 