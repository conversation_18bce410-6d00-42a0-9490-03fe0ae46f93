using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using RealEstate.Application.DTO;
using RealEstate.Application.Interfaces;

namespace RealEstate.InternalAPI.Controllers
{
    public class WalletTransactionController : BaseController
    {
        private readonly IWalletService _walletService;
        private readonly ILogger<WalletTransactionController> _logger;

        public WalletTransactionController(
            IWalletService walletService,
            ILogger<WalletTransactionController> logger)
        {
            _walletService = walletService;
            _logger = logger;
        }

        [HttpGet("admin/user/{userId}")]
        public async Task<ActionResult<IEnumerable<WalletTransactionDto>>> GetUserTransactions(
            Guid userId,
            [FromQuery] int page = 1,
            [FromQuery] int pageSize = 20)
        {
            try
            {
                var transactions = await _walletService.GetWalletTransactionsAsync(userId, page, pageSize);
                return Ok(transactions);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving user transactions");
                return StatusCode(500, $"An error occurred: {ex.Message}");
            }
        }

        [HttpGet("admin/pending")]
        public async Task<ActionResult<IEnumerable<WalletTransactionDto>>> GetAllPendingTransactions(
            [FromQuery] int page = 1,
            [FromQuery] int pageSize = 20)
        {
            try
            {
                var transactions = await _walletService.GetPendingTransactionsAsync(page, pageSize);
                return Ok(transactions);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving all pending transactions");
                return StatusCode(500, $"An error occurred: {ex.Message}");
            }
        }

        [HttpGet("admin/search")]
        public async Task<ActionResult<TransactionSearchResultDto>> AdminSearchTransactions(
            [FromQuery] Guid? userId = null,
            [FromQuery] DateTime? startDate = null,
            [FromQuery] DateTime? endDate = null,
            [FromQuery] string? status = null,
            [FromQuery] string? paymentMethod = null,
            [FromQuery] string? type = null,
            [FromQuery] decimal? minAmount = null,
            [FromQuery] decimal? maxAmount = null,
            [FromQuery] string? searchTerm = null,
            [FromQuery] int page = 1,
            [FromQuery] int pageSize = 20)
        {
            try
            {
                var criteria = new TransactionSearchCriteriaDto
                {
                    StartDate = startDate,
                    EndDate = endDate,
                    Status = status,
                    PaymentMethod = paymentMethod,
                    Type = type,
                    MinAmount = minAmount,
                    MaxAmount = maxAmount,
                    SearchTerm = searchTerm,
                    Page = page,
                    PageSize = pageSize
                };

                var result = await _walletService.SearchTransactionsAsync(criteria, userId);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error searching transactions");
                return StatusCode(500, $"An error occurred: {ex.Message}");
            }
        }

        [HttpGet("admin/export")]
        [Authorize(Policy = "AdminOnly")]
        public async Task<IActionResult> AdminExportTransactions(
            [FromQuery] Guid? userId = null,
            [FromQuery] DateTime? startDate = null,
            [FromQuery] DateTime? endDate = null,
            [FromQuery] string? status = null,
            [FromQuery] string? paymentMethod = null,
            [FromQuery] string? type = null,
            [FromQuery] decimal? minAmount = null,
            [FromQuery] decimal? maxAmount = null,
            [FromQuery] string? searchTerm = null)
        {
            try
            {
                var criteria = new TransactionSearchCriteriaDto
                {
                    StartDate = startDate,
                    EndDate = endDate,
                    Status = status,
                    PaymentMethod = paymentMethod,
                    Type = type,
                    MinAmount = minAmount,
                    MaxAmount = maxAmount,
                    SearchTerm = searchTerm
                };

                var excelData = await _walletService.ExportTransactionsToExcelAsync(criteria, userId);

                // Generate a filename with current date
                string fileName = $"Transactions_{DateTime.Now:yyyyMMdd_HHmmss}.xlsx";

                return File(excelData, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", fileName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error exporting transactions");
                return StatusCode(500, $"An error occurred: {ex.Message}");
            }
        }

        [HttpPost("{id}/process")]
        [Authorize(Policy = "AdminOnly")]
        public async Task<ActionResult<WalletTransactionDto>> ProcessTransaction(
            Guid id,
            [FromBody] ProcessTransactionDto request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                if (id != request.TransactionId)
                {
                    return BadRequest("Transaction ID in URL must match the one in the request body");
                }

                var adminId = GetUserId();
                if (adminId == null)
                {
                    return Unauthorized("Invalid admin user");
                }

                var transaction = await _walletService.ProcessTransactionAsync(id, request, adminId.Value);
                return Ok(transaction);
            }
            catch (ArgumentException ex)
            {
                return BadRequest(ex.Message);
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(ex.Message);
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing transaction");
                return StatusCode(500, $"An error occurred: {ex.Message}");
            }
        }
    }
}
