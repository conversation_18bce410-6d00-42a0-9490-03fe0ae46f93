import { formatCurrency, formatDate } from "@/lib/utils";
import { PiggyBank, ArrowDown, ArrowUp, AlertCircle } from "lucide-react";
import { Button } from "@/components/ui/button";
import {Link} from '@/i18n/navigation';;

export default function RecentTransactions({ transactions = [], limit = 5 }) {
  // Check if transactions array exists and has items
  const hasTransactions = transactions && transactions.length > 0;
  const displayedTransactions = hasTransactions ? transactions.slice(0, limit) : [];

  return (
    <div className="space-y-4">
      {!hasTransactions ? (
        <div className="flex flex-col items-center justify-center p-6 bg-gray-50 rounded-md text-gray-500">
          <AlertCircle className="h-12 w-12 text-gray-400 mb-2" />
          <p className="text-center">Chưa có giao dịch nào</p>
          <p className="text-sm text-center mt-1"><PERSON><PERSON><PERSON> giao dịch của bạn sẽ hiển thị ở đây</p>
        </div>
      ) : (
        <>
          <div className="space-y-3">
            {displayedTransactions.map(transaction => (
              <div key={transaction.id} className="flex items-center gap-4 p-3 bg-gray-50 rounded-md">
                <div className={`p-2 rounded-full ${transaction.type === 'deposit' ? 'bg-green-100' : 'bg-coral-100'}`}>
                  {transaction.type === 'deposit' ? (
                    <ArrowDown className={`h-5 w-5 ${transaction.type === 'deposit' ? 'text-green-600' : 'text-coral-600'}`} />
                  ) : (
                    <ArrowUp className={`h-5 w-5 ${transaction.type === 'deposit' ? 'text-green-600' : 'text-coral-600'}`} />
                  )}
                </div>
                <div className="flex-1">
                  <div className="font-medium">{transaction.description}</div>
                  <div className="text-xs text-gray-500">{formatDate(transaction.createdAt)}</div>
                </div>
                <div className={`font-medium ${transaction.type === 'deposit' ? 'text-green-600' : 'text-coral-600'}`}>
                  {transaction.type === 'deposit' ? '+' : '-'} {formatCurrency(transaction.amount)}
                </div>
              </div>
            ))}
          </div>

          {transactions.length > limit && (
            <div className="text-center pt-3">
              <Button variant="outline" size="sm" asChild>
                <Link href="/user/transactions">
                  Xem tất cả giao dịch
                </Link>
              </Button>
            </div>
          )}
        </>
      )}
    </div>
  );
} 