"use server";

import { handleErrorResponse, logError } from "@/lib/apiUtils";
import { fetchWithAuth } from "@/lib/sessionUtils";

const API_BASE_URL = `${process.env.API_URL}/api/notifications`;

/**
 * Get notifications with pagination and optional type filtering
 * @param {Object} params - Query parameters
 * @param {number} params.page - Page number (default: 1)
 * @param {number} params.limit - Items per page (default: 10)
 * @param {string} params.type - Notification type (news, wallet_update, promotion, customer_message)
 * @returns {Promise<Object>} Response with notifications data
 */
export async function getNotifications(params = {}) {
  try {
    const { page = 1, limit = 10, type } = params;
    
    // If type is specified, use the by-type endpoint
    let url = type 
      ? `${API_BASE_URL}/by-type/${type}?page=${page}&pageSize=${limit}`
      : `${API_BASE_URL}?page=${page}&pageSize=${limit}`;
    
    return await fetchWithAuth(url, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
    });
  } catch (error) {
    logError("NotificationService", error, {
      action: "getNotifications",
      params,
    });
    return handleErrorResponse(false, null, "Đã xảy ra lỗi khi lấy thông báo");
  }
}

/**
 * Get unread notification count
 * @returns {Promise<Object>} Response with notification count data
 */
export async function getUnreadCount() {
  try {
    return await fetchWithAuth(`${API_BASE_URL}/unread-count`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
    });
  } catch (error) {
    logError("NotificationService", error, {
      action: "getUnreadCount",
    });
    return handleErrorResponse(false, null, "Đã xảy ra lỗi khi lấy số thông báo chưa đọc");
  }
}

/**
 * Mark notifications as read
 * @param {Object} params - Request parameters
 * @param {Array<string>} params.ids - Array of notification IDs to mark as read
 * @returns {Promise<Object>} Response data
 */
export async function markAsRead(params) {
  try {
    // The API expects marking one notification at a time with a specific endpoint
    if (params.ids && params.ids.length > 0) {
      // Mark the first notification in the array
      const id = params.ids[0];
      return await fetchWithAuth(`${API_BASE_URL}/${id}/mark-as-read`, {
        method: "PUT", // Changed from POST to PUT as per API doc
        headers: {
          "Content-Type": "application/json",
        },
      });
    }
    return handleErrorResponse(false, null, "Không có ID thông báo được cung cấp");
  } catch (error) {
    logError("NotificationService", error, {
      action: "markAsRead",
      params,
    });
    return handleErrorResponse(false, null, "Đã xảy ra lỗi khi đánh dấu thông báo đã đọc");
  }
}

/**
 * Mark all notifications as read
 * @returns {Promise<Object>} Response data
 */
export async function markAllAsRead() {
  try {
    return await fetchWithAuth(`${API_BASE_URL}/mark-all-as-read`, {
      method: "PUT", // Changed from POST to PUT as per API doc
      headers: {
        "Content-Type": "application/json",
      },
    });
  } catch (error) {
    logError("NotificationService", error, {
      action: "markAllAsRead",
    });
    return handleErrorResponse(false, null, "Đã xảy ra lỗi khi đánh dấu tất cả thông báo đã đọc");
  }
}

/**
 * Get latest notifications (for navbar dropdown)
 * @param {number} limit - Number of notifications to get
 * @returns {Promise<Object>} Response with notifications data
 */
export async function getLatestNotifications(limit = 10) {
  try {
    // Using the default endpoint with a small page size for latest notifications
    return await fetchWithAuth(`${API_BASE_URL}?page=1&pageSize=${limit}`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
    });
  } catch (error) {
    logError("NotificationService", error, {
      action: "getLatestNotifications",
      limit,
    });
    return handleErrorResponse(false, null, "Đã xảy ra lỗi khi lấy thông báo mới nhất");
  }
} 