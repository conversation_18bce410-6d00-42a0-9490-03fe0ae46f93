
## API Documentation

Here is the documentation for all notification endpoints:

### 1. Get All Notifications for Current User

**Endpoint:** `GET /api/notifications`

**Description:** Retrieves all notifications specific to the authenticated user.

**Query Parameters:**
- `fromDate` (optional): Filter notifications created on or after this date
- `toDate` (optional): Filter notifications created on or before this date
- `page` (optional): Page number (default: 1)
- `pageSize` (optional): Number of items per page (default: 10)

**Response:**
```json
{
  "items": [
    {
      "id": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
      "userId": "1fa85f64-5717-4562-b3fc-2c963f66afa1",
      "type": "transaction",
      "title": "Top-up Successful",
      "message": "Your wallet has been topped up with 500,000 VND",
      "isRead": false,
      "createdAt": "2023-09-15T10:30:45.123Z"
    },
    {
      "id": "4fa85f64-5717-4562-b3fc-2c963f66afa7",
      "userId": null,
      "type": "system",
      "title": "System Maintenance",
      "message": "The system will be down for maintenance on Saturday from 2-4 AM",
      "isRead": true,
      "createdAt": "2023-09-10T08:15:30.456Z"
    }
  ],
  "totalCount": 15,
  "pageCount": 8,
  "currentPage": 1,
  "pageSize": 2
}
```

### 2. Get Notification By ID

**Endpoint:** `GET /api/notifications/{id}`

**Description:** Retrieves a specific notification by its ID.

**Response:**
```json
{
  "id": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
  "userId": "1fa85f64-5717-4562-b3fc-2c963f66afa1",
  "type": "transaction",
  "title": "Top-up Successful",
  "message": "Your wallet has been topped up with 500,000 VND",
  "isRead": false,
  "createdAt": "2023-09-15T10:30:45.123Z"
}
```

### 3. Get Notifications By Type

**Endpoint:** `GET /api/notifications/by-type/{type}`

**Description:** Retrieves notifications of a specific type.

**URL Parameters:**
- `type`: One of "system", "transaction", "contact", "promotion"

**Query Parameters:**
- `fromDate` (optional): Filter notifications created on or after this date
- `toDate` (optional): Filter notifications created on or before this date
- `page` (optional): Page number (default: 1)
- `pageSize` (optional): Number of items per page (default: 10)

**Response:** Same format as "Get All Notifications"

### 4. Get Notifications By Type And User

**Endpoint:** `GET /api/notifications/by-type/{type}/user/{userId}`

**Description:** Retrieves notifications of a specific type for a specific user (admin only).

**URL Parameters:**
- `type`: One of "system", "transaction", "contact", "promotion"
- `userId`: The ID of the user

**Query Parameters:**
- `fromDate` (optional): Filter notifications created on or after this date
- `toDate` (optional): Filter notifications created on or before this date
- `page` (optional): Page number (default: 1)
- `pageSize` (optional): Number of items per page (default: 10)

**Response:** Same format as "Get All Notifications"

### 5. Get User Notifications (Including System/Promotion)

**Endpoint:** `GET /api/notifications/for-user`

**Description:** Retrieves all notifications for the current user, including system and promotion notifications.

**Query Parameters:**
- `fromDate` (optional): Filter notifications created on or after this date
- `toDate` (optional): Filter notifications created on or before this date
- `page` (optional): Page number (default: 1)
- `pageSize` (optional): Number of items per page (default: 10)

**Response:** Same format as "Get All Notifications"

### 6. Mark Notification As Read

**Endpoint:** `PUT /api/notifications/{id}/mark-as-read`

**Description:** Marks a specific notification as read.

**Response:** 204 No Content

### 7. Mark All Notifications As Read

**Endpoint:** `PUT /api/notifications/mark-all-as-read`

**Description:** Marks all of the current user's notifications as read.

**Response:** 204 No Content

### 8. Delete Notification

**Endpoint:** `DELETE /api/notifications/{id}`

**Description:** Deletes a specific notification.

**Response:** 204 No Content

### 9. Get Unread Notification Count

**Endpoint:** `GET /api/notifications/unread-count`

**Description:** Gets the count of unread notifications for the current user.

**Response:**
```json
{
  "count": 5
}
```

This implementation provides a comprehensive notification system that meets all your requirements while maintaining good performance through efficient database queries and pagination.


# Notification API Endpoints Documentation

Below is a comprehensive list of the API endpoints in the `NotificationController.cs` with their methods, request data, parameters, and response data formats.

## 1. Get All Notifications for Current User

**Method:** `GET`

**URL:** `/api/notifications`

**Query Parameters:**
- `fromDate` (optional): ISO 8601 date format (e.g., `2023-09-15T00:00:00Z`) - Filter notifications created after this date
- `toDate` (optional): ISO 8601 date format (e.g., `2023-09-30T23:59:59Z`) - Filter notifications created before this date
- `page` (optional): Integer, default is 1 - The page number for pagination
- `pageSize` (optional): Integer, default is 10 - Number of items per page

**Response Format:**
```json
{
  "items": [
    {
      "id": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
      "userId": "0194ca0d-9c32-724b-ab13-94684bb88af0",
      "type": "wallet_update",
      "title": "Wallet Top-up Successful",
      "message": "Your wallet has been topped up with 500,000 VND.",
      "isRead": false,
      "createdAt": "2023-09-15T10:30:45.123Z"
    },
    // More notifications...
  ],
  "totalCount": 25,
  "pageCount": 3,
  "currentPage": 1,
  "pageSize": 10
}
```

## 2. Get Notification By ID

**Method:** `GET`

**URL:** `/api/notifications/{id}`

**URL Parameters:**
- `id`: GUID - The ID of the notification to retrieve

**Response Format:**
```json
{
  "id": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
  "userId": "0194ca0d-9c32-724b-ab13-94684bb88af0",
  "type": "wallet_update",
  "title": "Wallet Top-up Successful",
  "message": "Your wallet has been topped up with 500,000 VND.",
  "isRead": false,
  "createdAt": "2023-09-15T10:30:45.123Z"
}
```

## 3. Get Notifications By Type

**Method:** `GET`

**URL:** `/api/notifications/by-type/{type}`

**URL Parameters:**
- `type`: String - The notification type (`news`, `wallet_update`, `promotion`, or `customer_message`)

**Query Parameters:**
- `fromDate` (optional): ISO 8601 date format - Filter by start date
- `toDate` (optional): ISO 8601 date format - Filter by end date
- `page` (optional): Integer, default is 1
- `pageSize` (optional): Integer, default is 10

**Response Format:** Same as "Get All Notifications" endpoint

## 4. Get Notifications By Type And User (Admin Only)

**Method:** `GET`

**URL:** `/api/notifications/by-type/{type}/user/{userId}`

**Authorization:** Requires admin privileges

**URL Parameters:**
- `type`: String - The notification type (`news`, `wallet_update`, `promotion`, or `customer_message`)
- `userId`: GUID - The ID of the user whose notifications to retrieve

**Query Parameters:**
- `fromDate` (optional): ISO 8601 date format - Filter by start date
- `toDate` (optional): ISO 8601 date format - Filter by end date
- `page` (optional): Integer, default is 1
- `pageSize` (optional): Integer, default is 10

**Response Format:** Same as "Get All Notifications" endpoint

## 5. Get User Notifications (Including News and Promotions)

**Method:** `GET`

**URL:** `/api/notifications/for-user`

**Query Parameters:**
- `fromDate` (optional): ISO 8601 date format - Filter by start date
- `toDate` (optional): ISO 8601 date format - Filter by end date
- `page` (optional): Integer, default is 1
- `pageSize` (optional): Integer, default is 10

**Response Format:** Same as "Get All Notifications" endpoint

**Note:** This endpoint retrieves both user-specific notifications and system-wide news and promotion notifications.

## 6. Mark Notification As Read

**Method:** `PUT`

**URL:** `/api/notifications/{id}/mark-as-read`

**URL Parameters:**
- `id`: GUID - The ID of the notification to mark as read

**Response Format:** 
- Success: HTTP 204 No Content
- Not Found: HTTP 404 Not Found

## 7. Mark All Notifications As Read

**Method:** `PUT`

**URL:** `/api/notifications/mark-all-as-read`

**Response Format:**
- Success: HTTP 204 No Content

## 8. Delete Notification

**Method:** `DELETE`

**URL:** `/api/notifications/{id}`

**URL Parameters:**
- `id`: GUID - The ID of the notification to delete

**Response Format:**
- Success: HTTP 204 No Content
- Not Found: HTTP 404 Not Found

## 9. Get Unread Notification Count

**Method:** `GET`

**URL:** `/api/notifications/unread-count`

**Response Format:**
```json
{
  "count": 5
}
```

## Error Responses

All endpoints may return the following error responses:

### 400 Bad Request
```json
{
  "message": "User không hợp lệ"
}
```
or
```json
{
  "message": "Invalid notification type"
}
```

### 401 Unauthorized
Returned when the authorization token is missing or invalid.

### 403 Forbidden
Returned when trying to access admin-only endpoints without admin privileges.

### 404 Not Found
Returned when the requested resource is not found.

### 500 Internal Server Error
Returned for unexpected server errors.

## Note on Notification Types

Currently, the `IsValidNotificationType` method in the controller validates against `system`, `transaction`, `contact`, and `promotion`. This should be updated to validate against `news`, `wallet_update`, `customer_message`, and `promotion` to match the actual notification types used in your system.
