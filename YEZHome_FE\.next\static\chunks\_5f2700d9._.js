(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/app/actions/server/data:2d749f [app-client] (ecmascript) <text/javascript>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/* __next_internal_action_entry_do_not_use__ [{"409ad784b92a1a313639c04f81ca2c94d075b8cf47":"bulkDeleteProperties"},"app/actions/server/property.jsx",""] */ __turbopack_context__.s({
    "bulkDeleteProperties": (()=>bulkDeleteProperties)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-client-wrapper.js [app-client] (ecmascript)");
"use turbopack no side effects";
;
var bulkDeleteProperties = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createServerReference"])("409ad784b92a1a313639c04f81ca2c94d075b8cf47", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["callServer"], void 0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["findSourceMapURL"], "bulkDeleteProperties"); //# sourceMappingURL=data:application/json;base64,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
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/app/actions/server/data:c34ad3 [app-client] (ecmascript) <text/javascript>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/* __next_internal_action_entry_do_not_use__ [{"605e9ce34b71779c72cc0fd79fadaa516765e289db":"bulkUpdatePropertyHighlight"},"app/actions/server/property.jsx",""] */ __turbopack_context__.s({
    "bulkUpdatePropertyHighlight": (()=>bulkUpdatePropertyHighlight)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-client-wrapper.js [app-client] (ecmascript)");
"use turbopack no side effects";
;
var bulkUpdatePropertyHighlight = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createServerReference"])("605e9ce34b71779c72cc0fd79fadaa516765e289db", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["callServer"], void 0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["findSourceMapURL"], "bulkUpdatePropertyHighlight"); //# sourceMappingURL=data:application/json;base64,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
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/components/ui/checkbox.jsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "Checkbox": (()=>Checkbox)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$checkbox$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@radix-ui/react-checkbox/dist/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$check$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Check$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/check.js [app-client] (ecmascript) <export default as Check>");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/utils.js [app-client] (ecmascript)");
"use client";
;
;
;
;
;
const Checkbox = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["forwardRef"])(_c = ({ className, ...props }, ref)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$checkbox$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Root"], {
        ref: ref,
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])("peer h-4 w-4 shrink-0 rounded-sm border border-primary shadow focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground", className),
        ...props,
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$checkbox$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Indicator"], {
            className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])("flex items-center justify-center text-current"),
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$check$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Check$3e$__["Check"], {
                className: "h-4 w-4"
            }, void 0, false, {
                fileName: "[project]/components/ui/checkbox.jsx",
                lineNumber: 18,
                columnNumber: 7
            }, this)
        }, void 0, false, {
            fileName: "[project]/components/ui/checkbox.jsx",
            lineNumber: 17,
            columnNumber: 5
        }, this)
    }, void 0, false, {
        fileName: "[project]/components/ui/checkbox.jsx",
        lineNumber: 10,
        columnNumber: 3
    }, this));
_c1 = Checkbox;
Checkbox.displayName = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$checkbox$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Root"].displayName;
;
var _c, _c1;
__turbopack_context__.k.register(_c, "Checkbox$React.forwardRef");
__turbopack_context__.k.register(_c1, "Checkbox");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/app/actions/server/data:be55d0 [app-client] (ecmascript) <text/javascript>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/* __next_internal_action_entry_do_not_use__ [{"7085800a14a02e3d0acf1cb90a916071c6624bb6c0":"getPropertyByUser"},"app/actions/server/property.jsx",""] */ __turbopack_context__.s({
    "getPropertyByUser": (()=>getPropertyByUser)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-client-wrapper.js [app-client] (ecmascript)");
"use turbopack no side effects";
;
var getPropertyByUser = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createServerReference"])("7085800a14a02e3d0acf1cb90a916071c6624bb6c0", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["callServer"], void 0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["findSourceMapURL"], "getPropertyByUser"); //# sourceMappingURL=data:application/json;base64,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
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/app/actions/server/data:bccbeb [app-client] (ecmascript) <text/javascript>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/* __next_internal_action_entry_do_not_use__ [{"401a97280d5ce9565943fdcbe639ca98ed3df6816d":"deletePropertyById"},"app/actions/server/property.jsx",""] */ __turbopack_context__.s({
    "deletePropertyById": (()=>deletePropertyById)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-client-wrapper.js [app-client] (ecmascript)");
"use turbopack no side effects";
;
var deletePropertyById = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createServerReference"])("401a97280d5ce9565943fdcbe639ca98ed3df6816d", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["callServer"], void 0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["findSourceMapURL"], "deletePropertyById"); //# sourceMappingURL=data:application/json;base64,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
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/app/actions/server/data:5261b0 [app-client] (ecmascript) <text/javascript>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/* __next_internal_action_entry_do_not_use__ [{"40e516e88dde32b6d807dd4efdd3a65accb46d6fe9":"verifyPropertyRemainingTimes"},"app/actions/server/property.jsx",""] */ __turbopack_context__.s({
    "verifyPropertyRemainingTimes": (()=>verifyPropertyRemainingTimes)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-client-wrapper.js [app-client] (ecmascript)");
"use turbopack no side effects";
;
var verifyPropertyRemainingTimes = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createServerReference"])("40e516e88dde32b6d807dd4efdd3a65accb46d6fe9", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["callServer"], void 0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["findSourceMapURL"], "verifyPropertyRemainingTimes"); //# sourceMappingURL=data:application/json;base64,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
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/app/actions/server/data:cf9c15 [app-client] (ecmascript) <text/javascript>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/* __next_internal_action_entry_do_not_use__ [{"40349d8aaa75adc55fddf542d24addefd0ff4b906e":"updatePropertyStatus"},"app/actions/server/property.jsx",""] */ __turbopack_context__.s({
    "updatePropertyStatus": (()=>updatePropertyStatus)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-client-wrapper.js [app-client] (ecmascript)");
"use turbopack no side effects";
;
var updatePropertyStatus = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createServerReference"])("40349d8aaa75adc55fddf542d24addefd0ff4b906e", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["callServer"], void 0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["findSourceMapURL"], "updatePropertyStatus"); //# sourceMappingURL=data:application/json;base64,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
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/hooks/usePropertyList.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "usePropertyList": (()=>usePropertyList)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$hooks$2f$use$2d$toast$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/hooks/use-toast.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$contexts$2f$AlertContext$2e$jsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/contexts/AlertContext.jsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/navigation.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/next-intl/dist/esm/development/react-client/index.js [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$actions$2f$server$2f$data$3a$be55d0__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$text$2f$javascript$3e$__ = __turbopack_context__.i("[project]/app/actions/server/data:be55d0 [app-client] (ecmascript) <text/javascript>");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$actions$2f$server$2f$data$3a$bccbeb__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$text$2f$javascript$3e$__ = __turbopack_context__.i("[project]/app/actions/server/data:bccbeb [app-client] (ecmascript) <text/javascript>");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$actions$2f$server$2f$data$3a$5261b0__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$text$2f$javascript$3e$__ = __turbopack_context__.i("[project]/app/actions/server/data:5261b0 [app-client] (ecmascript) <text/javascript>");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$actions$2f$server$2f$data$3a$cf9c15__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$text$2f$javascript$3e$__ = __turbopack_context__.i("[project]/app/actions/server/data:cf9c15 [app-client] (ecmascript) <text/javascript>");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$actions$2f$server$2f$data$3a$2d749f__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$text$2f$javascript$3e$__ = __turbopack_context__.i("[project]/app/actions/server/data:2d749f [app-client] (ecmascript) <text/javascript>");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$actions$2f$server$2f$data$3a$c34ad3__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$text$2f$javascript$3e$__ = __turbopack_context__.i("[project]/app/actions/server/data:c34ad3 [app-client] (ecmascript) <text/javascript>");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$enum$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/enum.js [app-client] (ecmascript)");
var _s = __turbopack_context__.k.signature();
;
;
;
;
;
;
;
// Define filter keys
const FilterKeys = {
    ALL: "all",
    APPROVED: "Approved",
    PENDING_APPROVAL: "PendingApproval",
    REJECTED_BY_ADMIN: "RejectedByAdmin",
    REJECTED_DUE_TO_UNPAID: "RejectedDueToUnpaid",
    WAITING_PAYMENT: "WaitingPayment",
    EXPIRED: "Expired",
    DRAFT: "Draft",
    SOLD: "Sold"
};
function usePropertyList(initialData) {
    _s();
    const [data, setData] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(initialData || []);
    const [loadingId, setLoadingId] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const [isLoading, setIsLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [isLoadingFilterCounts, setIsLoadingFilterCounts] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(true);
    const [isInitialized, setIsInitialized] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [searchTerm, setSearchTerm] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])("");
    const [debouncedSearchTerm, setDebouncedSearchTerm] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])("");
    const [activeFilter, setActiveFilter] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(FilterKeys.ALL);
    const [selectedIds, setSelectedIds] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(new Set());
    const [currentPage, setCurrentPage] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(1);
    const [pageSize] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(10);
    const { toast } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$hooks$2f$use$2d$toast$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useToast"])();
    const { showAlert } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$contexts$2f$AlertContext$2e$jsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAlert"])();
    const router = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"])();
    const t = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"])("PropertyList");
    // Refs for performance optimization
    const searchTimeoutRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    // State for filter counts - memoized initial state
    const [filterCounts, setFilterCounts] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({
        "usePropertyList.useState": ()=>({
                [FilterKeys.ALL]: 0,
                [FilterKeys.APPROVED]: 0,
                [FilterKeys.PENDING_APPROVAL]: 0,
                [FilterKeys.REJECTED_BY_ADMIN]: 0,
                [FilterKeys.REJECTED_DUE_TO_UNPAID]: 0,
                [FilterKeys.WAITING_PAYMENT]: 0,
                [FilterKeys.EXPIRED]: 0,
                [FilterKeys.DRAFT]: 0,
                [FilterKeys.SOLD]: 0
            })
    }["usePropertyList.useState"]);
    // Debounce search input
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "usePropertyList.useEffect": ()=>{
            if (searchTimeoutRef.current) {
                clearTimeout(searchTimeoutRef.current);
            }
            searchTimeoutRef.current = setTimeout({
                "usePropertyList.useEffect": ()=>{
                    setDebouncedSearchTerm(searchTerm);
                }
            }["usePropertyList.useEffect"], 300); // 300ms debounce
            return ({
                "usePropertyList.useEffect": ()=>{
                    if (searchTimeoutRef.current) {
                        clearTimeout(searchTimeoutRef.current);
                    }
                }
            })["usePropertyList.useEffect"];
        }
    }["usePropertyList.useEffect"], [
        searchTerm
    ]);
    // Fetch filter counts from server
    const fetchFilterCounts = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "usePropertyList.useCallback[fetchFilterCounts]": async ()=>{
            setIsLoadingFilterCounts(true);
            try {
                const result = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$actions$2f$server$2f$data$3a$be55d0__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$text$2f$javascript$3e$__["getPropertyByUser"])("counts");
                if (result && result?.success && result?.data) {
                    setFilterCounts({
                        [FilterKeys.ALL]: result.data.total || 0,
                        [FilterKeys.APPROVED]: result.data.approved || 0,
                        [FilterKeys.PENDING_APPROVAL]: result.data.pendingApproval || 0,
                        [FilterKeys.REJECTED_BY_ADMIN]: result.data.rejectedByAdmin || 0,
                        [FilterKeys.REJECTED_DUE_TO_UNPAID]: result.data.rejectedDueToUnpaid || 0,
                        [FilterKeys.WAITING_PAYMENT]: result.data.waitingPayment || 0,
                        [FilterKeys.EXPIRED]: result.data.expired || 0,
                        [FilterKeys.DRAFT]: result.data.draft || 0,
                        [FilterKeys.SOLD]: result.data.sold || 0
                    });
                }
            } catch (error) {
                console.error("Error fetching filter counts:", error);
                toast({
                    description: "Failed to fetch property statistics",
                    variant: "destructive"
                });
            } finally{
                setIsLoadingFilterCounts(false);
            }
        }
    }["usePropertyList.useCallback[fetchFilterCounts]"], [
        toast
    ]);
    // Data fetching logic
    const fetchProperties = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "usePropertyList.useCallback[fetchProperties]": async (status = null, page = 1, size = pageSize)=>{
            setIsLoading(true);
            try {
                const result = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$actions$2f$server$2f$data$3a$be55d0__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$text$2f$javascript$3e$__["getPropertyByUser"])(status, page, size);
                if (result.success && result.data) {
                    const propertyData = result.data.items || [];
                    setData(propertyData);
                    setCurrentPage(result.data.currentPage || page);
                    fetchFilterCounts();
                } else {
                    console.error("API returned error:", result);
                    toast({
                        description: result.message || "Failed to fetch properties",
                        variant: "destructive"
                    });
                }
            } catch (error) {
                console.error("Error fetching properties:", error);
                toast({
                    description: "An unexpected error occurred",
                    variant: "destructive"
                });
            } finally{
                setIsLoading(false);
            }
        }
    }["usePropertyList.useCallback[fetchProperties]"], [
        pageSize,
        toast,
        fetchFilterCounts
    ]);
    // Initialize component - fetch filter counts and handle initial data
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "usePropertyList.useEffect": ()=>{
            const initializeComponent = {
                "usePropertyList.useEffect.initializeComponent": async ()=>{
                    await fetchFilterCounts();
                    if (initialData && Array.isArray(initialData) && initialData.length > 0 && activeFilter === FilterKeys.ALL) {
                        setData(initialData);
                    } else {
                        const statusParam = activeFilter !== FilterKeys.ALL ? activeFilter : null;
                        await fetchProperties(statusParam, 1, pageSize);
                    }
                    setIsInitialized(true);
                }
            }["usePropertyList.useEffect.initializeComponent"];
            initializeComponent();
        }
    }["usePropertyList.useEffect"], []); // Only run once on mount
    // Handle filter changes after initialization
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "usePropertyList.useEffect": ()=>{
            if (!isInitialized) return;
            const statusParam = activeFilter !== FilterKeys.ALL ? activeFilter : null;
            fetchProperties(statusParam, 1, pageSize);
        }
    }["usePropertyList.useEffect"], [
        activeFilter,
        isInitialized,
        fetchProperties,
        pageSize
    ]);
    // Filtering logic for search - optimized with debounced search
    const filteredData = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "usePropertyList.useMemo[filteredData]": ()=>{
            if (!Array.isArray(data)) {
                console.error("Data is not an array:", data);
                return [];
            }
            if (!debouncedSearchTerm.trim()) {
                return data;
            }
            const searchLower = debouncedSearchTerm.toLowerCase();
            return data.filter({
                "usePropertyList.useMemo[filteredData]": (property)=>{
                    return property && (property.name && property.name.toLowerCase().includes(searchLower) || property.address && property.address.toLowerCase().includes(searchLower) || property.addressSelected && property.addressSelected.toLowerCase().includes(searchLower));
                }
            }["usePropertyList.useMemo[filteredData]"]);
        }
    }["usePropertyList.useMemo[filteredData]"], [
        data,
        debouncedSearchTerm
    ]);
    // Memoized computed values for better performance
    const computedValues = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "usePropertyList.useMemo[computedValues]": ()=>{
            const isOverallLoading = isLoading || isLoadingFilterCounts || !isInitialized;
            const isAllSelected = filteredData.length > 0 && selectedIds.size === filteredData.length;
            const isIndeterminate = selectedIds.size > 0 && selectedIds.size < filteredData.length;
            const isEmptyDatabase = !isOverallLoading && filterCounts[FilterKeys.ALL] === 0;
            const isEmptySearchResults = !isOverallLoading && filterCounts[FilterKeys.ALL] > 0 && (!filteredData || filteredData.length === 0);
            const hasResults = !isOverallLoading && filteredData && filteredData.length > 0;
            return {
                isOverallLoading,
                isAllSelected,
                isIndeterminate,
                isEmptyDatabase,
                isEmptySearchResults,
                hasResults
            };
        }
    }["usePropertyList.useMemo[computedValues]"], [
        isLoading,
        isLoadingFilterCounts,
        isInitialized,
        filteredData,
        selectedIds.size,
        filterCounts
    ]);
    // Handler functions
    const handleEdit = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "usePropertyList.useCallback[handleEdit]": (propertyId)=>{
            router.push(`/user/bds/${propertyId}`);
        }
    }["usePropertyList.useCallback[handleEdit]"], [
        router
    ]);
    const handleDelete = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "usePropertyList.useCallback[handleDelete]": async (propertyId)=>{
            showAlert({
                title: t("deleteConfirmTitle"),
                message: t("deleteConfirmMessage"),
                onConfirm: {
                    "usePropertyList.useCallback[handleDelete]": async ()=>{
                        setLoadingId(propertyId);
                        try {
                            const result = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$actions$2f$server$2f$data$3a$bccbeb__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$text$2f$javascript$3e$__["deletePropertyById"])(propertyId);
                            if (result.success) {
                                setSelectedIds({
                                    "usePropertyList.useCallback[handleDelete]": (prev)=>{
                                        const newSet = new Set(prev);
                                        newSet.delete(propertyId);
                                        return newSet;
                                    }
                                }["usePropertyList.useCallback[handleDelete]"]);
                                toast({
                                    description: t("deleteSuccessToast"),
                                    className: "bg-teal-600 text-white"
                                });
                                fetchProperties(activeFilter !== FilterKeys.ALL ? activeFilter : null, 1, pageSize);
                            } else {
                                toast({
                                    description: result.message || t("deleteErrorToast"),
                                    variant: "destructive"
                                });
                            }
                        } catch (error) {
                            toast({
                                description: t("deleteErrorToast"),
                                variant: "destructive"
                            });
                        } finally{
                            setLoadingId(null);
                        }
                    }
                }["usePropertyList.useCallback[handleDelete]"]
            });
        }
    }["usePropertyList.useCallback[handleDelete]"], [
        showAlert,
        toast,
        t,
        fetchProperties,
        activeFilter,
        pageSize
    ]);
    const handleSendToReviewRequest = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "usePropertyList.useCallback[handleSendToReviewRequest]": async (propertyId)=>{
            setLoadingId(propertyId);
            try {
                const remainingTimes = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$actions$2f$server$2f$data$3a$5261b0__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$text$2f$javascript$3e$__["verifyPropertyRemainingTimes"])(propertyId);
                if (remainingTimes.success) {
                    showAlert({
                        title: t("verifyConfirmTitle"),
                        message: t("verifyConfirmMessage", {
                            remainingTimes: remainingTimes.data
                        }),
                        onConfirm: {
                            "usePropertyList.useCallback[handleSendToReviewRequest]": async ()=>{
                                try {
                                    setLoadingId(propertyId);
                                    const formData = new FormData();
                                    formData.append("propertyId", propertyId);
                                    formData.append("status", __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$enum$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PropertyStatus"].PENDING_APPROVAL);
                                    const result = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$actions$2f$server$2f$data$3a$cf9c15__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$text$2f$javascript$3e$__["updatePropertyStatus"])(formData);
                                    if (result.success) {
                                        toast({
                                            description: t("verifySuccessToast"),
                                            className: "bg-teal-600 text-white"
                                        });
                                        fetchProperties(activeFilter !== FilterKeys.ALL ? activeFilter : null, 1, pageSize);
                                    } else {
                                        toast({
                                            description: result?.message || t("verifyErrorToast"),
                                            variant: "destructive"
                                        });
                                    }
                                } catch (error) {
                                    console.error("Error sending verification request:", error);
                                    toast({
                                        description: t("verifyGenericErrorToast"),
                                        variant: "destructive"
                                    });
                                } finally{
                                    setLoadingId(null);
                                }
                            }
                        }["usePropertyList.useCallback[handleSendToReviewRequest]"],
                        hasCancel: true,
                        onCancel: {
                            "usePropertyList.useCallback[handleSendToReviewRequest]": ()=>setLoadingId(null)
                        }["usePropertyList.useCallback[handleSendToReviewRequest]"]
                    });
                } else {
                    setLoadingId(null);
                    toast({
                        description: remainingTimes?.message || t("verifyCheckErrorToast"),
                        className: "bg-red-600 text-white"
                    });
                }
            } catch (error) {
                setLoadingId(null);
                console.error("Error checking remaining verification times:", error);
                toast({
                    description: t("verifyGenericErrorToast"),
                    className: "bg-red-600 text-white"
                });
            }
        }
    }["usePropertyList.useCallback[handleSendToReviewRequest]"], [
        showAlert,
        toast,
        t,
        fetchProperties,
        activeFilter,
        pageSize
    ]);
    const handleBulkHighlight = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "usePropertyList.useCallback[handleBulkHighlight]": ()=>{
            if (selectedIds.size === 0) return;
            showAlert({
                title: t("bulkHighlightConfirmTitle", {
                    count: selectedIds.size
                }),
                message: t("bulkHighlightConfirmMessage"),
                onConfirm: {
                    "usePropertyList.useCallback[handleBulkHighlight]": async ()=>{
                        setLoadingId("bulk-highlight");
                        try {
                            const result = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$actions$2f$server$2f$data$3a$c34ad3__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$text$2f$javascript$3e$__["bulkUpdatePropertyHighlight"])(Array.from(selectedIds), true);
                            if (result.success) {
                                toast({
                                    description: t("bulkHighlightSuccessToast", {
                                        count: selectedIds.size
                                    }),
                                    className: "bg-teal-600 text-white"
                                });
                                fetchProperties(activeFilter !== FilterKeys.ALL ? activeFilter : null, 1, pageSize);
                            } else {
                                toast({
                                    description: result.message || t("bulkHighlightErrorToast"),
                                    variant: "destructive"
                                });
                            }
                        } catch (error) {
                            console.error("Error highlighting properties:", error);
                            toast({
                                description: t("bulkHighlightGenericErrorToast"),
                                variant: "destructive"
                            });
                        } finally{
                            setLoadingId(null);
                        }
                    }
                }["usePropertyList.useCallback[handleBulkHighlight]"]
            });
        }
    }["usePropertyList.useCallback[handleBulkHighlight]"], [
        selectedIds,
        showAlert,
        toast,
        fetchProperties,
        activeFilter,
        pageSize,
        t
    ]);
    const handleBulkDelete = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "usePropertyList.useCallback[handleBulkDelete]": ()=>{
            if (selectedIds.size === 0) return;
            showAlert({
                title: t("bulkDeleteConfirmTitle", {
                    count: selectedIds.size
                }),
                message: t("bulkDeleteConfirmMessage"),
                onConfirm: {
                    "usePropertyList.useCallback[handleBulkDelete]": async ()=>{
                        setLoadingId("bulk-delete");
                        try {
                            const result = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$actions$2f$server$2f$data$3a$2d749f__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$text$2f$javascript$3e$__["bulkDeleteProperties"])(Array.from(selectedIds));
                            if (result.success) {
                                setSelectedIds(new Set());
                                toast({
                                    description: t("bulkDeleteSuccessToast", {
                                        count: selectedIds.size
                                    }),
                                    className: "bg-teal-600 text-white"
                                });
                                fetchProperties(activeFilter !== FilterKeys.ALL ? activeFilter : null, 1, pageSize);
                            } else {
                                toast({
                                    description: result.message || t("bulkDeleteErrorToast"),
                                    variant: "destructive"
                                });
                            }
                        } catch (error) {
                            console.error("Error deleting properties:", error);
                            toast({
                                description: t("bulkDeleteGenericErrorToast"),
                                variant: "destructive"
                            });
                        } finally{
                            setLoadingId(null);
                        }
                    }
                }["usePropertyList.useCallback[handleBulkDelete]"]
            });
        }
    }["usePropertyList.useCallback[handleBulkDelete]"], [
        selectedIds,
        showAlert,
        toast,
        fetchProperties,
        activeFilter,
        pageSize,
        t
    ]);
    return {
        // State
        data,
        filteredData,
        loadingId,
        setLoadingId,
        searchTerm,
        setSearchTerm,
        activeFilter,
        setActiveFilter,
        selectedIds,
        setSelectedIds,
        currentPage,
        pageSize,
        filterCounts,
        // Computed values
        ...computedValues,
        // Functions
        fetchProperties,
        fetchFilterCounts,
        handleEdit,
        handleDelete,
        handleSendToReviewRequest,
        handleBulkHighlight,
        handleBulkDelete,
        // Constants
        FilterKeys
    };
}
_s(usePropertyList, "VwFrNHxSBYBaZJNwMuSl8lrp7/E=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$hooks$2f$use$2d$toast$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useToast"],
        __TURBOPACK__imported__module__$5b$project$5d2f$contexts$2f$AlertContext$2e$jsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAlert"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"]
    ];
});
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/app/[locale]/(protected)/user/bds/PropertyList.jsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$loader$2d$circle$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__LoaderCircle$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/loader-circle.js [app-client] (ecmascript) <export default as LoaderCircle>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$trash$2d$2$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Trash2$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/trash-2.js [app-client] (ecmascript) <export default as Trash2>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$search$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Search$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/search.js [app-client] (ecmascript) <export default as Search>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$zap$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Zap$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/zap.js [app-client] (ecmascript) <export default as Zap>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$refresh$2d$ccw$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__RefreshCcw$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/refresh-ccw.js [app-client] (ecmascript) <export default as RefreshCcw>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/navigation.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$shared$2f$lib$2f$app$2d$dynamic$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/shared/lib/app-dynamic.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$contexts$2f$AlertContext$2e$jsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/contexts/AlertContext.jsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$hooks$2f$use$2d$toast$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/hooks/use-toast.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$actions$2f$server$2f$data$3a$2d749f__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$text$2f$javascript$3e$__ = __turbopack_context__.i("[project]/app/actions/server/data:2d749f [app-client] (ecmascript) <text/javascript>");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$actions$2f$server$2f$data$3a$c34ad3__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$text$2f$javascript$3e$__ = __turbopack_context__.i("[project]/app/actions/server/data:c34ad3 [app-client] (ecmascript) <text/javascript>");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$button$2e$jsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/ui/button.jsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$enum$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/enum.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/next-intl/dist/esm/development/react-client/index.js [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$input$2e$jsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/ui/input.jsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$checkbox$2e$jsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/ui/checkbox.jsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$loading$2d$spinner$2e$jsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/ui/loading-spinner.jsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$hooks$2f$usePropertyList$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/hooks/usePropertyList.js [app-client] (ecmascript)");
;
;
;
;
;
;
var _s = __turbopack_context__.k.signature();
"use client";
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
// Lazy load heavy components
const AlertPopup = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$shared$2f$lib$2f$app$2d$dynamic$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(()=>__turbopack_context__.r("[project]/components/layout/AlertPopup.jsx [app-client] (ecmascript, next/dynamic entry, async loader)")(__turbopack_context__.i), {
    loadableGenerated: {
        modules: [
            "[project]/components/layout/AlertPopup.jsx [app-client] (ecmascript, next/dynamic entry)"
        ]
    },
    ssr: false
});
_c = AlertPopup;
const NoData = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$shared$2f$lib$2f$app$2d$dynamic$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(()=>__turbopack_context__.r("[project]/components/layout/NoData.jsx [app-client] (ecmascript, next/dynamic entry, async loader)")(__turbopack_context__.i), {
    loadableGenerated: {
        modules: [
            "[project]/components/layout/NoData.jsx [app-client] (ecmascript, next/dynamic entry)"
        ]
    }
});
_c1 = NoData;
const PropertyCard = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$shared$2f$lib$2f$app$2d$dynamic$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(()=>__turbopack_context__.r("[project]/components/user-property/PropertyCard.jsx [app-client] (ecmascript, next/dynamic entry, async loader)")(__turbopack_context__.i), {
    loadableGenerated: {
        modules: [
            "[project]/components/user-property/PropertyCard.jsx [app-client] (ecmascript, next/dynamic entry)"
        ]
    }
});
_c2 = PropertyCard;
const ContactRequestModal = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$shared$2f$lib$2f$app$2d$dynamic$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(()=>__turbopack_context__.r("[project]/components/user-property/ContactRequestModal.jsx [app-client] (ecmascript, next/dynamic entry, async loader)")(__turbopack_context__.i), {
    loadableGenerated: {
        modules: [
            "[project]/components/user-property/ContactRequestModal.jsx [app-client] (ecmascript, next/dynamic entry)"
        ]
    },
    ssr: false
});
_c3 = ContactRequestModal;
const HistoryModal = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$shared$2f$lib$2f$app$2d$dynamic$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(()=>__turbopack_context__.r("[project]/components/user-property/HistoryModal.jsx [app-client] (ecmascript, next/dynamic entry, async loader)")(__turbopack_context__.i), {
    loadableGenerated: {
        modules: [
            "[project]/components/user-property/HistoryModal.jsx [app-client] (ecmascript, next/dynamic entry)"
        ]
    },
    ssr: false
});
_c4 = HistoryModal;
function PropertyList({ initialData }) {
    _s();
    // Use the custom hook for state management and performance optimizations
    const { data, filteredData, loadingId, setLoadingId, searchTerm, setSearchTerm, activeFilter, setActiveFilter, selectedIds, setSelectedIds, currentPage, pageSize, filterCounts, isOverallLoading, isAllSelected, isIndeterminate, isEmptyDatabase, isEmptySearchResults, hasResults, fetchProperties, handleEdit, handleDelete, handleSendToReviewRequest, handleBulkHighlight, handleBulkDelete, FilterKeys } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$hooks$2f$usePropertyList$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["usePropertyList"])(initialData);
    const [selectedPropertyId, setSelectedPropertyId] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const [isContactModalOpen, setIsContactModalOpen] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [isHistoryModalOpen, setIsHistoryModalOpen] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const tCommon = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"])("Common");
    // Remaining handlers not in the hook
    const handleCheckboxChange = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "PropertyList.useCallback[handleCheckboxChange]": (propertyId, checked)=>{
            setSelectedIds({
                "PropertyList.useCallback[handleCheckboxChange]": (prev)=>{
                    const newSet = new Set(prev);
                    if (checked) {
                        newSet.add(propertyId);
                    } else {
                        newSet.delete(propertyId);
                    }
                    return newSet;
                }
            }["PropertyList.useCallback[handleCheckboxChange]"]);
        }
    }["PropertyList.useCallback[handleCheckboxChange]"], [
        setSelectedIds
    ]);
    const handleSelectAllChange = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "PropertyList.useCallback[handleSelectAllChange]": (checked)=>{
            if (checked) {
                const allIds = filteredData.map({
                    "PropertyList.useCallback[handleSelectAllChange].allIds": (p)=>p.id
                }["PropertyList.useCallback[handleSelectAllChange].allIds"]);
                setSelectedIds(new Set(allIds));
            } else {
                setSelectedIds(new Set());
            }
        }
    }["PropertyList.useCallback[handleSelectAllChange]"], [
        filteredData,
        setSelectedIds
    ]);
    const handleShowContacts = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "PropertyList.useCallback[handleShowContacts]": (propertyId)=>{
            setSelectedPropertyId(propertyId);
            setIsContactModalOpen(true);
        }
    }["PropertyList.useCallback[handleShowContacts]"], []);
    const handleShowHistory = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "PropertyList.useCallback[handleShowHistory]": (propertyId)=>{
            setSelectedPropertyId(propertyId);
            setIsHistoryModalOpen(true);
        }
    }["PropertyList.useCallback[handleShowHistory]"], []);
    const handleBulkRenew = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "PropertyList.useCallback[handleBulkRenew]": ()=>{
            if (selectedIds.size === 0) return;
        // For now, just show a message since renew functionality is not implemented in the API
        // toast implementation would need to be added here
        }
    }["PropertyList.useCallback[handleBulkRenew]"], [
        selectedIds
    ]);
    // Bulk action handlers
    const handleBulkHighlight = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "PropertyList.useCallback[handleBulkHighlight]": ()=>{
            if (selectedIds.size === 0) return;
            showAlert({
                title: t("bulkHighlightConfirmTitle", {
                    count: selectedIds.size
                }),
                message: t("bulkHighlightConfirmMessage"),
                onConfirm: {
                    "PropertyList.useCallback[handleBulkHighlight]": async ()=>{
                        setLoadingId("bulk-highlight");
                        try {
                            const result = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$actions$2f$server$2f$data$3a$c34ad3__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$text$2f$javascript$3e$__["bulkUpdatePropertyHighlight"])(Array.from(selectedIds), true);
                            if (result.success) {
                                toast({
                                    description: t("bulkHighlightSuccessToast", {
                                        count: selectedIds.size
                                    }),
                                    className: "bg-teal-600 text-white"
                                });
                                // Refresh data with current filter
                                fetchProperties(activeFilter !== FilterKeys.ALL ? activeFilter : null, 1, pageSize);
                            } else {
                                toast({
                                    description: result.message || t("bulkHighlightErrorToast"),
                                    variant: "destructive"
                                });
                            }
                        } catch (error) {
                            console.error("Error highlighting properties:", error);
                            toast({
                                description: t("bulkHighlightGenericErrorToast"),
                                variant: "destructive"
                            });
                        } finally{
                            setLoadingId(null);
                        }
                    }
                }["PropertyList.useCallback[handleBulkHighlight]"]
            });
        }
    }["PropertyList.useCallback[handleBulkHighlight]"], [
        selectedIds,
        showAlert,
        toast,
        fetchProperties,
        activeFilter,
        pageSize,
        t
    ]);
    const handleBulkRenew = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "PropertyList.useCallback[handleBulkRenew]": ()=>{
            if (selectedIds.size === 0) return;
            // For now, just show a message since renew functionality is not implemented in the API
            toast({
                description: t("bulkRenewFeatureInDevelopment"),
                variant: "default"
            });
        // When API is available, implement similar to handleBulkHighlight
        // using the appropriate API endpoint
        }
    }["PropertyList.useCallback[handleBulkRenew]"], [
        selectedIds,
        toast,
        t
    ]);
    const handleBulkDelete = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "PropertyList.useCallback[handleBulkDelete]": ()=>{
            if (selectedIds.size === 0) return;
            showAlert({
                title: t("bulkDeleteConfirmTitle", {
                    count: selectedIds.size
                }),
                message: t("bulkDeleteConfirmMessage"),
                onConfirm: {
                    "PropertyList.useCallback[handleBulkDelete]": async ()=>{
                        setLoadingId("bulk-delete");
                        try {
                            const result = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$actions$2f$server$2f$data$3a$2d749f__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$text$2f$javascript$3e$__["bulkDeleteProperties"])(Array.from(selectedIds));
                            if (result.success) {
                                // Clear the selected IDs
                                setSelectedIds(new Set());
                                toast({
                                    description: t("bulkDeleteSuccessToast", {
                                        count: selectedIds.size
                                    }),
                                    className: "bg-teal-600 text-white"
                                });
                                // Refresh data with current filter
                                fetchProperties(activeFilter !== FilterKeys.ALL ? activeFilter : null, 1, pageSize);
                            } else {
                                toast({
                                    description: result.message || t("bulkDeleteErrorToast"),
                                    variant: "destructive"
                                });
                            }
                        } catch (error) {
                            console.error("Error deleting properties:", error);
                            toast({
                                description: t("bulkDeleteGenericErrorToast"),
                                variant: "destructive"
                            });
                        } finally{
                            setLoadingId(null);
                        }
                    }
                }["PropertyList.useCallback[handleBulkDelete]"]
            });
        }
    }["PropertyList.useCallback[handleBulkDelete]"], [
        selectedIds,
        showAlert,
        toast,
        fetchProperties,
        activeFilter,
        pageSize,
        setSelectedIds,
        t
    ]);
    // --- Render ---
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(AlertPopup, {}, void 0, false, {
                fileName: "[project]/app/[locale]/(protected)/user/bds/PropertyList.jsx",
                lineNumber: 215,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "mb-4 flex flex-col sm:flex-row gap-4",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "relative flex-grow",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$search$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Search$3e$__["Search"], {
                            className: "absolute left-2.5 top-2.5 h-4 w-4 text-gray-500"
                        }, void 0, false, {
                            fileName: "[project]/app/[locale]/(protected)/user/bds/PropertyList.jsx",
                            lineNumber: 220,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$input$2e$jsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Input"], {
                            type: "search",
                            placeholder: t("searchPlaceholder"),
                            className: "pl-8 w-full",
                            value: searchTerm,
                            onChange: (e)=>setSearchTerm(e.target.value),
                            disabled: isOverallLoading
                        }, void 0, false, {
                            fileName: "[project]/app/[locale]/(protected)/user/bds/PropertyList.jsx",
                            lineNumber: 221,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/app/[locale]/(protected)/user/bds/PropertyList.jsx",
                    lineNumber: 219,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/app/[locale]/(protected)/user/bds/PropertyList.jsx",
                lineNumber: 218,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "mb-4 flex flex-wrap gap-2 border-b relative",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$button$2e$jsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                        variant: activeFilter === FilterKeys.ALL ? "solid" : "ghost",
                        size: "sm",
                        rounded: "none",
                        onClick: ()=>setActiveFilter(FilterKeys.ALL),
                        disabled: isOverallLoading,
                        children: [
                            t("all"),
                            " (",
                            filterCounts[FilterKeys.ALL],
                            ")"
                        ]
                    }, void 0, true, {
                        fileName: "[project]/app/[locale]/(protected)/user/bds/PropertyList.jsx",
                        lineNumber: 234,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$button$2e$jsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                        variant: activeFilter === FilterKeys.APPROVED ? "solid" : "ghost",
                        size: "sm",
                        rounded: "none",
                        onClick: ()=>setActiveFilter(FilterKeys.APPROVED),
                        disabled: isOverallLoading,
                        children: [
                            t("approved"),
                            " (",
                            filterCounts[FilterKeys.APPROVED],
                            ")"
                        ]
                    }, void 0, true, {
                        fileName: "[project]/app/[locale]/(protected)/user/bds/PropertyList.jsx",
                        lineNumber: 243,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$button$2e$jsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                        variant: activeFilter === FilterKeys.PENDING_APPROVAL ? "solid" : "ghost",
                        size: "sm",
                        rounded: "none",
                        onClick: ()=>setActiveFilter(FilterKeys.PENDING_APPROVAL),
                        disabled: isOverallLoading,
                        children: [
                            t("pendingApproval"),
                            " (",
                            filterCounts[FilterKeys.PENDING_APPROVAL],
                            ")"
                        ]
                    }, void 0, true, {
                        fileName: "[project]/app/[locale]/(protected)/user/bds/PropertyList.jsx",
                        lineNumber: 252,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$button$2e$jsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                        variant: activeFilter === FilterKeys.REJECTED_BY_ADMIN ? "solid" : "ghost",
                        size: "sm",
                        rounded: "none",
                        onClick: ()=>setActiveFilter(FilterKeys.REJECTED_BY_ADMIN),
                        disabled: isOverallLoading,
                        children: [
                            t("rejectedByAdmin"),
                            " (",
                            filterCounts[FilterKeys.REJECTED_BY_ADMIN],
                            ")"
                        ]
                    }, void 0, true, {
                        fileName: "[project]/app/[locale]/(protected)/user/bds/PropertyList.jsx",
                        lineNumber: 261,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$button$2e$jsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                        variant: activeFilter === FilterKeys.REJECTED_DUE_TO_UNPAID ? "solid" : "ghost",
                        size: "sm",
                        rounded: "none",
                        onClick: ()=>setActiveFilter(FilterKeys.REJECTED_DUE_TO_UNPAID),
                        disabled: isOverallLoading,
                        children: [
                            t("rejectedDueToUnpaid"),
                            " (",
                            filterCounts[FilterKeys.REJECTED_DUE_TO_UNPAID],
                            ")"
                        ]
                    }, void 0, true, {
                        fileName: "[project]/app/[locale]/(protected)/user/bds/PropertyList.jsx",
                        lineNumber: 270,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$button$2e$jsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                        variant: activeFilter === FilterKeys.WAITING_PAYMENT ? "solid" : "ghost",
                        size: "sm",
                        rounded: "none",
                        onClick: ()=>setActiveFilter(FilterKeys.WAITING_PAYMENT),
                        disabled: isOverallLoading,
                        children: [
                            t("waitingPayment"),
                            " (",
                            filterCounts[FilterKeys.WAITING_PAYMENT],
                            ")"
                        ]
                    }, void 0, true, {
                        fileName: "[project]/app/[locale]/(protected)/user/bds/PropertyList.jsx",
                        lineNumber: 279,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$button$2e$jsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                        variant: activeFilter === FilterKeys.EXPIRED ? "solid" : "ghost",
                        size: "sm",
                        rounded: "none",
                        onClick: ()=>setActiveFilter(FilterKeys.EXPIRED),
                        disabled: isOverallLoading,
                        children: [
                            t("expired"),
                            " (",
                            filterCounts[FilterKeys.EXPIRED],
                            ")"
                        ]
                    }, void 0, true, {
                        fileName: "[project]/app/[locale]/(protected)/user/bds/PropertyList.jsx",
                        lineNumber: 288,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$button$2e$jsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                        variant: activeFilter === FilterKeys.DRAFT ? "solid" : "ghost",
                        size: "sm",
                        rounded: "none",
                        onClick: ()=>setActiveFilter(FilterKeys.DRAFT),
                        disabled: isOverallLoading,
                        children: [
                            t("draft"),
                            " (",
                            filterCounts[FilterKeys.DRAFT],
                            ")"
                        ]
                    }, void 0, true, {
                        fileName: "[project]/app/[locale]/(protected)/user/bds/PropertyList.jsx",
                        lineNumber: 297,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$button$2e$jsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                        variant: activeFilter === FilterKeys.SOLD ? "solid" : "ghost",
                        size: "sm",
                        rounded: "none",
                        onClick: ()=>setActiveFilter(FilterKeys.SOLD),
                        disabled: isOverallLoading,
                        children: [
                            t("sold"),
                            " (",
                            filterCounts[FilterKeys.SOLD],
                            ")"
                        ]
                    }, void 0, true, {
                        fileName: "[project]/app/[locale]/(protected)/user/bds/PropertyList.jsx",
                        lineNumber: 306,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/app/[locale]/(protected)/user/bds/PropertyList.jsx",
                lineNumber: 233,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "mb-4 flex items-center gap-4",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$checkbox$2e$jsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Checkbox"], {
                        id: "select-all",
                        checked: isAllSelected,
                        onCheckedChange: handleSelectAllChange,
                        "aria-label": "Select all properties on this page",
                        "data-state": isIndeterminate ? "indeterminate" : isAllSelected ? "checked" : "unchecked",
                        disabled: isOverallLoading
                    }, void 0, false, {
                        fileName: "[project]/app/[locale]/(protected)/user/bds/PropertyList.jsx",
                        lineNumber: 319,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                        htmlFor: "select-all",
                        className: `text-sm font-medium ${isOverallLoading ? 'text-gray-400' : ''}`,
                        children: t("selectAll")
                    }, void 0, false, {
                        fileName: "[project]/app/[locale]/(protected)/user/bds/PropertyList.jsx",
                        lineNumber: 327,
                        columnNumber: 9
                    }, this),
                    selectedIds.size > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex gap-2 ml-auto",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$button$2e$jsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                size: "sm",
                                variant: "outline",
                                onClick: handleBulkHighlight,
                                disabled: isOverallLoading || loadingId === "bulk-delete" || loadingId === "bulk-highlight",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$zap$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Zap$3e$__["Zap"], {
                                        className: "h-4 w-4 mr-1"
                                    }, void 0, false, {
                                        fileName: "[project]/app/[locale]/(protected)/user/bds/PropertyList.jsx",
                                        lineNumber: 338,
                                        columnNumber: 15
                                    }, this),
                                    " ",
                                    tCommon("highlight_status"),
                                    " (",
                                    selectedIds.size,
                                    ")"
                                ]
                            }, void 0, true, {
                                fileName: "[project]/app/[locale]/(protected)/user/bds/PropertyList.jsx",
                                lineNumber: 332,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$button$2e$jsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                size: "sm",
                                variant: "outline",
                                onClick: handleBulkRenew,
                                disabled: isOverallLoading || loadingId === "bulk-delete" || loadingId === "bulk-highlight",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$refresh$2d$ccw$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__RefreshCcw$3e$__["RefreshCcw"], {
                                        className: "h-4 w-4 mr-1"
                                    }, void 0, false, {
                                        fileName: "[project]/app/[locale]/(protected)/user/bds/PropertyList.jsx",
                                        lineNumber: 346,
                                        columnNumber: 15
                                    }, this),
                                    " ",
                                    t("renew"),
                                    " (",
                                    selectedIds.size,
                                    ")"
                                ]
                            }, void 0, true, {
                                fileName: "[project]/app/[locale]/(protected)/user/bds/PropertyList.jsx",
                                lineNumber: 340,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$button$2e$jsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                size: "sm",
                                variant: "destructive",
                                onClick: handleBulkDelete,
                                disabled: isOverallLoading || loadingId === "bulk-delete" || loadingId === "bulk-highlight",
                                children: [
                                    loadingId === "bulk-delete" || loadingId === "bulk-highlight" ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$loader$2d$circle$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__LoaderCircle$3e$__["LoaderCircle"], {
                                        className: "animate-spin h-4 w-4 mr-1"
                                    }, void 0, false, {
                                        fileName: "[project]/app/[locale]/(protected)/user/bds/PropertyList.jsx",
                                        lineNumber: 355,
                                        columnNumber: 17
                                    }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$trash$2d$2$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Trash2$3e$__["Trash2"], {
                                        className: "h-4 w-4 mr-1"
                                    }, void 0, false, {
                                        fileName: "[project]/app/[locale]/(protected)/user/bds/PropertyList.jsx",
                                        lineNumber: 357,
                                        columnNumber: 17
                                    }, this),
                                    t("delete"),
                                    " (",
                                    selectedIds.size,
                                    ")"
                                ]
                            }, void 0, true, {
                                fileName: "[project]/app/[locale]/(protected)/user/bds/PropertyList.jsx",
                                lineNumber: 348,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/app/[locale]/(protected)/user/bds/PropertyList.jsx",
                        lineNumber: 331,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/app/[locale]/(protected)/user/bds/PropertyList.jsx",
                lineNumber: 318,
                columnNumber: 7
            }, this),
            isOverallLoading ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$loading$2d$spinner$2e$jsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["LoadingSpinner"], {}, void 0, false, {
                fileName: "[project]/app/[locale]/(protected)/user/bds/PropertyList.jsx",
                lineNumber: 367,
                columnNumber: 9
            }, this) : isEmptyDatabase ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(NoData, {
                hasCreateButton: true,
                createMessage: t("noProperty"),
                createPageRoute: "/user/bds/new",
                createButtonTitle: t("createProperty")
            }, void 0, false, {
                fileName: "[project]/app/[locale]/(protected)/user/bds/PropertyList.jsx",
                lineNumber: 369,
                columnNumber: 9
            }, this) : isEmptySearchResults ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(NoData, {
                message: t("noResults")
            }, void 0, false, {
                fileName: "[project]/app/[locale]/(protected)/user/bds/PropertyList.jsx",
                lineNumber: 376,
                columnNumber: 9
            }, this) : hasResults ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "grid gap-4",
                        children: filteredData.map((property)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(PropertyCard, {
                                property: property,
                                onEdit: handleEdit,
                                onDelete: handleDelete,
                                onSendToReview: handleSendToReviewRequest,
                                onShowContacts: handleShowContacts,
                                onShowHistory: handleShowHistory,
                                loadingId: loadingId,
                                isSelected: selectedIds.has(property.id),
                                onCheckboxChange: handleCheckboxChange
                            }, property.id, false, {
                                fileName: "[project]/app/[locale]/(protected)/user/bds/PropertyList.jsx",
                                lineNumber: 381,
                                columnNumber: 15
                            }, this))
                    }, void 0, false, {
                        fileName: "[project]/app/[locale]/(protected)/user/bds/PropertyList.jsx",
                        lineNumber: 379,
                        columnNumber: 11
                    }, this),
                    data.length > pageSize && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "mt-6 flex justify-center items-center gap-2",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$button$2e$jsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                variant: "outline",
                                size: "sm",
                                onClick: ()=>fetchProperties(activeFilter !== FilterKeys.ALL ? activeFilter : null, Math.max(1, currentPage - 1), pageSize),
                                disabled: currentPage <= 1 || isOverallLoading,
                                children: t("previous")
                            }, void 0, false, {
                                fileName: "[project]/app/[locale]/(protected)/user/bds/PropertyList.jsx",
                                lineNumber: 399,
                                columnNumber: 15
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                className: "text-sm",
                                children: [
                                    t("page"),
                                    " ",
                                    currentPage
                                ]
                            }, void 0, true, {
                                fileName: "[project]/app/[locale]/(protected)/user/bds/PropertyList.jsx",
                                lineNumber: 407,
                                columnNumber: 15
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$button$2e$jsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                variant: "outline",
                                size: "sm",
                                onClick: ()=>fetchProperties(activeFilter !== FilterKeys.ALL ? activeFilter : null, currentPage + 1, pageSize),
                                disabled: filteredData.length < pageSize || isOverallLoading,
                                children: t("next")
                            }, void 0, false, {
                                fileName: "[project]/app/[locale]/(protected)/user/bds/PropertyList.jsx",
                                lineNumber: 410,
                                columnNumber: 15
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/app/[locale]/(protected)/user/bds/PropertyList.jsx",
                        lineNumber: 398,
                        columnNumber: 13
                    }, this)
                ]
            }, void 0, true) : null,
            isContactModalOpen && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(ContactRequestModal, {
                propertyId: selectedPropertyId,
                open: isContactModalOpen,
                onClose: ()=>setIsContactModalOpen(false)
            }, void 0, false, {
                fileName: "[project]/app/[locale]/(protected)/user/bds/PropertyList.jsx",
                lineNumber: 424,
                columnNumber: 9
            }, this),
            isHistoryModalOpen && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(HistoryModal, {
                propertyId: selectedPropertyId,
                open: isHistoryModalOpen,
                onClose: ()=>setIsHistoryModalOpen(false)
            }, void 0, false, {
                fileName: "[project]/app/[locale]/(protected)/user/bds/PropertyList.jsx",
                lineNumber: 427,
                columnNumber: 30
            }, this)
        ]
    }, void 0, true);
}
_s(PropertyList, "BUxIREi40UautF0VwzfSG4OSuI4=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$hooks$2f$usePropertyList$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["usePropertyList"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"]
    ];
});
_c5 = PropertyList;
const __TURBOPACK__default__export__ = /*#__PURE__*/ _c6 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["memo"])(PropertyList);
var _c, _c1, _c2, _c3, _c4, _c5, _c6;
__turbopack_context__.k.register(_c, "AlertPopup");
__turbopack_context__.k.register(_c1, "NoData");
__turbopack_context__.k.register(_c2, "PropertyCard");
__turbopack_context__.k.register(_c3, "ContactRequestModal");
__turbopack_context__.k.register(_c4, "HistoryModal");
__turbopack_context__.k.register(_c5, "PropertyList");
__turbopack_context__.k.register(_c6, "%default%");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
}]);

//# sourceMappingURL=_5f2700d9._.js.map