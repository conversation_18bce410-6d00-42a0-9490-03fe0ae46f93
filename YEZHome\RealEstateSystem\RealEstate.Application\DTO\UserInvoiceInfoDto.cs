using System.ComponentModel.DataAnnotations;

namespace RealEstate.Application.DTO
{
    public class UserInvoiceInfoDto
    {
        public string? BuyerName { get; set; }
        
        [EmailAddress]
        public string? Email { get; set; }
        
        public string? CompanyName { get; set; }
        
        [RegularExpression(@"^(\d{10}|\d{10}-\d{3})$", ErrorMessage = "Tax Code must be 10 digits or 10 digits followed by a hyphen and 3 digits")]
        public string? TaxCode { get; set; }
        
        public string? Address { get; set; }
    }
}
