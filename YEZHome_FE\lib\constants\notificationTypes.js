import { FileText, Wallet, Gift, PhoneCall } from "lucide-react";
import { NOTIFICATION_TYPE } from "@/lib/enum";

/**
 * Get notification types with icons and labels
 * @param {Function} t - Translation function from useTranslations hook
 * @returns {Array} Array of notification type objects with id, label, and icon
 */
export const getNotificationTypes = (t) => [
  { id: NOTIFICATION_TYPE.SYSTEM, label: t('typeSystem'), icon: FileText },
  { id: NOTIFICATION_TYPE.TRANSACTION, label: t('typeTransaction'), icon: Wallet },
  { id: NOTIFICATION_TYPE.PROMOTION, label: t('typePromotion'), icon: Gift },
  { id: NOTIFICATION_TYPE.CONTACT, label: t('typeContactRequest'), icon: PhoneCall },
];

/**
 * Get notification type by ID
 * @param {Array} types - Array of notification types
 * @param {String} typeId - Notification type ID to find
 * @returns {Object|undefined} Notification type object or undefined if not found
 */
export const getNotificationTypeById = (types, typeId) => {
  return types.find(type => type.id === typeId);
};

/**
 * Get action text based on notification type
 * @param {Object} notification - Notification object
 * @param {Function} t - Translation function from useTranslations hook
 * @returns {String} Action text for the notification
 */
export const getNotificationActionText = (notification, t) => {
  switch (notification.type) {
    case NOTIFICATION_TYPE.CONTACT:
      return notification.relatedPropertyId ? t('actionTextContact') : t('actionTextContactDefault');
    case NOTIFICATION_TYPE.TRANSACTION:
      return t('actionTextTransaction');
    case NOTIFICATION_TYPE.WALLET_UPDATE:
      return t('actionTextWalletUpdate');
    case NOTIFICATION_TYPE.SYSTEM:
      return t('actionTextSystem');
    case NOTIFICATION_TYPE.PROMOTION:
      return t('actionTextPromotion');
    case NOTIFICATION_TYPE.NEWS:
      return t('actionTextNews');
    default:
      return t('actionTextDefault');
  }
};

/**
 * Check if notification is actionable
 * @param {Object} notification - Notification object
 * @returns {Boolean} True if notification has action URL or related entities
 */
export const isActionableNotification = (notification) => {
  return notification.actionUrl || notification.relatedPropertyId || notification.relatedEntityId;
}; 