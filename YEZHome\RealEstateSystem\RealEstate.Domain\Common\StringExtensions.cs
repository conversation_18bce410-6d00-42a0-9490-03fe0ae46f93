﻿using System.Globalization;
using System.Text;
using System.Text.RegularExpressions;

namespace RealEstate.Domain.Common
{
    public static class StringExtensions
    {
        public static string ToSlug(this string text)
        {
            if (string.IsNullOrEmpty(text))
            {
                return string.Empty;
            }

            // Remove diacritics from Vietnamese characters
            string normalizedString = text.Normalize(NormalizationForm.FormD);
            string normalizedFormD = new string(normalizedString.Where(c => CharUnicodeInfo.GetUnicodeCategory(c) != UnicodeCategory.NonSpacingMark).ToArray());

            // Replace spaces, hyphens, underscores with hyphens
            string slug = Regex.Replace(normalizedFormD.ToLowerInvariant(), @"\s+", "-");
            slug = Regex.Replace(slug, @"[^a-z0-9-]", "");
            slug = Regex.Replace(slug, @"--+", "-");

            return slug;
        }
    }
}
