import { clsx } from "clsx";
import { twMerge } from "tailwind-merge"

export function cn(...inputs) {
  return twMerge(clsx(inputs));
}

export function parseEmptyStringsToNull(payload) {
  if (Array.isArray(payload)) {
    return payload.map(item => parseEmptyStringsToNull(item));
  }

  if (typeof payload === 'object' && payload !== null) {
    const newPayload = { ...payload };

    Object.keys(newPayload).forEach(key => {
      if (newPayload[key] === '') {
        newPayload[key] = null;
      } else if (typeof newPayload[key] === 'object' && newPayload[key] !== null) {
        newPayload[key] = parseEmptyStringsToNull(newPayload[key]);
      }
    });

    return newPayload;
  }

  return payload;
}

export function formatCurrency(amount) {
  return new Intl.NumberFormat('vi-VN', {
    style: 'currency',
    currency: 'VND',
    maximumFractionDigits: 0
  }).format(amount);
}

export function formatDate(dateString) {
  return new Date(dateString).toLocaleDateString('vi-VN', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });
}

export const formatPriceShort = (price) => {
  if (price === null || price === undefined) return 'N/A';
  if (price >= 1000000000) {
      // Làm tròn 1 chữ số thập phân, loại bỏ .0 nếu là số nguyên tỷ
      const val = (price / 1000000000).toFixed(1);
      return val.endsWith('.0') ? val.slice(0, -2) + ' Tỷ' : val + ' Tỷ';
  }
  if (price >= 1000000) {
      // Làm tròn 1 chữ số thập phân, loại bỏ .0 nếu là số nguyên triệu
       const val = (price / 1000000).toFixed(1);
      return val.endsWith('.0') ? val.slice(0, -2) + ' Tr' : val + ' Tr';
  }
   // Định dạng số thông thường cho các giá trị nhỏ hơn 1 triệu
   if (typeof price === 'number') {
       return price.toLocaleString('vi-VN');
   }
  return String(price); // Trường hợp khác cố gắng convert sang string
};

export function debounce(func, delay) {
  let timeoutId;
  // Hàm debounce trả về một hàm mới
  const debounced = function(...args) {
    const context = this; // Lưu ngữ cảnh 'this'
    clearTimeout(timeoutId); // Xóa timer cũ nếu có
    // Thiết lập timer mới để gọi hàm gốc sau độ trễ
    timeoutId = setTimeout(() => {
      func.apply(context, args); // Gọi hàm gốc với ngữ cảnh và đối số đúng
    }, delay);
  };

  // Thêm phương thức cancel vào hàm debounced trả về
  debounced.cancel = function() {
    clearTimeout(timeoutId);
  };

  return debounced; // Trả về hàm đã được debounce
}

export function formatStatusText(status) {
  return status
    .replace(/_/g, " ")
    .toLowerCase()
    .replace(/^\w/, (c) => c.toUpperCase());
}

/**
 * Format notification time relative to current time
 * @param {string} dateString - ISO date string to format
 * @param {Function} t - Translation function from useTranslations hook
 * @param {string} locale - Current locale (e.g., 'en', 'vi')
 * @returns {string} Formatted time string (e.g., "5 minutes ago", "2 hours ago")
 */
export const formatNotificationTime = (dateString, t, locale) => {
  if (!dateString) return "";
  try {
    const date = new Date(dateString);
    const now = new Date();
    const diffMs = now - date;
    const diffSeconds = Math.floor(diffMs / 1000);
    const diffMinutes = Math.floor(diffSeconds / 60);
    const diffHours = Math.floor(diffMinutes / 60);
    const diffDays = Math.floor(diffHours / 24);

    if (diffMinutes < 60) {
      return t("notificationTimeMinutes", { count: diffMinutes });
    } else if (diffHours < 24) {
      return t("notificationTimeHours", { count: diffHours });
    } else if (diffDays < 7) {
      return t("notificationTimeDays", { count: diffDays });
    } else {
      return date.toLocaleDateString(locale, { day: "2-digit", month: "2-digit", year: "numeric" });
    }
  } catch (error) {
    console.error("Error formatting notification time:", error);
    return "";
  }
};