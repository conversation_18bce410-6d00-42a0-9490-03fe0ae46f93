using System.ComponentModel.DataAnnotations;
using static RealEstate.Domain.Common.EnumValues;

namespace RealEstate.API.DTO
{
    public class LogPropertySpendingRequest
    {
        [Required]
        public Guid PropertyId { get; set; }
        
        [Required]
        [Range(0, double.MaxValue, ErrorMessage = "Amount must be greater than or equal to 0")]
        public decimal Amount { get; set; }
        
        [Required]
        public PropertySpendingType SpendingType { get; set; }
        
        public Guid? TransactionId { get; set; }
        
        public string? Details { get; set; }
    }
} 