using RealEstate.Domain.Interfaces;

namespace RealEstate.Domain.Entities
{
    public class Ward
    {
        public int Id { get; set; }
        public string Name { get; set; }
        public string Slug { get; set; }
        public string Type { get; set; }
        public string NameWithType { get; set; }
        public string Path { get; set; }
        public string PathWithType { get; set; }
        public int DistrictId { get; set; }
        
        public District District { get; set; }
        public ICollection<Project> Projects { get; set; } = new List<Project>();
    }
} 