namespace RealEstate.Application.DTO
{
    public class RolePermissionDto
    {
        public Guid Id { get; set; }
        public Guid RoleID { get; set; }
        public Guid PermissionID { get; set; }
        public AdminRoleDto? Role { get; set; }
        public PermissionDto? Permission { get; set; }
    }

    public class CreateRolePermissionDto
    {
        public Guid RoleID { get; set; }
        public Guid PermissionID { get; set; }
    }

    public class AssignPermissionsToRoleDto
    {
        public Guid RoleID { get; set; }
        public List<Guid> PermissionIDs { get; set; } = new List<Guid>();
    }
}
