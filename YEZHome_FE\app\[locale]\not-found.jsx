import Image from "next/image";
import {Link} from '@/i18n/navigation';;
import { getTranslations } from "next-intl/server";

export default async function NotFound() {
  const t = await getTranslations("NotFound");

  return (
    <div className="flex flex-col items-center justify-center min-h-screen bg-slate-50 text-center p-4">
      <Image
        src="/404.png"
        alt={t("title")}
        width={400}
        height={400}
        className="mb-8 max-w-full h-auto"
      />
      <h1 className="text-2xl md:text-4xl font-bold text-gray-800 mb-4">{t("title")}</h1>
      <p className="text-gray-600 mb-8 max-w-md">{t("description")}</p>
      <Link
        href="/"
        className="px-6 py-3 bg-navy-blue text-white text-lg rounded-lg shadow-md hover:bg-teal-700 transition"
      >
        {t("backButton")}
      </Link>
    </div>
  );
}
