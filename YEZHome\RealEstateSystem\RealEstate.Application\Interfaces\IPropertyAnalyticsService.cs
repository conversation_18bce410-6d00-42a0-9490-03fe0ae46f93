using RealEstate.Application.DTO;
using RealEstate.Application.DTO.Analytics;

namespace RealEstate.Application.Interfaces
{
    public interface IPropertyAnalyticsService
    {
        // Core logging methods
        Task LogPropertyViewAsync(LogPropertyViewDto dto);
        Task LogPropertyEngagementEventAsync(LogPropertyEngagementEventDto dto);
        Task LogPropertySpendingAsync(LogPropertySpendingDto dto);
        
        // Get analytics
        Task<PropertyAnalyticsDto> GetPropertyAnalyticsAsync(Guid propertyId, DateTime? startDate = null, DateTime? endDate = null);
        Task<PagedResultDto<PropertyAnalyticsDto>> GetUserPropertiesAnalyticsAsync(Guid userId, PropertyAnalyticsFilterDto filter);
        
        // Export analytics
        Task<byte[]> ExportPropertyAnalyticsToExcelAsync(Guid propertyId, DateTime? startDate = null, DateTime? endDate = null);
        Task<byte[]> ExportUserPropertiesAnalyticsToExcelAsync(Guid userId, PropertyAnalyticsFilterDto filter);
        
        // Update engagement summary (can be called by a background job)
        Task UpdatePropertyEngagementSummaryAsync(Guid propertyId);
        Task UpdateAllPropertiesEngagementSummaryAsync();
    }
}
