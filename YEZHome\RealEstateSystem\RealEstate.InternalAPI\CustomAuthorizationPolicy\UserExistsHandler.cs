﻿using Microsoft.AspNetCore.Authorization;
using RealEstate.Application.Interfaces;
using System.Security.Claims;

namespace RealEstate.InternalAPI.CustomAuthorizationPolicy
{
    public class UserExistsRequirement : IAuthorizationRequirement { }
    public class UserExistsHandler : AuthorizationHandler<UserExistsRequirement>
    {
        private readonly IUserService _userService;
        private readonly ILogger<UserExistsHandler> _logger;

        public UserExistsHandler(IUserService userService, ILogger<UserExistsHandler> logger)
        {
            _userService = userService;
            _logger = logger;
        }

        protected override async Task HandleRequirementAsync(AuthorizationHandlerContext context, UserExistsRequirement requirement)
        {
            _logger.LogDebug("Checking if user exists...");

            if (context.User.Identity?.IsAuthenticated != true)
            {
                return; // Not authenticated, skip check
            }

            var userIdClaim = context.User.FindFirst(ClaimTypes.NameIdentifier)?.Value;

            if (string.IsNullOrEmpty(userIdClaim))
            {
                _logger.LogWarning("Missing or empty NameIdentifier claim.");
                context.Fail(new AuthorizationFailureReason(this, "The NameIdentifier claim is missing."));
                return;
            }

            if (Guid.TryParse(userIdClaim, out Guid userId))
            {
                var isUserExist = await _userService.IsUserExistsAsync(userId);
                if (isUserExist)
                {
                    _logger.LogDebug($"User exists: {userId}");
                    context.Succeed(requirement);
                    return;
                }
            }

            _logger.LogWarning($"User not found: {userIdClaim}");
            context.Fail(new AuthorizationFailureReason(this, "The user does not exist."));
        }
    }
}
