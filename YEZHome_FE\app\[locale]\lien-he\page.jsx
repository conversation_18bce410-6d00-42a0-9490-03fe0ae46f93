"use client"

import { useState } from "react"
import ButtonLoading from "@/components/ui/ButtonLoading"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { Mail, Phone, MapPin } from "lucide-react"
import { useTranslations } from 'next-intl'

export default function ContactUs() {
  const t = useTranslations('ContactPage')
  const [name, setName] = useState("")
  const [email, setEmail] = useState("")
  const [message, setMessage] = useState("")
  const [isSubmitting, setIsSubmitting] = useState(false)

  const handleSubmit = async (e) => {
    e.preventDefault()
    setIsSubmitting(true)
    // Add actual submission logic here (e.g., API call)
    await new Promise(resolve => setTimeout(resolve, 1000)) // Simulate delay
    
    // Reset form fields after mock submission
    setName("")
    setEmail("")
    setMessage("")
    setIsSubmitting(false)
    // Show success toast (requires useToast hook setup if not already)
    // import { useToast } from "@/hooks/use-toast"
    // const { toast } = useToast()
    // toast({ title: t('submitSuccessTitle'), description: t('submitSuccessDesc') })
  }

  return (
    <div className="min-h-screen bg-gray-100">
      {/* Hero Header with Background Image */}
      <header className="relative bg-primary text-white py-24 md:py-32">
        <div
          className="absolute inset-0 bg-cover bg-center"
          style={{ backgroundImage: "url('/contactus.webp')" }}
          aria-hidden="true"
        />
        <div className="absolute inset-0 bg-black bg-opacity-50" aria-hidden="true" /> {/* Overlay for better text visibility */}
        <div className="container mx-auto px-4 relative">
          <h1 className="text-4xl md:text-5xl font-bold text-center">{t('headerTitle')}</h1>
          <p className="mt-4 text-xl text-center">{t('headerSubtitle')}</p>
        </div>
      </header>

      {/* Main Content */}
      <div className="container mx-auto px-4 py-12">
        <div className="bg-white rounded-lg shadow-lg overflow-hidden">
          <div className="md:flex">
            {/* Contact Form */}
            <div className="md:w-2/3 p-8">
              <h2 className="text-2xl font-bold mb-6">{t('formTitle')}</h2>
              <form onSubmit={handleSubmit} className="space-y-4">
                <div>
                  <Label htmlFor="name">{t('nameLabel')}</Label>
                  <Input
                    id="name"
                    value={name}
                    onChange={(e) => setName(e.target.value)}
                    required
                    placeholder={t('namePlaceholder')}
                    disabled={isSubmitting}
                  />
                </div>
                <div>
                  <Label htmlFor="email">{t('emailLabel')}</Label>
                  <Input
                    id="email"
                    type="email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    required
                    placeholder={t('emailPlaceholder')}
                    disabled={isSubmitting}
                  />
                </div>
                <div>
                  <Label htmlFor="message">{t('messageLabel')}</Label>
                  <Textarea
                    id="message"
                    value={message}
                    onChange={(e) => setMessage(e.target.value)}
                    required
                    className="min-h-[150px]"
                    placeholder={t('messagePlaceholder')}
                    disabled={isSubmitting}
                  />
                </div>
                <ButtonLoading 
                    type="submit" 
                    className="w-full" 
                    title={t('submitButton')} 
                    showLoading={isSubmitting} 
                    disabled={isSubmitting}
                />
              </form>
            </div>

            {/* Contact Information */}
            <div className="md:w-1/3 bg-gray-50 p-8">
              <h2 className="text-2xl font-bold mb-6">{t('infoTitle')}</h2>
              <div className="space-y-4">
                <div className="flex items-start space-x-3">
                  <Mail className="w-6 h-6 text-primary mt-1 flex-shrink-0" />
                  <div>
                    <p className="font-semibold">{t('infoEmailLabel')}</p>
                    <a href={`mailto:${t('infoEmailValue')}`} className="text-primary hover:underline break-all">
                      {t('infoEmailValue')}
                    </a>
                  </div>
                </div>
                <div className="flex items-start space-x-3">
                  <Phone className="w-6 h-6 text-primary mt-1 flex-shrink-0" />
                  <div>
                    <p className="font-semibold">{t('infoPhoneLabel')}</p>
                    {/* Only make it a link if it's a valid tel URI format */}
                    <a href={`tel:${t('infoPhoneValue').replace(/\s+/g, '')}`} className="text-primary hover:underline">
                      {t('infoPhoneValue')}
                    </a>
                  </div>
                </div>
                <div className="flex items-start space-x-3">
                  <MapPin className="w-6 h-6 text-primary mt-1 flex-shrink-0" />
                  <div>
                    <p className="font-semibold">{t('infoAddressLabel')}</p>
                    <p>{t('infoAddressValue')}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

