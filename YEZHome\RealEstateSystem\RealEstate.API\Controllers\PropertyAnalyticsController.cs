using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using RealEstate.API.Attributes;
using RealEstate.API.DTO;
using RealEstate.Application.DTO;
using RealEstate.Application.DTO.Analytics;
using RealEstate.Application.Interfaces;
using RealEstate.Domain.Common;
using RealEstate.Domain.Entities;

namespace RealEstate.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class PropertyAnalyticsController : BaseController
    {
        private readonly IPropertyAnalyticsService _analyticsService;
        private readonly IPropertyService _propertyService;
        private readonly ILogger<PropertyAnalyticsController> _logger;

        public PropertyAnalyticsController(
            IPropertyAnalyticsService analyticsService,
            IPropertyService propertyService,
            ILogger<PropertyAnalyticsController> logger)
        {
            _analyticsService = analyticsService;
            _propertyService = propertyService;
            _logger = logger;
        }

        [HttpGet("property/{propertyId}")]
        public async Task<ActionResult<PropertyAnalyticsDto>> GetPropertyAnalytics(
            Guid propertyId,
            [FromQuery] DateTime? startDate = null,
            [FromQuery] DateTime? endDate = null)
        {
            try
            {
                var userId = GetUserId();
                if (!userId.HasValue)
                {
                    LogSecurityEvent(_logger, "UnauthorizedAnalyticsAccess", $"User attempted to access analytics for property {propertyId} without valid authentication");
                    return Unauthorized(new { Message = "User not authenticated" });
                }

                _logger.LogInformation("Retrieving analytics for property {PropertyId} by user {UserId} - StartDate: {StartDate}, EndDate: {EndDate}",
                    propertyId, userId.Value, startDate, endDate);
                LogUserAction(_logger, "GetPropertyAnalytics", new { PropertyId = propertyId, StartDate = startDate, EndDate = endDate });

                var analytics = await _analyticsService.GetPropertyAnalyticsAsync(propertyId, startDate, endDate);

                _logger.LogInformation("Successfully retrieved analytics for property {PropertyId}", propertyId);
                return Ok(analytics);
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting property analytics for property {PropertyId}", propertyId);
                return StatusCode(500, "An error occurred while retrieving property analytics");
            }
        }

        [HttpGet("user")]
        public async Task<ActionResult<PagedResultDto<PropertyAnalyticsDto>>> GetUserPropertiesAnalytics(
            [FromQuery] PropertyAnalyticsFilterDto filter)
        {
            try
            {
                var userId = GetUserId();
                if (!userId.HasValue)
                {
                    return Unauthorized("User not authenticated");
                }

                var analytics = await _analyticsService.GetUserPropertiesAnalyticsAsync(userId.Value, filter);
                return Ok(analytics);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting user properties analytics");
                return StatusCode(500, "An error occurred while retrieving user properties analytics");
            }
        }

        [HttpGet("property/{propertyId}/export")]
        public async Task<IActionResult> ExportPropertyAnalytics(
            Guid propertyId,
            [FromQuery] DateTime? startDate = null,
            [FromQuery] DateTime? endDate = null)
        {
            try
            {
                var userId = GetUserId();
                if (!userId.HasValue)
                {
                    return Unauthorized("User not authenticated");
                }

                var excelBytes = await _analyticsService.ExportPropertyAnalyticsToExcelAsync(propertyId, startDate, endDate);
                return File(excelBytes, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", $"property_analytics_{propertyId}.xlsx");
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error exporting property analytics for property {PropertyId}", propertyId);
                return StatusCode(500, "An error occurred while exporting property analytics");
            }
        }

        [HttpGet("user/export")]
        public async Task<IActionResult> ExportUserPropertiesAnalytics(
            [FromQuery] PropertyAnalyticsFilterDto filter)
        {
            try
            {
                var userId = GetUserId();
                if (!userId.HasValue)
                {
                    return Unauthorized("User not authenticated");
                }

                var excelBytes = await _analyticsService.ExportUserPropertiesAnalyticsToExcelAsync(userId.Value, filter);
                return File(excelBytes, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "user_properties_analytics.xlsx");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error exporting user properties analytics");
                return StatusCode(500, "An error occurred while exporting user properties analytics");
            }
        }

        [HttpPost("property/{propertyId}/view")]
        [AllowAnonymous]
        public async Task<IActionResult> LogPropertyView(Guid propertyId, [FromBody] LogPropertyViewRequest request)
        {
            try
            {
                var viewerId = GetUserId();
                var ipAddress = HttpContext.Connection.RemoteIpAddress?.ToString() ?? "Unknown";
                var referrer = Request.Headers["Referer"].ToString();

                _logger.LogInformation("Logging property view for property {PropertyId} - ViewerId: {ViewerId}, IP: {IpAddress}",
                    propertyId, viewerId, ipAddress);

                var dto = new LogPropertyViewDto
                {
                    PropertyId = propertyId,
                    ViewerId = viewerId,
                    ViewerIp = ipAddress,
                    UserAgent = request.UserAgent ?? Request.Headers["User-Agent"].ToString(),
                    ReferrerUrl = referrer,
                    SessionId = request.SessionId,
                    DeviceId = request.DeviceId,
                    DeviceType = request.DeviceType,
                    Platform = request.Platform,
                    Browser = request.Browser,
                    City = null, // Geo-location temporarily set to null
                    Region = null,
                    Country = null
                };

                await _analyticsService.LogPropertyViewAsync(dto);

                _logger.LogInformation("Successfully logged property view for property {PropertyId}", propertyId);
                return Ok();
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error logging property view for property {PropertyId}", propertyId);
                return StatusCode(500, "An error occurred while logging property view");
            }
        }

        /// <summary>
        /// Get the status history of a property
        /// </summary>
        /// <param name="propertyId">The ID of the property</param>
        /// <returns>List of property status history entries</returns>
        /// <response code="200">Returns the property history</response>
        /// <response code="401">If the user is not authenticated</response>
        [HttpGet("history/status/{propertyId}")]
        [Authorize(Policy = "UserExists")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        public async Task<ActionResult<IEnumerable<PropertyStatusLogDto>>> GetPropertyHistoryStatus(Guid propertyId)
        {
            try
            {
                var history = await _propertyService.GetPropertyHistoryStatus(propertyId);
                return Ok(history);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting property history for property {PropertyId}", propertyId);
                return StatusCode(500, "An error occurred while retrieving property history");
            }
        }

        /// <summary>
        /// Log property engagement event (matches frontend /api/log/property-event route)
        /// </summary>
        [HttpPost("/api/log/property-event")]
        [ServiceFilter(typeof(EnforceFrontendOriginAttribute))]
        [AllowAnonymous]
        public async Task<IActionResult> LogPropertyEvent([FromBody] LogPropertyEventRequest request)
        {
            try
            {
                // Model validation handles event type validation via ValidPropertyEngagementEventType attribute
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                if (!Guid.TryParse(request.PropertyId, out var propertyId))
                {
                    return BadRequest("Invalid property ID format");
                }

                var userId = GetUserId();
                var ipAddress = HttpContext.Connection.RemoteIpAddress?.ToString() ?? "Unknown";

                _logger.LogInformation("Logging property engagement event {EventType} for property {PropertyId} - UserId: {UserId}",
                    request.EventType, propertyId, userId);

                var dto = new LogPropertyEngagementEventDto
                {
                    PropertyId = propertyId,
                    UserId = userId,
                    EventType = request.EventType,
                    SessionId = request.SessionId,
                    DeviceId = request.DeviceId,
                    UserAgent = request.UserAgent ?? Request.Headers["User-Agent"].ToString(),
                    IpAddress = ipAddress,
                    DeviceType = request.DeviceType,
                    Platform = request.Platform,
                    Browser = request.Browser,
                    City = null, // Geo-location temporarily set to null
                    Region = null,
                    Country = null
                };

                await _analyticsService.LogPropertyEngagementEventAsync(dto);

                _logger.LogInformation("Successfully logged property engagement event {EventType} for property {PropertyId}",
                    request.EventType, propertyId);
                return Ok();
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error logging property engagement event {EventType} for property {PropertyId}",
                    request.EventType, request.PropertyId);
                return StatusCode(500, "An error occurred while logging property engagement event");
            }
        }

        /// <summary>
        /// Log property view (matches frontend /api/log/property-view route)
        /// </summary>
        [HttpPost("/api/log/property-view")]
        [ServiceFilter(typeof(EnforceFrontendOriginAttribute))]
        [AllowAnonymous]
        public async Task<IActionResult> LogPropertyViewFromFrontend([FromBody] LogPropertyViewRequest request)
        {
            try
            {
                if (!Guid.TryParse(request.PropertyId, out var propertyId))
                {
                    return BadRequest("Invalid property ID format");
                }

                var viewerId = GetUserId();
                var ipAddress = HttpContext.Connection.RemoteIpAddress?.ToString() ?? "Unknown";
                var referrer = Request.Headers["Referer"].ToString();

                _logger.LogInformation("Logging property view for property {PropertyId} - ViewerId: {ViewerId}, IP: {IpAddress}",
                    propertyId, viewerId, ipAddress);

                var dto = new LogPropertyViewDto
                {
                    PropertyId = propertyId,
                    ViewerId = viewerId,
                    ViewerIp = ipAddress,
                    UserAgent = request.UserAgent ?? Request.Headers["User-Agent"].ToString(),
                    ReferrerUrl = referrer,
                    SessionId = request.SessionId,
                    DeviceId = request.DeviceId,
                    DeviceType = request.DeviceType,                    
                    Platform = request.Platform,
                    Browser = request.Browser,
                    City = null, // Geo-location temporarily set to null
                    Region = null,
                    Country = null
                };

                await _analyticsService.LogPropertyViewAsync(dto);

                _logger.LogInformation("Successfully logged property view for property {PropertyId}", propertyId);
                return Ok();
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error logging property view for property {PropertyId}", request.PropertyId);
                return StatusCode(500, "An error occurred while logging property view");
            }
        }

        /// <summary>
        /// Log property spending
        /// </summary>
        [HttpPost("spending")]
        [Authorize]
        public async Task<IActionResult> LogPropertySpending([FromBody] LogPropertySpendingRequest request)
        {
            try
            {
                var userId = GetUserId();
                if (!userId.HasValue)
                {
                    return Unauthorized("User not authenticated");
                }

                _logger.LogInformation("Logging property spending for property {PropertyId} - UserId: {UserId}, Amount: {Amount}, Type: {SpendingType}",
                    request.PropertyId, userId.Value, request.Amount, request.SpendingType);

                var dto = new LogPropertySpendingDto
                {
                    PropertyId = request.PropertyId,
                    UserId = userId.Value,
                    Amount = request.Amount,
                    SpendingType = request.SpendingType,
                    TransactionId = request.TransactionId,
                    Details = request.Details
                };

                await _analyticsService.LogPropertySpendingAsync(dto);

                _logger.LogInformation("Successfully logged property spending for property {PropertyId}", request.PropertyId);
                return Ok();
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error logging property spending for property {PropertyId}", request.PropertyId);
                return StatusCode(500, "An error occurred while logging property spending");
            }
        }
    }
}
