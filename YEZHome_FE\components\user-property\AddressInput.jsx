"use client";

import { useState, useCallback, useRef, useEffect } from "react";
import { FormField, FormItem, FormLabel, FormControl, FormMessage } from "@/components/ui/form";
import { Command, CommandGroup, CommandInput, CommandItem, CommandList, CommandSeparator } from "@/components/ui/command";
import { MapPin } from "lucide-react";
import { HelpCircle } from "lucide-react";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { useTranslations } from "next-intl";
import AddressConflictDialog from "./AddressConflictDialog";

const AddressInput = ({
  form,
  isFormDisabled,
  selectedCity,
  selectedDistrict,
  selectedWard,
  onAddressSelect,
  onManualAddressClick,
  locationSelectorRef,
}) => {
  const t = useTranslations("PropertyForm");

  // Initialize with the form value or empty string
  const [inputValue, setInputValue] = useState(form.getValues("address") || "");
  const [predictions, setPredictions] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const timeoutRef = useRef(null);

  // State for location conflict dialog
  const [showConflictDialog, setShowConflictDialog] = useState(false);
  const [conflictData, setConflictData] = useState(null);
  const [pendingPrediction, setPendingPrediction] = useState(null);



  // Update inputValue when form value changes
  useEffect(() => {
    const subscription = form.watch((value, { name }) => {
      if (name === "address") {
        setInputValue(value.address || "");
      }
    });

    return () => subscription.unsubscribe();
  }, [form]);

  // Efficient debounced fetch implementation
  const fetchAddressSuggestions = useCallback(
    async (query) => {
      if (!query) {
        setPredictions([]);
        return;
      }

      setIsLoading(true);

      const locationParts = [query];

      // Get location data from LocationSelector component
      const locationData = locationSelectorRef?.current || {};
      const { cities = [], districts = [], wards = [] } = locationData;

      const cityName = cities.find((c) => c.id.toString() === selectedCity)?.name;
      if (cityName) locationParts.push(cityName);

      const districtName = districts.find((d) => d.id.toString() === selectedDistrict)?.name;
      if (districtName) locationParts.push(districtName);

      const wardName = wards.find((w) => w.id.toString() === selectedWard)?.name;
      if (wardName) locationParts.push(wardName);

      const fullQuery = encodeURIComponent(locationParts.join(", "));

      try {
        const response = await fetch(`/api/map/address-suggestions?input=${fullQuery}`);
        const data = await response.json();
        setPredictions(data.predictions ?? []);
      } catch (error) {
        console.error("Error fetching address suggestions:", error);
      } finally {
        setIsLoading(false);
      }
    },
    [selectedCity, selectedDistrict, selectedWard, locationSelectorRef]
  );

  // Debounce the input changes
  const handleInputChange = useCallback(
    (value) => {
      setInputValue(value);

      // Clear any existing timeout
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }

      // Set a new timeout
      timeoutRef.current = setTimeout(() => {
        fetchAddressSuggestions(value);
      }, 300); // 300ms debounce time
    },
    [fetchAddressSuggestions]
  );

  // Clean up timeout on unmount
  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  // Check for location conflicts between selected address and current dropdowns
  const checkLocationConflict = useCallback(
    async (place_id) => {
      try {
        // Get place details to extract administrative components
        const response = await fetch(`/api/map/place-detail?place_id=${place_id}`);
        const data = await response.json();

        if (!data.result || !data.result.compound) {
          return null;
        }

        // Extract city, district, ward from compound object
        const { compound } = data.result;

        // Get province (city), district, and commune (ward) from compound
        const cityName = compound.province;
        const districtName = compound.district;
        const wardName = compound.commune;

        // Store these for potential future use (district/ward matching)
        const addressLocation = {
          city: cityName,
          district: districtName,
          ward: wardName,
        };

        // If no city found, can't compare
        if (!cityName) return null;

        // Get location data from LocationSelector component
        const locationData = locationSelectorRef?.current || {};
        const { cities = [] } = locationData;

        // Check if city matches
        const selectedCityObj = cities.find((city) => city.id.toString() === selectedCity);

        // If no city is selected in dropdown, no conflict
        if (!selectedCity || !selectedCityObj) return null;

        // Check if the city name from address matches the selected city
        const cityMatches =
          selectedCityObj.name.toLowerCase().includes(cityName.toLowerCase()) || cityName.toLowerCase().includes(selectedCityObj.name.toLowerCase());

        // If city doesn't match, we have a conflict
        if (!cityMatches) {
          return {
            currentLocation: {
              id: selectedCityObj.id,
              name: selectedCityObj.nameWithType || selectedCityObj.name,
            },
            newLocation: {
              name: cityName,
              // Try to find matching city in our list
              matchingCity: cities.find(
                (city) => city.name.toLowerCase().includes(cityName.toLowerCase()) || cityName.toLowerCase().includes(city.name.toLowerCase())
              ),
              addressLocation, // Include full location data for future use
            },
            placeDetails: data.result,
          };
        }

        return null; // No conflict
      } catch (error) {
        console.error("Error checking location conflict:", error);
        return null;
      }
    },
    [selectedCity, locationSelectorRef]
  );

  // Handle location conflict resolution
  const handleConfirmLocationChange = useCallback(async () => {
    if (!conflictData || !pendingPrediction) return;

    // Find the matching city in our list (optional, parent component can handle this)
    const newCity = conflictData?.newLocation?.matchingCity; // Pass resolved location details back to parent component

    if (onAddressSelect && pendingPrediction && conflictData?.newLocation?.addressLocation) {
      const description = pendingPrediction.description || "";
      onAddressSelect(
        pendingPrediction.place_id,
        { ...pendingPrediction, description },
        {
          city: conflictData.newLocation.addressLocation.city,
          district: conflictData.newLocation.addressLocation.district,
          ward: conflictData.newLocation.addressLocation.ward,
          newCityId: newCity?.id,
        }
      );
    }

    // Reset state
    setShowConflictDialog(false);
    setConflictData(null);
    setPendingPrediction(null);
  }, [conflictData, pendingPrediction, form, onAddressSelect]);

  // Handle keeping current location
  const handleKeepCurrentLocation = useCallback(() => {
    // Clear the input and predictions
    setInputValue("");
    setPredictions([]);

    // Reset state
    setShowConflictDialog(false);
    setConflictData(null);
    setPendingPrediction(null);
  }, []);

  // Handle address selection
  const handleSelectAddress = useCallback(
    async (place_id) => {
      const selectedPrediction = predictions.find((item) => item.place_id === place_id);
      if (!selectedPrediction) return;

      // Check for location conflicts
      const conflict = await checkLocationConflict(place_id);

      if (conflict) {
        // Store the prediction for later use
        setPendingPrediction(selectedPrediction);
        setConflictData(conflict);
        setShowConflictDialog(true);
      } else {
        // No conflict, proceed normally
        if (onAddressSelect) {
          // Make sure we're passing a valid description
          const description = selectedPrediction.description || "";
          onAddressSelect(place_id, { ...selectedPrediction, description });
        }
      }

      setPredictions([]);
    },
    [predictions, onAddressSelect, checkLocationConflict]
  );

  return (
    <>
      <FormField
        control={form.control}
        name="address"
        render={({ field }) => (
          <FormItem className="flex-1">
            <FormLabel className="flex gap-3">
              {t("address")}
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <HelpCircle className="h-4 w-4 text-gray-500 cursor-help" />
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>{t("addressDescription")}</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </FormLabel>
            <FormControl>
              <Command shouldFilter={false} className="border rounded-md">
                <CommandInput
                  value={inputValue}
                  placeholder={t("addressPlaceholder")}
                  onValueChange={handleInputChange}
                  disabled={!selectedWard || isFormDisabled}
                  className={isLoading ? "opacity-70" : ""}
                  onBlur={() => {
                    field.onBlur();
                    // Update form value when input loses focus
                    form.setValue("address", inputValue);
                  }}
                />
                <CommandList>
                  {!isFormDisabled && predictions.length > 0 && (
                    <>
                      <CommandGroup>
                        {predictions.map((prediction) => (
                          <CommandItem
                            key={prediction.place_id}
                            value={prediction.place_id}
                            onSelect={handleSelectAddress}
                            className="flex items-center gap-2"
                          >
                            <MapPin className="h-4 w-4 text-gray-400" />
                            {prediction.description}
                          </CommandItem>
                        ))}
                      </CommandGroup>
                      <CommandSeparator />
                      <CommandGroup>
                        <CommandItem onSelect={onManualAddressClick} className="text-teal-600 font-medium hover:text-teal-700 hover:bg-teal-50">
                          {t("enterAddressManually")}
                        </CommandItem>
                      </CommandGroup>
                    </>
                  )}
                </CommandList>
              </Command>
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      {/* Location conflict dialog */}
      <AddressConflictDialog
        open={showConflictDialog}
        onOpenChange={setShowConflictDialog}
        currentLocation={conflictData?.currentLocation}
        newLocation={conflictData?.newLocation}
        onConfirmChange={handleConfirmLocationChange}
        onKeepCurrent={handleKeepCurrentLocation}
      />
    </>
  );
};

export default AddressInput;
