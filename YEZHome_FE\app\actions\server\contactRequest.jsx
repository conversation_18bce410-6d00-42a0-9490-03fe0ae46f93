"use server";

import { handleErrorResponse, logError } from "@/lib/apiUtils";
import { fetchWithAuth, fetchWithoutAuth, getSession } from "@/lib/sessionUtils";
import { parseEmptyStringsToNull } from "@/lib/utils";

const API_BASE_URL = `${process.env.API_URL}/api/ContactRequest`;

/**
 * Get all contact requests for a specific property
 * @param {string} propertyId - The ID of the property
 */
export async function getContactRequestsByPropertyId(propertyId) {
  try {
    const response = await fetchWithAuth(`${API_BASE_URL}/property/${propertyId}`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
    });

    return {
      success: true,
      data: response.data,
    };
  } catch (error) {
    logError("ContactRequestService", error, {
      action: "getContactRequestsByPropertyId",
      propertyId,
    });
    return handleErrorResponse(false, null, "Đã xảy ra lỗi khi lấy danh sách yêu cầu liên hệ");
  }
}

/**
 * Get a specific contact request by ID
 * @param {string} id - The ID of the contact request
 */
export async function getContactRequestById(id) {
  try {
    const response = await fetchWithAuth(`${API_BASE_URL}/${id}`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
    });

    if (!response.success) {
      return {
        success: false,
        message: response.message || "Không thể lấy thông tin yêu cầu liên hệ",
      };
    }

    return {
      success: true,
      data: response.data,
    };
  } catch (error) {
    logError("ContactRequestService", error, {
      action: "getContactRequestById",
      id,
    });
    return handleErrorResponse(false, null, "Đã xảy ra lỗi khi lấy thông tin yêu cầu liên hệ");
  }
}

/**
 * Create a new contact request
 * @param {Object} prevState - Previous state
 * @param {FormData} formData - Form data containing contact request details
 */
export async function createContactRequest(prevState, formData) {
  try {
    // Convert FormData to a plain object for easier handling
    const formDataObject = Object.fromEntries(formData.entries());

    const payload = {
      ...formDataObject,
    };

    // Get the current user's ID if they're logged in
    const userSession = await getSession("User");
    if (userSession) {
      const user = JSON.parse(userSession);
      payload.userId = user.id;
    }

    return await fetchWithoutAuth(API_BASE_URL, {
      method: "POST",
      body: JSON.stringify(parseEmptyStringsToNull(payload)),
      headers: {
        "Content-Type": "application/json",
      },
    });
  } catch (error) {
    logError("ContactRequestService", error, {
      action: "createContactRequest",
      formData,
    });
    return handleErrorResponse(false, null, "Đã xảy ra lỗi khi tạo yêu cầu liên hệ");
  }
}

/**
 * Update a contact request
 * @param {Object} prevState - Previous state
 * @param {FormData} formData - Form data containing updated contact request details
 */
export async function updateContactRequest(prevState, formData) {
  try {
    const id = formData.get("id");
    if (!id) {
      return handleErrorResponse(false, null, "ID yêu cầu liên hệ không hợp lệ");
    }

    // Convert FormData to a plain object for easier handling
    const formDataObject = Object.fromEntries(formData.entries());

    const payload = {
      ...formDataObject,
    };

    return await fetchWithAuth(`${API_BASE_URL}/${id}`, {
      method: "PUT",
      body: JSON.stringify(parseEmptyStringsToNull(payload)),
      headers: {
        "Content-Type": "application/json",
      },
    });
  } catch (error) {
    logError("ContactRequestService", error, {
      action: "updateContactRequest",
      formData,
    });
    return handleErrorResponse(false, null, "Đã xảy ra lỗi khi cập nhật yêu cầu liên hệ");
  }
}

/**
 * Delete a contact request
 * @param {string} id - The ID of the contact request to delete
 */
export async function deleteContactRequest(id) {
  try {
    return await fetchWithAuth(`${API_BASE_URL}/${id}`, {
      method: "DELETE",
      headers: {
        "Content-Type": "application/json",
      },
    });
  } catch (error) {
    logError("ContactRequestService", error, {
      action: "deleteContactRequest",
      id,
    });
    return handleErrorResponse(false, null, "Đã xảy ra lỗi khi xóa yêu cầu liên hệ");
  }
} 