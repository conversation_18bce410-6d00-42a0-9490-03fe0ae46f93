# Technical Design Document: Homepage Feature

## 1. Overview

This document outlines the technical design of the YEZHome homepage feature, located primarily within `app/[locale]/page.jsx`. The homepage serves as the central hub for users to discover real estate properties through an integrated interface. It combines a **Search Filter** section, an interactive **Map Section** showing property locations, and a **Property List** section displaying results with pagination and favoriting. A **Property Detail Modal** provides in-depth information upon selection. The feature aims to provide an efficient and intuitive property browsing experience, leveraging server-side data fetching and client-side interactivity, including URL updates reflecting the currently viewed property detail and visual highlighting on the map.

## 2. Requirements

### 2.1 Functional Requirements

*   **FR1:** Users must be able to filter properties using the `SearchFilterComponent` based on:
    *   Transaction Type (`postType`: sell, rent)
    *   Property Type (`propertyType`)
    *   Location (Province `cityId`, District `districtId`, Address `address`)
    *   Price Range (`minPrice`, `maxPrice`)
    *   Area Range (`minArea`, `maxArea`)
    *   Bedrooms (`minRooms`)
    *   Bathrooms (`minToilets`)
    *   Direction (`direction`)
    *   Legal Status (`legality`)
    *   Road Width (`minRoadWidth`)
*   **FR2:** Filtered properties retrieved via the `searchProperties` action must be displayed simultaneously as markers on the `MapSectionComponent` and as cards in the `PropertyListComponent`.
*   **FR3:** The application URL must update dynamically to reflect the currently applied filters using query parameters, enabling bookmarking and sharing of searches. (`filterCriteriaToUrlParams` / `parseUrlToFilterCriteria` handle this).
*   **FR4:** The `PropertyListComponent` must implement pagination (`Pagination` component) based on the data provided (`paginationInfo` from `searchProperties`), allowing users to navigate through result pages.
*   **FR5:** Clicking a `PropertyCard` in the `PropertyListComponent` must:
    *   Trigger the `handlePropertySelect` handler.
    *   Update the `selectedPropertyForModal` state to display the `PropertyDetailModal` with the selected property's details.
    *   Update the `selectedProperty` state.
    *   If the property has valid coordinates (`latitude`, `longitude`), update the `mapCenter` state to focus the `MapSectionComponent` on that location.
    *   Update the browser URL to include the current filter parameters *and* the selected property's ID (e.g., `?cityId=...&propertyId=123`).
*   **FR5.1:** When the `PropertyDetailModal` is closed (via its close button or other means), the `propertyId` query parameter must be removed from the URL, restoring the URL to only reflect the active filter parameters.
*   **FR6:** Logged-in users must be able to add/remove properties from their favorites using the heart icon button on each `PropertyCard`. This interacts with `addToFavorites` and `removeFromFavorites` server actions. The favorite status should be visually indicated on the card (`favorite` state in `PropertyCard`). Initial status is fetched via `checkFavoriteStatus`.
*   **FR7:** The `MapSectionComponent` should initially attempt to center on the user's current geolocation (`navigator.geolocation`). If permission is denied or the API is unavailable, it should default to `HCM_COORDINATES_DISTRICT_2`. The detected `userLocation` state is used for map centering and potentially included in search criteria.
*   **FR8:** Loading indicators (`Loader2` icon) must be displayed within the `PropertyListComponent` while data is being fetched via `searchProperties` (`loading` state). Component-level loading states (using `dynamic`) are shown during initial component load.
*   **FR9:** Users must receive visual feedback via toast notifications (`useToast`) for:
    *   Successfully adding/removing favorites.
    *   Errors during the favoriting process.
    *   Errors encountered while fetching property data (`searchProperties`).
    *   Attempting to favorite when not logged in.
*   **FR10:** When a property is selected (and has valid coordinates), its corresponding marker on the `MapSectionComponent` must be visually differentiated from other markers (e.g., change color to green, increase size) to indicate selection.

### 2.2 Non-Functional Requirements

*   **NFR1:** **Performance:** The initial page load should be fast. Subsequent searches and pagination should feel responsive. Achieved through:
    *   Dynamic imports (`dynamic`) for major components (`SearchFilter`, `MapSection`, `PropertyList`).
    *   `next/image` for optimized image loading in `PropertyCard`.
    *   Memoization (`React.memo`) for `PropertyCard` and `PropertyList`.
    *   Server Actions (`searchProperties`) for efficient data fetching.
*   **NFR2:** **Responsiveness:** The layout must adapt gracefully to various screen sizes (mobile, tablet, desktop) using Tailwind CSS utility classes (e.g., `flex-col md:flex-row`, `grid-cols-1 sm:grid-cols-2`).
*   **NFR3:** **Security:** User-specific actions (favoriting) must be protected, relying on server-side authentication checks within the relevant server actions. Filter inputs processed server-side should be sanitized.
*   **NFR4:** **Scalability:** The frontend should handle potentially large numbers of properties through pagination. Backend search (`searchProperties`) must be optimized for performance.
*   **NFR5:** **Maintainability:** Code should be well-structured, utilizing components, hooks, and helper functions (`parseUrlToFilterCriteria`, `filterCriteriaToUrlParams`). State management logic should be clear (`useState`, `useCallback`).

## 3. Technical Design

### 3.1. Data Model Changes

*   **Backend Interaction:** This feature primarily *consumes* data from the backend via the `searchProperties` server action. The expected structure of a property object returned by this action is crucial and includes fields like: `id`, `name`, `price`, `postType`, `address`, `addressSelected`, `area`, `rooms`, `toilets`, `latitude`, `longitude`, `propertyMedia` (array of objects with `mediaURL`), etc. The action also returns pagination metadata (`totalCount`, `pageCount`, `currentPage`, `pageSize`, `hasNextPage`, `hasPreviousPage`).
*   **Client-Side State:**
    *   **`Home` Component (`page.jsx`):** Manages the core state:
        *   `properties`: Array of all property objects (potentially redundant if `filteredProperties` is always the source of truth).
        *   `filteredProperties`: Array of property objects currently displayed, returned by `searchProperties`.
        *   `loading`: Boolean indicating if `searchProperties` is in progress.
        *   `paginationInfo`: Object containing pagination metadata.
        *   `filterCriteria`: Object holding the current user-defined filters, including pagination state (`page`, `pageSize`).
        *   `selectedProperty`: The property object currently selected (influences map center).
        *   `selectedPropertyForModal`: The property object to display in the modal.
        *   `mapCenter`: Object `{lat, lng}` for the map's center coordinates.
        *   `userLocation`: Object `{lat, lng}` if geolocation is successful.
        *   `isInitialLoad`: Boolean flag to manage initial URL parsing.
    *   **`PropertyList` Component:** Manages favorite status for displayed items:
        *   `favorites`: Object mapping `propertyId` to `true` if favorited by the current user. Used to display the correct state on `PropertyCard`s.

*   **Client-State Interaction Diagram (Favorites in PropertyList):**

```mermaid
stateDiagram-v2
    [*] --> NotLoggedIn
    NotLoggedIn --> LoggedIn: User logs in
    LoggedIn --> Idle: Component Mounts / Properties Load

    state LoggedIn {
        Idle --> FetchingStatus: properties change
        FetchingStatus --> Idle: checkFavoriteStatus() success (updates favorites map)
        FetchingStatus --> Idle: checkFavoriteStatus() error
        Idle --> UpdatingFavorite: User clicks favorite button
        UpdatingFavorite --> Idle: Favorite action success (updates favorites map)
        UpdatingFavorite --> Idle: Favorite action error
    }

    LoggedIn --> NotLoggedIn: User logs out
```

### 3.2. UI Changes

*   **`app/[locale]/page.jsx`:** Orchestrates the main layout (`flex flex-col min-h-screen`, `flex flex-1 flex-col md:flex-row`). Dynamically renders:
    *   `SearchFilterComponent`: At the top, receives `onFilterChange`, `initialFilters`.
    *   `MapSectionComponent`: Occupies the main area (left on desktop), receives `properties`, `selectedProperty`, `center`, `userLocation`. Markers within this component will change appearance (e.g., color, size) based on the `selectedProperty` prop if it matches a marker's property and has coordinates.
    *   `PropertyListComponent`: Occupies the sidebar area (right on desktop), receives `properties`, `loading`, `onPropertySelect`, `pagination`, `onPageChange`, `isLoggedIn` (prop needs adding to `Home` component).
    *   `PropertyDetailModal`: Rendered conditionally based on `selectedPropertyForModal`, receives `property`, `onClose`.
*   **`components/property/PropertyList.jsx`:**
    *   Displays loading (`Loader2`) or "No results" message.
    *   Renders search result count and pagination info.
    *   Uses CSS Grid (`grid grid-cols-1 sm:grid-cols-2 gap-4`) to layout `PropertyCard` components.
    *   Renders the `Pagination` component when `pageCount > 1`.
*   **`components/property/PropertyCard.jsx`:**
    *   Displays property image (`next/image`), type badge, favorite button (Heart icon), name, price, address (`MapPin`), core stats (area `Square`, bedrooms `BedDouble`, toilets `Bath`).
    *   Favorite button style changes based on `favorite` state and shows loading pulse (`isLoading`).
    *   Includes a "Chi tiết" (`Details`) button.
*   **`components/property/Pagination.jsx`:**
    *   Renders previous/next buttons (`ChevronLeft`, `ChevronRight`) and numbered page buttons.
    *   Disables buttons based on `hasPreviousPage`/`hasNextPage`.
    *   Highlights the current page button. Calculates which page numbers to display (`getPageNumbers`).

### 3.3. Logic Flow

*(Sequence diagrams from previous response are still relevant here)*

1.  **Initialization & URL Parsing:** `Home` mounts -> `useEffect` gets location -> `useEffect` parses `searchParams` into `filterCriteria` (only on initial load controlled by `isInitialLoad`). If `propertyId` is present in the initial URL, potentially attempt to fetch and display that property detail directly (TBD if needed).
2.  **Data Fetching:** `useEffect` monitors `filterCriteria`, `userLocation`, `isInitialLoad`. When appropriate, calls `searchProperties(searchCriteria)`. Handles success (updates `filteredProperties`, `paginationInfo`), error (toast message), and loading states.
3.  **Filter Application:** `SearchFilterComponent` calls `handleFilterChange` -> `Home` updates `filterCriteria` (resets page to 1) -> triggers data fetch effect -> updates URL via `useEffect` monitoring `filterCriteria`.
4.  **Pagination:** `Pagination` calls `onPageChange` -> `PropertyListComponent` calls `handlePageChange` -> `Home` updates `filterCriteria.page` -> triggers data fetch effect -> updates URL (preserving filters).
5.  **Property Selection/Modal:**
    *   `PropertyCard` calls `onPropertySelect(property)`.
    *   `Home` component's `handlePropertySelect(property)` executes:
        *   Updates `selectedProperty` state to `property`.
        *   Updates `selectedPropertyForModal` state to `property`.
        *   Updates `mapCenter` state if `property.latitude` and `property.longitude` exist.
        *   Constructs new URL parameters including all current `filterCriteria` *plus* `propertyId=property.id`.
        *   Uses `router.push` to update the browser URL with the new parameters (reflecting filters + selected property).
    *   `PropertyDetailModal` renders based on `selectedPropertyForModal`.
    *   `MapSectionComponent` re-renders, using the updated `selectedProperty` prop to highlight the corresponding marker.
6.  **Modal Closure:**
    *   User interaction closes `PropertyDetailModal`.
    *   The modal's `onClose` handler is called.
    *   In `Home` component (handler passed to modal's `onClose` prop):
        *   Updates `selectedPropertyForModal` state to `null`.
        *   Updates `selectedProperty` state to `null` (or keeps it if map selection should persist briefly - TBD).
        *   Constructs URL parameters based *only* on the current `filterCriteria` (omitting `propertyId`).
        *   Uses `router.push` to update the browser URL, restoring the filter-only state.
7.  **Favoriting Flow:** See sequence diagram in the previous response. Key steps: check login -> call server action -> update local state (`PropertyCard`, `PropertyList`) -> show toast -> dispatch global event. Initial state fetched via `checkFavoriteStatus` effect in `PropertyList`.

### 3.4. API / Server Action Interactions

*   **`searchProperties(criteria)`:**
    *   **Called from:** `Home` component (`page.jsx`).
    *   **Purpose:** Fetch a paginated list of properties based on filter criteria (including location, price, type, etc., and optional user location/radius).
    *   **Expected Input:** An object matching the `filterCriteria` state structure, potentially augmented with `userLocation` and `radius`.
    *   **Expected Output (Success):** `{ success: true, data: { items: Property[], totalCount: number, pageCount: number, currentPage: number, pageSize: number, hasNextPage: boolean, hasPreviousPage: boolean } }`
    *   **Expected Output (Error):** `{ success: false, error: string }`
*   **`checkFavoriteStatus(propertyIds)`:**
    *   **Called from:** `PropertyList` component.
    *   **Purpose:** Determine which properties in a given list are favorited by the currently logged-in user.
    *   **Expected Input:** An array of property IDs.
    *   **Expected Output (Success):** `{ success: true, data: [{ propertyId: string }] }` (Array contains objects for favorited properties)
    *   **Expected Output (Error):** `{ success: false, message?: string }`
*   **`addToFavorites(propertyId)`:**
    *   **Called from:** `PropertyCard` component (via `handleFavoriteClick`).
    *   **Purpose:** Add a specific property to the current user's favorites.
    *   **Expected Input:** A single property ID string.
    *   **Expected Output (Success):** `{ success: true }`
    *   **Expected Output (Error):** `{ success: false, message?: string }`
*   **`removeFromFavorites(propertyId)`:**
    *   **Called from:** `PropertyCard` component (via `handleFavoriteClick`).
    *   **Purpose:** Remove a specific property from the current user's favorites.
    *   **Expected Input:** A single property ID string.
    *   **Expected Output (Success):** `{ success: true }`
    *   **Expected Output (Error):** `{ success: false, message?: string }`

### 3.6. Security Considerations

*   **Server-Side Validation:** The `searchProperties` action *must* validate and sanitize all filter inputs server-side to prevent NoSQL injection, XSS via address search, or other parameter-based attacks.
*   **Authentication:** All favorite-related server actions (`checkFavoriteStatus`, `addToFavorites`, `removeFromFavorites`) *must* verify that the user is authenticated before performing any operation. Check should happen server-side within the action.
*   **Authorization:** Ensure users can only modify their *own* favorites list.
*   **Rate Limiting:** Consider applying rate limiting to the `searchProperties` endpoint and favorite actions to prevent abuse.
*   **Data Exposure:** Ensure the `searchProperties` action only returns necessary, non-sensitive property data suitable for public display.

### 3.7. Performance Considerations

*   **Bundle Size:** Dynamic imports significantly help. Monitor bundle size using tools like `@next/bundle-analyzer`.
*   **Data Fetching:** `searchProperties` needs to be efficient on the backend (database indexing, query optimization). Pagination limits client-side data load.
*   **Rendering:**
    *   `React.memo` usage helps (`PropertyCard`, `PropertyList`).
    *   Minimize prop drilling where possible (State management libraries like Zustand could help if state becomes more complex).
    *   `useCallback` is used for handlers passed down to memoized components to prevent unnecessary re-renders due to function reference changes.
*   **Map Performance:** Ensure the map library (`MapSectionComponent`) efficiently handles potentially large numbers of markers. Consider marker clustering if needed for high densities. Marker style updates on selection should be performant.
*   **Image Loading:** `next/image` optimizes images. The custom `IntersectionObserver` in `PropertyList` might be redundant or conflict; review its necessity.

## 4. Testing Plan

*   **Unit Tests (Jest/React Testing Library):**
    *   `parseUrlToFilterCriteria`: Test various URL query strings map correctly to filter objects.
    *   `filterCriteriaToUrlParams`: Test filter objects map correctly to URL query strings.
    *   `Pagination`: Test `getPageNumbers` logic, button rendering, disabled states, `onClick` handler calls.
    *   `PropertyCard`: Test rendering with different property data, favorite states (`isFavorite`, `isLoading`), button clicks (`onClick`, `handleFavoriteClick` mock). Verify login check prevents favorite action call.
    *   `PropertyList`: Test rendering with loading state, empty state, list of properties. Mock `checkFavoriteStatus` and test `favorites` state update. Test `handleToggleFavorite` updates state and dispatches event.
    *   `Home` component: Mock server actions (`searchProperties`). Test initial load logic (URL parsing, geolocation mock). Test state updates on `handleFilterChange`, `handlePageChange`, `handlePropertySelect`. Test URL updates via mocked `useRouter` for both filter changes *and* modal open/close scenarios (checking for `propertyId` presence/absence).
*   **Integration Tests (React Testing Library):**
    *   Test `Home` page interaction: Simulate filter changes in `SearchFilterComponent`, verify `searchProperties` mock is called with correct criteria, verify `PropertyListComponent` and `MapSectionComponent` receive updated props.
    *   Test Property Selection Flow: Click `PropertyCard`, verify modal opens, verify URL includes `propertyId`, verify `MapSectionComponent` receives updated `selectedProperty` prop (mock map component to check). Close modal, verify URL reverts, verify modal closes.
    *   Test Pagination flow: Click pagination buttons, verify `searchProperties` mock called with new page, verify list updates.
    *   Test Favorite flow: Click favorite button on `PropertyCard`, mock server actions (`checkFavoriteStatus`, `addToFavorites`/`removeFromFavorites`), verify UI updates (icon, toast), verify global event dispatch.
*   **End-to-End Tests (Cypress/Playwright):**
    *   Full user journey: Load page -> Apply multiple filters -> Verify URL updates -> Verify map and list show expected results -> Navigate pagination -> Select a property -> Verify modal opens *and* URL contains `propertyId` -> Verify selected marker on map is highlighted (requires visual testing or specific DOM checks) -> Close modal -> Verify URL removes `propertyId` but keeps filters -> Favorite/unfavorite a property -> Verify persistent state (if possible/applicable). Test responsive layouts.

## 5. Open Questions

*   **`isLoggedIn` Prop:** The `PropertyList` component expects an `isLoggedIn` prop, but it doesn't seem to be passed down from the `Home` component in the provided `page.jsx`. This needs to be added, likely determined using a session/auth hook/context in `Home`.
*   **Map Marker Interaction:** Now defined: clicking a list item selects and highlights the marker. Should clicking a marker *also* select the property, open the modal, and potentially scroll the list item into view? This interaction needs definition if desired.
*   **Redundant State:** Is the `properties` state variable in `Home` truly necessary if `filteredProperties` holds the data used for rendering? Can it be removed?
*   **Intersection Observer Utility:** Confirm if the `IntersectionObserver` in `PropertyList` provides benefits beyond `next/image` default behavior or if it can be removed/simplified.
*   **Initial Load with `propertyId`:** If a user directly navigates to a URL with `?propertyId=...`, should the application attempt to fetch this specific property and open the modal on load? Current logic focuses on parsing filters first.
*   **Map Selection Persistence:** When the modal closes, should the map marker remain highlighted (`selectedProperty` state remains set), or should it revert to the default state (`selectedProperty` becomes `null`)?

## 6. Alternatives Considered

*   **State Management:** Instead of prop drilling and `useState`, a dedicated state management library (Zustand, Redux Toolkit, Jotai) could centralize state like `filterCriteria`, `results`, `selectedProperty`, simplifying component logic, especially as features grow. This was likely considered and deferred for simplicity initially.
*   **Infinite Scrolling:** Instead of pagination, infinite scrolling could load more properties as the user scrolls down the `PropertyListComponent`. This offers a different UX but adds complexity in managing data loading triggers and scroll position.
