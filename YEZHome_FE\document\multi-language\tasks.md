# Multi-Language Feature Task Breakdown

Based on the technical design document `document/multi-language/multi-language.md`.

## Dependencies & Setup
- [x] Install `next-intl` package (`npm install next-intl`) (Completed)

## Project Structure & Configuration
- [x] Create `locales/en.json` for English translations. (Completed)
- [x] Create `locales/vi.json` for Vietnamese translations. (Completed)
- [x] Populate `locales/en.json` with initial keys and English text. (Completed)
- [x] Populate `locales/vi.json` with initial keys and Vietnamese text. (Completed)
- [x] Create `src/i18n.js` (or `i18n.js`) to configure `next-intl` (locales, message loading). (Completed)
- [x] Create/Modify `middleware.js` to integrate `next-intl` middleware for locale detection and redirection (default: `vi`). (Completed)
- [x] Rename `app/layout.jsx` to `app/[locale]/layout.jsx`. (Completed)
- [x] Update `app/[locale]/layout.jsx` to include the `[locale]` dynamic segment logic. (Completed)
- [x] Wrap the application layout in `app/[locale]/layout.jsx` with `NextIntlClientProvider`, passing messages fetched server-side. (Completed)

## UI Components
- [x] Create `components/ui/LanguageSwitcher.tsx`. (Completed)
  - [x] Implement dropdown using `shadcn/ui` DropdownMenu. (Completed)
  - [x] Display language options ("English", "Tiếng Việt"). (Completed)
  - [x] Implement `onClick` handler using `next-intl`'s `useRouter` and `usePathname` to navigate with the selected locale. (Completed)
- [x] Integrate `LanguageSwitcher` component into `components/layout/Navbar.jsx`. (Completed)
- [x] Refactor `components/layout/NoData.jsx`: Replace hardcoded text with `useTranslations`. (Completed)
- [x] Refactor `components/property/SearchFilter.jsx`: Replace hardcoded text with `useTranslations`. (Completed)
- [x] Refactor `app/page.jsx` (and other static pages/components): Replace hardcoded text with `useTranslations`. (Completed)
  - *Note: Identify all components/pages with static text requiring translation.*

## Testing
- [ ] Write unit tests for `LanguageSwitcher` component (Jest/RTL).
  - [ ] Verify correct rendering.
  - [ ] Verify navigation is triggered with the correct locale on selection.
- [ ] Write integration tests (e.g., using Cypress or Playwright).
  - [ ] Test middleware redirection for requests missing a locale (should redirect to `/vi`).
  - [ ] Test accessing URLs with explicit locales (`/en/page`, `/vi/page`) displays correct language.
  - [ ] Test the full language switching flow via the UI.
- [ ] Perform User Acceptance Testing (UAT).
  - [ ] Manually verify translations on all key pages in both `en` and `vi`.
  - [ ] Confirm `LanguageSwitcher` functionality across different pages.
  - [ ] Check URL updates correctly reflect the selected language.

## Documentation
- [ ] Add JSDoc comments to the `LanguageSwitcher` component.
- [ ] Add JSDoc comments to any new hooks or complex logic introduced.
- [ ] Update project README if necessary regarding i18n setup or usage. 