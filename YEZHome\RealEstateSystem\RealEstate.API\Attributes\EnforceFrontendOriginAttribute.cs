﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.Extensions.Options;

namespace RealEstate.API.Attributes
{
    public class AnalysisFromFrontEnd
    {
        public List<string> AllowedOrigins { get; set; } = new();
    }

    public class EnforceFrontendOriginAttribute : ActionFilterAttribute
    {
        private readonly HashSet<string> _allowedOrigins;

        public EnforceFrontendOriginAttribute(IOptions<AnalysisFromFrontEnd> options)
        {
            _allowedOrigins = options.Value.AllowedOrigins
                .Select(o => o.TrimEnd('/').ToLowerInvariant())
                .ToHashSet();
        }

        public override void OnActionExecuting(ActionExecutingContext context)
        {
            var request = context.HttpContext.Request;
            var origin = request.Headers["Origin"].FirstOrDefault();

            // Fallback to Referer if Origin is empty
            if (string.IsNullOrWhiteSpace(origin))
            {
                var referer = request.Headers["Referer"].FirstOrDefault();
                if (!string.IsNullOrWhiteSpace(referer))
                {
                    try
                    {
                        origin = new Uri(referer).GetLeftPart(UriPartial.Authority);
                    }
                    catch
                    {
                        // ignore invalid referer
                    }
                }
            }

            if (string.IsNullOrWhiteSpace(origin) || !_allowedOrigins.Contains(origin.TrimEnd('/').ToLowerInvariant()))
            {
                context.Result = new ForbidResult(); // 403
            }
        }
    }
}
