"use client";

import { useState, useEffect } from "react";
import { getUserTransactions } from "@/app/actions/server/user";
import { formatCurrency } from "@/lib/utils";
import { Card, CardContent } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { ArrowDown, ArrowUp, Clock, CheckCircle2, XCircle, Filter, Calendar } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Link } from "@/i18n/navigation";
import { useTranslations } from "next-intl";

// Function to format date (consider locale-aware formatting if needed)
const formatDate = (dateString, locale = "vi-VN") => {
  const date = new Date(dateString);
  return date.toLocaleDateString(locale);
};

// Status badge component - Now uses translations
const StatusBadge = ({ status, t }) => {
  switch (status) {
    case "completed":
      return (
        <Badge variant="outline" rounded="full" className="bg-green-100 text-green-800 hover:bg-green-100">
          <CheckCircle2 className="h-3 w-3 mr-1" />
          {t("statusCompleted")}
        </Badge>
      );
    case "pending":
      return (
        <Badge variant="outline" rounded="full" className="bg-amber-100 text-amber-800 hover:bg-amber-100">
          <Clock className="h-3 w-3 mr-1" />
          {t("statusPending")}
        </Badge>
      );
    case "failed":
      return (
        <Badge variant="outline" rounded="full" className="bg-red-100 text-red-800 hover:bg-red-100">
          <XCircle className="h-3 w-3 mr-1" />
          {t("statusFailed")}
        </Badge>
      );
    default:
      return (
        <Badge variant="outline" rounded="full" className="bg-gray-100 text-gray-800">
          {status} {/* Fallback for unknown statuses */}
        </Badge>
      );
  }
};

export default function TransactionsPage() {
  const t = useTranslations("UserTransactionsPage");
  const [transactions, setTransactions] = useState([]);
  const [loading, setLoading] = useState(true);
  const [filter, setFilter] = useState("all"); // all, deposit, payment
  const [timeframe, setTimeframe] = useState("all"); // all, month, year
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchTransactions = async () => {
      try {
        setLoading(true);
        const response = await getUserTransactions(100);

        if (response.success) {
          setTransactions(response.data);
        } else {
          setError(response.message || t("fetchError")); // Use translated error
        }
      } catch (err) {
        console.error(err);
        setError(t("genericFetchError")); // Use translated error
      } finally {
        setLoading(false);
      }
    };

    fetchTransactions();
  }, [t]); // Add t dependency

  // Filter transactions based on selected filters
  const filteredTransactions = transactions.filter((transaction) => {
    if (filter === "deposit" && transaction.type !== "topup") return false;
    if (filter === "payment" && transaction.type === "topup") return false;

    if (timeframe !== "all") {
      const now = new Date();
      const transactionDate = new Date(transaction.createdAt);
      if (timeframe === "month") {
        return (
          transactionDate.getMonth() === now.getMonth() &&
          transactionDate.getFullYear() === now.getFullYear()
        );
      }
      if (timeframe === "year") {
        return transactionDate.getFullYear() === now.getFullYear();
      }
    }
    return true;
  });

  // Calculate summary
  const summary = filteredTransactions.reduce(
    (acc, transaction) => {
      if (transaction.type === "topup" && transaction.status === "completed") {
        acc.totalIn += transaction.amount;
      } else if (transaction.type !== "topup" && transaction.status === "completed") {
        acc.totalOut += transaction.amount;
      }
      return acc;
    },
    { totalIn: 0, totalOut: 0 }
  );

  // Helper to get transaction type label
  const getTransactionTypeLabel = (type) => {
    switch (type) {
      case "topup":
        return t("transactionTypeDeposit");
      case "payment_highlight": // Example specific type
        return t("transactionTypeHighlight");
      case "payment_post": // Example specific type
        return t("transactionTypePost");
      default:
        return t("transactionTypePayment"); // Generic payment
    }
  };

  if (loading) {
    return (
      <div className="p-6">
        <div className="h-96 flex items-center justify-center">
          <div className="text-coral-500">{t("loadingMessage")}</div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-6">
        <div className="h-96 flex items-center justify-center">
          <div className="text-red-500">{error}</div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 max-w-6xl">
      <h1 className="text-2xl font-bold text-navy-blue mb-6">{t("pageTitle")}</h1>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <div className="text-sm text-gray-500">{t("totalDepositLabel")}</div>
                <div className="text-xl font-bold text-green-600">
                  {formatCurrency(summary.totalIn)}
                </div>
              </div>
              <div className="p-2 bg-green-100 rounded-full">
                <ArrowDown className="h-5 w-5 text-green-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <div className="text-sm text-gray-500">{t("totalSpendingLabel")}</div>
                <div className="text-xl font-bold text-coral-600">
                  {formatCurrency(summary.totalOut)}
                </div>
              </div>
              <div className="p-2 bg-coral-100 rounded-full">
                <ArrowUp className="h-5 w-5 text-coral-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <div className="text-sm text-gray-500">{t("currentBalanceLabel")}</div>
                <div className="text-xl font-bold text-navy-blue">
                  {formatCurrency(summary.totalIn - summary.totalOut)}
                </div>
              </div>
              <div className="p-2 bg-blue-100 rounded-full">
                <Calendar className="h-5 w-5 text-blue-600" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-4 space-y-3 sm:space-y-0">
        <Tabs defaultValue="all" value={filter} onValueChange={setFilter}>
          <TabsList>
            <TabsTrigger value="all">{t("filterAll")}</TabsTrigger>
            <TabsTrigger value="deposit">{t("filterDeposit")}</TabsTrigger>
            <TabsTrigger value="payment">{t("filterSpending")}</TabsTrigger>
          </TabsList>
        </Tabs>

        <div className="flex items-center space-x-2">
          <Select value={timeframe} onValueChange={setTimeframe}>
            <SelectTrigger className="w-[180px]">
              <div className="flex items-center">
                <Filter className="h-4 w-4 mr-2" />
                <SelectValue placeholder={t("timeframePlaceholder")} />
              </div>
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">{t("timeframeAll")}</SelectItem>
              <SelectItem value="month">{t("timeframeMonth")}</SelectItem>
              <SelectItem value="year">{t("timeframeYear")}</SelectItem>
            </SelectContent>
          </Select>

          <Button variant="outline" size="sm" onClick={() => window.print()}>
            {t("exportButton")}
          </Button>
        </div>
      </div>

      {/* Transactions List */}
      {filteredTransactions.length > 0 ? (
        <div className="space-y-3">
          {filteredTransactions.map((transaction) => (
            <Link href={`/user/transactions/${transaction.id}`} key={transaction.id}>
              <div className="flex items-center gap-4 p-3 bg-white hover:bg-gray-50 border rounded-md transition cursor-pointer">
                <div
                  className={`p-2 rounded-full ${
                    transaction.type === "topup" ? "bg-green-100" : "bg-coral-100"
                  }`}
                >
                  {transaction.type === "topup" ? (
                    <ArrowDown className="h-5 w-5 text-green-600" />
                  ) : (
                    <ArrowUp className="h-5 w-5 text-coral-600" />
                  )}
                </div>

                <div className="flex-1">
                  <div className="font-medium">{getTransactionTypeLabel(transaction.type)}</div>
                  <div className="text-xs text-gray-500">
                    {formatDate(transaction.createdAt)} - ID: {transaction.id}
                  </div>
                </div>

                <div className="text-right">
                  <div
                    className={`font-semibold ${
                      transaction.type === "topup" ? "text-green-600" : "text-coral-600"
                    }`}
                  >
                    {transaction.type === "topup" ? "+" : "-"} {formatCurrency(transaction.amount)}
                  </div>
                  <StatusBadge status={transaction.status} t={t} />
                </div>
              </div>
            </Link>
          ))}
        </div>
      ) : (
        <div className="text-center py-10 text-gray-500">{t("noTransactions")}</div>
      )}
    </div>
  );
}
