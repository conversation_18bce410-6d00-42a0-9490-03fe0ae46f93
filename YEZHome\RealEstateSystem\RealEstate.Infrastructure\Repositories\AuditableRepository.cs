﻿using Microsoft.EntityFrameworkCore;
using RealEstate.Domain.Entities;
using RealEstate.Domain.Interfaces;

namespace RealEstate.Infrastructure.Repositories
{
    public class AuditableRepository<T> : Repository<T>, IAuditableRepository<T> where T : BaseEntityWithAuditable
    {
        public AuditableRepository(ApplicationDbContext context) : base(context) { }

        public async Task<IEnumerable<T>> GetActiveAsync()
        {
            return await _dbSet.Where(x => !x.IsDeleted).ToListAsync();
        }

        public override void Update(T entity)
        {
            entity.UpdatedAt = DateTime.UtcNow;
            base.Update(entity);
        }

        public IQueryable<T> GetQueryable()
        {
            return _context.Set<T>().AsQueryable();
        }
    }

}
