INSERT INTO public."AdminRole" ("Id", "Code", "RoleName", "Description") VALUES
('a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a11', 'SUPER_MOD', 'Super Mod', '<PERSON><PERSON><PERSON><PERSON> cao nhất trong hệ thống, truy cập đầy đủ tất cả các tính năng và dữ liệu.'),
('a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a12', 'SYSTEM_ADMIN', 'System Admin', 'Quản lý người dùng, duy<PERSON><PERSON> bà<PERSON> đăng, quản lý tin tức. Không được xem màn hình kế toán.'),
('a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a13', 'ADMIN_APPROVER', '<PERSON><PERSON>', 'Chỉ có nhiệm vụ duy<PERSON> và quản lý trạng thái bài đăng. <PERSON>h<PERSON><PERSON> được xem giá mỗi bài đăng.'),
('a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a14', 'ADMIN_CONTENT', 'Admin Nội Dung', 'Chịu trách nhiệm quản lý và viết bài cho mục tin tức.'),
('a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a15', 'FINANCE_MANAGER', 'Kế Toán Trưởng', 'Quản lý toàn bộ màn hình kế toán, xuất hóa đơn điện tử, và xem các báo cáo tài chính.'),
('a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a16', 'FINANCE_STAFF', 'Kế Toán Viên', 'Quản lý màn hình kế toán cơ bản, xuất hóa đơn điện tử, xem các yêu cầu hóa đơn. Không được xem báo cáo tài chính tổng hợp.');

INSERT INTO public."Permission" ("Code", "PermissionName", "Description") VALUES
('P_USER_MANAGE_FULL', 'Quản lý người dùng đầy đủ', 'Tạo, sửa, xóa, phân quyền người dùng.'),
('P_VIEW_USER_MANAGEMENT_SCREEN', 'Xem màn hình quản lý người dùng', 'Cho phép truy cập màn hình quản lý người dùng.'),
('P_LISTING_APPROVE', 'Duyệt bài đăng', 'Cho phép duyệt hoặc từ chối các bài đăng.'),
('P_LISTING_VIEW_PRICE', 'Xem giá bài đăng', 'Cho phép xem thông tin giá của các bài đăng.'),
('P_NEWS_MANAGE', 'Quản lý tin tức', 'Thêm, sửa, xóa và viết bài mục tin tức.'),
('P_VIEW_NEWS_EDITOR_SCREEN', 'Xem màn hình soạn tin tức', 'Cho phép truy cập màn hình quản lý tin tức.'),
('P_ACCOUNTING_VIEW', 'Xem màn hình kế toán', 'Cho phép truy cập dashboard kế toán.'),
('P_INVOICE_EXPORT_E', 'Xuất hóa đơn điện tử', 'Cho phép tạo và xuất hóa đơn điện tử.'),
('P_REPORT_FINANCE_VIEW', 'Xem báo cáo tài chính', 'Cho phép truy cập các báo cáo thu, chi, lợi nhuận, thuế.'),
('P_INVOICE_REQUEST_VIEW', 'Xem yêu cầu hóa đơn', 'Cho phép xem danh sách yêu cầu xuất hóa đơn của khách hàng.'),
('P_ALL_ACCESS', 'Quyền truy cập toàn bộ', 'Có tất cả các quyền trong hệ thống.');

-- Gán TẤT CẢ các quyền hiện có cho Super Mod
INSERT INTO public."RolePermission" ("RoleID", "PermissionID")
SELECT
    ar."Id" AS "RoleID",
    p."Id" AS "PermissionID"
FROM
    public."AdminRole" AS ar,
    public."Permission" AS p
WHERE
    ar."Code" = 'SUPER_MOD'
ON CONFLICT ("RoleID", "PermissionID") DO NOTHING; -- Tránh lỗi nếu đã tồn tại bản ghi


-- Gán quyền cho System Admin
INSERT INTO public."RolePermission" ("RoleID", "PermissionID")
SELECT
    ar."Id" AS "RoleID",
    p."Id" AS "PermissionID"
FROM
    public."AdminRole" AS ar,
    public."Permission" AS p
WHERE
    ar."Code" = 'SYSTEM_ADMIN' AND p."Code" IN (
        'P_USER_MANAGE_FULL',             -- Tạo/sửa/xóa/phân quyền user
        'P_VIEW_USER_MANAGEMENT_SCREEN',  -- Xem màn hình quản lý user
        'P_LISTING_APPROVE',              -- Duyệt bài đăng
        'P_LISTING_MANAGE',               -- Quản lý (sửa/xóa) bài đăng (nếu có)
        'P_NEWS_MANAGE',                  -- Quản lý mục tin tức
        'P_VIEW_NEWS_EDITOR_SCREEN'       -- Xem màn hình soạn tin tức
    )
ON CONFLICT ("RoleID", "PermissionID") DO NOTHING;

-- Gán quyền cho Admin Duyệt Bài
INSERT INTO public."RolePermission" ("RoleID", "PermissionID")
SELECT
    ar."Id" AS "RoleID",
    p."Id" AS "PermissionID"
FROM
    public."AdminRole" AS ar,
    public."Permission" AS p
WHERE
    ar."Code" = 'ADMIN_APPROVER' AND p."Code" IN (
        'P_LISTING_APPROVE'               -- Duyệt bài đăng
    )
ON CONFLICT ("RoleID", "PermissionID") DO NOTHING;

-- Gán quyền cho Admin Nội Dung
INSERT INTO public."RolePermission" ("RoleID", "PermissionID")
SELECT
    ar."Id" AS "RoleID",
    p."Id" AS "PermissionID"
FROM
    public."AdminRole" AS ar,
    public."Permission" AS p
WHERE
    ar."Code" = 'ADMIN_CONTENT' AND p."Code" IN (
        'P_NEWS_MANAGE',                  -- Quản lý (thêm/sửa/xóa/viết) tin tức
        'P_VIEW_NEWS_EDITOR_SCREEN'       -- Xem màn hình soạn tin tức
    )
ON CONFLICT ("RoleID", "PermissionID") DO NOTHING;