using System.ComponentModel.DataAnnotations;

namespace RealEstate.API.DTO
{
    public class LogPropertyViewRequest
    {
        [Required]
        public string PropertyId { get; set; } = string.Empty;
        
        public string? UserAgent { get; set; }

        [Required]
        public string? SessionId { get; set; }
        public string? DeviceType { get; set; }
        public string? Platform { get; set; }
        public string? Browser { get; set; }
        public string? DeviceId { get; set; }
    }
} 