"use client";

import { useState, useEffect } from "react";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { useTranslations } from "next-intl";
import { formatCurrency } from "@/lib/utils";

const RankChangeDialog = ({ 
  open, 
  onOpenChange, 
  rankChangeDetails, 
  onConfirm, 
  onCancel,
}) => {
  const t = useTranslations("PropertyForm");
  const tWallet = useTranslations("UserWalletPage");

  return (
    <AlertDialog open={open} onOpenChange={onOpenChange}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>{rankChangeDetails?.isUpgrade ? t("congratulations") : t("announcementChange")}</AlertDialogTitle>
          <AlertDialogDescription>
            {
              rankChangeDetails
                ? rankChangeDetails.isUpgrade
                  ? t("rankUpgradeMessage", {
                      currentRank: tWallet(rankChangeDetails.currentRank) ?? rankChangeDetails.currentRank,
                      previousPrice: formatCurrency(rankChangeDetails.previousPrice),
                      currentPrice: formatCurrency(rankChangeDetails.currentPrice),
                    })
                  : t("rankChangeMessage", {
                      currentRank: tWallet(rankChangeDetails.currentRank) ?? rankChangeDetails.currentRank,
                      previousPrice: formatCurrency(rankChangeDetails.previousPrice),
                      currentPrice: formatCurrency(rankChangeDetails.currentPrice),
                    })
                : // Provide a fallback message or render nothing if rankChangeDetails is null
                  t("loadingRankChangeInfo") // Example fallback, adjust as needed
            }
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel onClick={onCancel}>{t("cancel")}</AlertDialogCancel>
          <AlertDialogAction onClick={onConfirm}>
            {rankChangeDetails?.isUpgrade ? t("continue") : t("updatePrice")}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
};

export default RankChangeDialog;
