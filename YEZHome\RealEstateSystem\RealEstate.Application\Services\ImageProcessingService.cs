using Microsoft.Extensions.Logging;
using RealEstate.Application.Interfaces;
using SixLabors.Fonts;
using SixLabors.ImageSharp;
using SixLabors.ImageSharp.Drawing.Processing;
using SixLabors.ImageSharp.Processing;

namespace RealEstate.Application.Services
{

    public class ImageProcessingService : IImageProcessingService
    {
        private readonly ILogger<ImageProcessingService> _logger;

        public ImageProcessingService(ILogger<ImageProcessingService> logger)
        {
            _logger = logger;
        }

        public async Task<string> ResizeImageAsync(string inputPath, string outputPath, int? width, int? height, bool preserveAspectRatio = true)
        {
            try
            {
                using var image = await Image.LoadAsync(inputPath);
                
                if (width.HasValue || height.HasValue)
                {
                    var resizeOptions = new ResizeOptions
                    {
                        Size = new Size(width ?? 0, height ?? 0),
                        Mode = preserveAspectRatio ? ResizeMode.Max : ResizeMode.Stretch
                    };

                    image.Mutate(x => x.Resize(resizeOptions));
                }

                // Ensure the directory exists
                Directory.CreateDirectory(Path.GetDirectoryName(outputPath));
                
                await image.SaveAsync(outputPath);
                return outputPath;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error resizing image {InputPath} to {OutputPath}", inputPath, outputPath);
                throw;
            }
        }

        public async Task<string> AddWatermarkAsync(string inputPath, string outputPath, string watermarkText, float opacity = 0.5f)
        {
            try
            {
                using var image = await Image.LoadAsync(inputPath);

                // Get system font (fallback to first available)
                var fontFamily = SystemFonts.TryGet("Arial", out var family) ? family : SystemFonts.Families.First();
                var font = fontFamily.CreateFont(Math.Max(image.Width / 20, 20));

                // Convert opacity (0.0 - 1.0) to ARGB alpha (0 - 255)
                var alpha = (byte)(opacity * 255);
                var textColor = Color.White.WithAlpha(alpha);

                // Set text options
                var textOptions = new TextOptions(font)
                {
                    HorizontalAlignment = HorizontalAlignment.Center,
                    VerticalAlignment = VerticalAlignment.Center
                };

                // Center position
                var position = new PointF(image.Width / 2f, image.Height / 2f);

                // Apply watermark
                image.Mutate(ctx => ctx.DrawText(watermarkText, font, textColor, position));

                // Ensure directory exists
                Directory.CreateDirectory(Path.GetDirectoryName(outputPath) ?? throw new InvalidOperationException("Invalid output path"));

                await image.SaveAsync(outputPath);
                return outputPath;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error adding watermark to image {InputPath}", inputPath);
                throw;
            }
        }

        public async Task<string> ProcessImageAsync(string inputPath, string outputPath, int? width, int? height, bool preserveAspectRatio = true, string watermarkText = null, float opacity = 0.5f)
        {
            try
            {
                using var image = await Image.LoadAsync(inputPath);
                
                // Resize if needed
                if (width.HasValue || height.HasValue)
                {
                    var resizeOptions = new ResizeOptions
                    {
                        Size = new Size(width ?? 0, height ?? 0),
                        Mode = preserveAspectRatio ? ResizeMode.Max : ResizeMode.Stretch
                    };

                    image.Mutate(x => x.Resize(resizeOptions));
                }
                
                // Add watermark if text is provided
                if (!string.IsNullOrEmpty(watermarkText))
                {
                    // Get system font (fallback to first available)
                    var fontFamily = SystemFonts.TryGet("Arial", out var family) ? family : SystemFonts.Families.First();
                    var font = fontFamily.CreateFont(Math.Max(image.Width / 20, 20));

                    // Convert opacity (0.0 - 1.0) to ARGB alpha (0 - 255)
                    var alpha = (byte)(opacity * 255);
                    var textColor = Color.White.WithAlpha(alpha);

                    // Set text options
                    var textOptions = new TextOptions(font)
                    {
                        HorizontalAlignment = HorizontalAlignment.Center,
                        VerticalAlignment = VerticalAlignment.Center
                    };

                    // Center position
                    var position = new PointF(image.Width / 2f, image.Height / 2f);

                    // Apply watermark
                    image.Mutate(ctx => ctx.DrawText(watermarkText, font, textColor, position));
                }

                // Ensure the directory exists
                Directory.CreateDirectory(Path.GetDirectoryName(outputPath));
                
                await image.SaveAsync(outputPath);
                return outputPath;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing image {InputPath}", inputPath);
                throw;
            }
        }

        public async Task<Dictionary<string, string>> GenerateImageSizesAsync(string inputPath, string outputDirectory, string baseFilename, bool addWatermark = true, string watermarkText = "YEZ HOME")
        {
            // Define standard sizes
            var sizes = new Dictionary<string, (int? Width, int? Height)>
            {
                ["thumbnail"] = (150, 150),
                ["small"] = (300, 300),
                ["medium"] = (800, 600),
                ["large"] = (1200, 900),
                ["original"] = (null, null)
            };

            var result = new Dictionary<string, string>();

            // Ensure the output directory exists
            Directory.CreateDirectory(outputDirectory);
            
            foreach (var size in sizes)
            {
                var sizeName = size.Key;
                var dimensions = size.Value;
                
                var outputFilename = $"{baseFilename}_{sizeName}{Path.GetExtension(inputPath)}";
                var outputPath = Path.Combine(outputDirectory, outputFilename);
                
                // Only add watermark to non-thumbnail images if requested
                var shouldWatermark = addWatermark && sizeName != "thumbnail";
                
                await ProcessImageAsync(
                    inputPath, 
                    outputPath, 
                    dimensions.Width, 
                    dimensions.Height, 
                    true, 
                    shouldWatermark ? watermarkText : null
                );
                
                result[sizeName] = outputPath;
            }
            
            return result;
        }
    }
}
