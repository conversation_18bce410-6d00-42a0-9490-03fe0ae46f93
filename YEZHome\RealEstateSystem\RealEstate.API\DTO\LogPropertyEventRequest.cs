using System.ComponentModel.DataAnnotations;
using RealEstate.API.Attributes;

namespace RealEstate.API.DTO
{
    public class LogPropertyEventRequest
    {
        [Required]
        public string PropertyId { get; set; } = string.Empty;
        
        [Required]
        [ValidPropertyEngagementEventType]
        public string EventType { get; set; } = string.Empty;
        
        public string? UserAgent { get; set; }

        [Required]
        public string? SessionId { get; set; }
        public string? DeviceType { get; set; }
        public string? Platform { get; set; }
        public string? Browser { get; set; }
        public string? DeviceId { get; set; }
    }
} 