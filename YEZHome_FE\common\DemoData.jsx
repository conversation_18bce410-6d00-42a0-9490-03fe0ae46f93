export const properties = [
  {
    "id": 1,
    "title": "Modern Apartment in City Center",
    "price": 450000,
    "address": "Downtown, New York",
    "image": "https://plus.unsplash.com/premium_photo-1689609950112-d66095626efb?w=500&auto=format&fit=crop&q=60&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8NXx8cmVhbCUyMGVzdGF0ZXxlbnwwfHwwfHx8MA%3D%3D",
    "status": "Active"
  },
  {
    "id": 2,
    "title": "Charming Cottage with Garden",
    "price": 600000,
    "address": "Rural Oxfordshire, UK",
    "image": "https://images.unsplash.com/photo-1568605114967-8130f3a36994?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1740&q=80",
    "status": "Sold"
  },
  {
    "id": 3,
    "title": "Spacious Family Home near Park",
    "price": 850000,
    "address": "Residential Area, Chicago",
    "image": "https://images.unsplash.com/photo-1518780664697-55e3ad937233?w=500&auto=format&fit=crop&q=60&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8Mnx8aG91c2V8ZW58MHx8MHx8fDA%3D",
    "status": "Pending"
  },
  {
    "id": 4,
    "title": "Luxury Penthouse with City Views",
    "price": 1200000,
    "address": "Midtown Manhattan, New York",
    "image": "https://images.unsplash.com/photo-1570129477492-45c003edd2be?w=500&auto=format&fit=crop&q=60&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8M3x8aG91c2V8ZW58MHx8MHx8fDA%3D",
    "status": "Pending"
  },
  {
    "id": 5,
    "title": "Beachfront Condo with Ocean Views",
    "price": 950000,
    "address": "Miami Beach, Florida",
    "image": "https://images.unsplash.com/photo-1598228723793-52759bba239c?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1740&q=80",
    "status": "Active"
  },
    {
    "id": 6,
    "title": "Rustic Cabin in the Woods",
    "price": 300000,
    "address": "Rocky Mountains, Colorado",
    "image": "https://images.unsplash.com/photo-1600596542815-ffad4c1539a9?w=500&auto=format&fit=crop&q=60&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8OHx8aG91c2V8ZW58MHx8MHx8fDA%3D",
    "status": "Active"
  },
  {
    "id": 7,
    "title": "Modern Townhouse with Rooftop Terrace",
    "price": 700000,
    "address": "Brooklyn, New York",
    "image": "https://plus.unsplash.com/premium_photo-1661883964999-c1bcb57a7357?w=500&auto=format&fit=crop&q=60&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8MTN8fGhvdXNlfGVufDB8fDB8fHww",
    "status": "Active"
  },
    {
    "id": 8,
    "title": "Historic Victorian House",
    "price": 1100000,
    "address": "San Francisco, California",
    "image": "https://images.unsplash.com/photo-1583608205776-bfd35f0d9f83?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1740&q=80",
    "status": "Active"
  },
  {
    "id": 9,
    "title": "Studio Apartment in Trendy Neighborhood",
    "price": 350000,
    "address": "Shoreditch, London",
    "image": "https://images.unsplash.com/photo-1572120360610-d971b9d7767c?w=500&auto=format&fit=crop&q=60&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8MTV8fGhvdXNlfGVufDB8fDB8fHww",
    "status": "Sold"
  },
  {
    "id": 10,
    "title": "Farmhouse with Acreage",
    "price": 750000,
    "address": "Tuscany, Italy",
    "image": "https://images.unsplash.com/photo-1480074568708-e7b720bb3f09?w=500&auto=format&fit=crop&q=60&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8MTh8fGhvdXNlfGVufDB8fDB8fHww",
    "status": "Active"
  }
]

export const notifications = [
  { id: 1, message: "New inquiry for Sunset Villa", timestamp: "2 hours ago", read: false },
  { id: 2, message: "Maintenance request for Mountain Retreat", timestamp: "1 day ago", read: true },
  { id: 3, message: "Lease renewal for City Loft", timestamp: "3 days ago", read: true },
  { id: 4, message: "Price change alert for Lakeside Cabin", timestamp: "4 days ago", read: false },
  { id: 5, message: "New review for Downtown Apartment", timestamp: "1 week ago", read: true },
  { id: 6, message: "Booking confirmation for Suburban House", timestamp: "1 week ago", read: false },
  { id: 7, message: "Maintenance completed at Beach Bungalow", timestamp: "2 weeks ago", read: true },
  { id: 8, message: "Property tax reminder for Country Estate", timestamp: "2 weeks ago", read: true },
  { id: 9, message: "Maintenance completed at Beach Bungalow", timestamp: "2 weeks ago", read: true },
  { id: 10, message: "Property tax reminder for Country Estate", timestamp: "2 weeks ago", read: true },
  { id: 11, message: "Maintenance completed at Beach Bungalow", timestamp: "2 weeks ago", read: true },
  { id: 12, message: "Property tax reminder for Country Estate", timestamp: "2 weeks ago", read: true },
]
