
# YEZHOME: A Real Estate Platform

YEZHOME là nền tảng ứng dụng mua – bán – thuê bất động sản (bds). Hướng đến người dùng thật sự có nhu cầu về giao dịch bds.
<PERSON>ạt động trên đa nền tảng IOS, Android và Website. 

YEZHOME is a platform for buying, selling, and renting real estate, designed for users with genuine real estate transaction needs.

Operating on multiple platforms: iOS, Android, and Website.

## How it works:
1. User and business registration: Users and businesses must register and verify their identity using their ID card and phone number.
2. Clear and transparent listing information: Listing information must be clear and transparent according to eZZy's regulations (including videos, images, and property status).
3. Peer-to-peer rating and reporting: Users can rate and report violations between businesses, sellers, and buyers.
4. Clear, transparent, and objective rating system: Improves the credibility of sellers and businesses.

## Entities
### 1. User:
A logged-in or unregistered user seeking real estate. 
### 2. Property Owner:
A seller/agent who wants to sell real estate (must log in to post). c) Admin (with sub-permissions):
System administrator: The system is divided into sub-permissions for administrators as follows:
### 3. SEO Editor:
Can only post articles (news, ...) for SEO purposes.
Cannot view user property information or other permissions.
Property Admin:
Can only view/approve property listings and approve user/property owner comments on properties.
### 4. System Admin:
Full system privileges, including the permissions of SEO Editor and Property Admin. Additionally, they can:
Add/edit/delete/grant permissions to registered users in the system.
Add/edit/delete other information related to eZZy (company address, hotline, phone number, ...).
## Feature List
The following is a preliminary feature list for each entity (detailed features will be updated later):

### 1. User:

* Quick and easy search through an intuitive checkpoint feature displayed on the map.
* In-app chat and call feature (including video call): facilitates easy connection between buyers and sellers.
* Shopping cart feature: helps buyers easily save and compare products before deciding to contact the seller.
* Peer-to-peer rating feature: users can rate each other to increase transparency and credibility for both buyers and sellers.
* Peer-to-peer reporting feature: users can report each other to limit ghosting and dishonest sellers.
* Edit profile account (change password, contact information, ...).
### 2. Property Owner/Agent:

* Convenient and easy posting feature like posting on social networks.
* Checkpoint feature on Google Map helps customers easily access real estate products.
* Easy management of posts like on e-commerce.
* Save post feature (7-day limit)
* Edit post feature (can only be edited within 24 hours from the time of posting)
* Easily connect with interested customers through in-app chat and call features (including video call).
* Rating system from customers to increase the credibility of businesses and agents.
* Edit profile account (change password, contact information, ...).
### 3. Admin (permissions vary by admin type as mentioned in section 3):

* Easily add/edit/delete company information (contact information, hotline, about us, ...) of eZZy without having to modify the code.
* Add/edit/delete banner ads (this depends on the design, if the design has a dynamic header as a banner, then it's needed, otherwise not)
* Add/edit/delete/grant permissions to users in the system.
* Manage/review property listings.
* Manage SEO articles (news, experience sharing, ...).

## Tech Stack

**Client:** React, TailwindCSS

**Server:** .net Core 8.0

**Database:** Postgress



## Feedback

If you have any feedback, please reach out to us at 
* <EMAIL> 
* <EMAIL>


## License

Copyright ©2025 eZZy. All rights reserved.
## Authors

©2025 eZZy

