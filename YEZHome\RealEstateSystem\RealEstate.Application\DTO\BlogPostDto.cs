﻿namespace RealEstate.Application.DTO
{
    public class BlogPostDto
    {
        public Guid Id { get; set; }
        public Guid AuthorID { get; set; }
        public string AuthorName { get; set; } // Assuming you want to include the author's name as part of the DTO
        public string Title { get; set; }
        public string Slug { get; set; }
        public string Content { get; set; }
        public string FeaturedImage { get; set; }
        public string Tags { get; set; }
        public string Status { get; set; }
        public bool IsFeature { get; set; }
        public DateTime? PublishedAt { get; set; }
        public IEnumerable<BlogCommentDto> BlogComments { get; set; } // Including comments as a collection of DTOs
    }

}
