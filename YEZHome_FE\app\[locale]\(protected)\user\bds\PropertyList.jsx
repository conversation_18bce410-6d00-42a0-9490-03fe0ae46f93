"use client";
import { memo, useState, useCallback, useMemo, useEffect } from "react";
import { Loader<PERSON>ircle, Trash2, Search, Zap, RefreshCcw } from "lucide-react";
import { useRouter } from "next/navigation";
import AlertPopup from "@/components/layout/AlertPopup";
import { useAlert } from "@/contexts/AlertContext";
import NoData from "@/components/layout/NoData";
import { useToast } from "@/hooks/use-toast";
import {
  deletePropertyById,
  getPropertyByUser,
  verifyPropertyRemainingTimes,
  updatePropertyStatus,
  bulkDeleteProperties,
  bulkUpdatePropertyHighlight,
} from "@/app/actions/server/property";
import { Button } from "@/components/ui/button";
import ContactRequestModal from "@/components/user-property/ContactRequestModal";
import HistoryModal from "@/components/user-property/HistoryModal";
import { PropertyStatus } from "@/lib/enum";
import { useTranslations } from "next-intl";
import { Input } from "@/components/ui/input";
import { Checkbox } from "@/components/ui/checkbox";
import PropertyCard from "@/components/user-property/PropertyCard";
import { LoadingSpinner } from "@/components/ui/loading-spinner";

// Define filter keys
const FilterKeys = {
  ALL: "all",
  APPROVED: "Approved",
  PENDING_APPROVAL: "PendingApproval",
  REJECTED_BY_ADMIN: "RejectedByAdmin",
  REJECTED_DUE_TO_UNPAID: "RejectedDueToUnpaid",
  WAITING_PAYMENT: "WaitingPayment",
  EXPIRED: "Expired",
  DRAFT: "Draft",
  SOLD: "Sold",
};

function PropertyList({ initialData }) {
  const [data, setData] = useState(initialData || []);
  const [loadingId, setLoadingId] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();
  const { showAlert } = useAlert();
  const { toast } = useToast();
  const [selectedPropertyId, setSelectedPropertyId] = useState(null);
  const [isContactModalOpen, setIsContactModalOpen] = useState(false);
  const [isHistoryModalOpen, setIsHistoryModalOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [activeFilter, setActiveFilter] = useState(FilterKeys.ALL);
  const [selectedIds, setSelectedIds] = useState(new Set());
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize] = useState(10);
  const t = useTranslations("PropertyList");
  const tCommon = useTranslations("Common");
  // State for filter counts
  const [filterCounts, setFilterCounts] = useState({
    [FilterKeys.ALL]: 0,
    [FilterKeys.APPROVED]: 0,
    [FilterKeys.PENDING_APPROVAL]: 0,
    [FilterKeys.REJECTED_BY_ADMIN]: 0,
    [FilterKeys.REJECTED_DUE_TO_UNPAID]: 0,
    [FilterKeys.WAITING_PAYMENT]: 0,
    [FilterKeys.EXPIRED]: 0,
    [FilterKeys.DRAFT]: 0,
    [FilterKeys.SOLD]: 0,
  });

  

  // Fetch filter counts from server
  const fetchFilterCounts = useCallback(async () => {
    try {
      // Call the getPropertyByUser with 'counts' parameter to use the stats endpoint
      const result = await getPropertyByUser("counts");
      if (result && result?.success && result?.data) {
        // The getPropertyStats function in property.jsx transforms the API response
        // to match this expected format
        setFilterCounts({
          [FilterKeys.ALL]: result.data.total || 0,
          [FilterKeys.APPROVED]: result.data.approved || 0,
          [FilterKeys.PENDING_APPROVAL]: result.data.pendingApproval || 0,
          [FilterKeys.REJECTED_BY_ADMIN]: result.data.rejectedByAdmin || 0,
          [FilterKeys.REJECTED_DUE_TO_UNPAID]: result.data.rejectedDueToUnpaid || 0,
          [FilterKeys.WAITING_PAYMENT]: result.data.waitingPayment || 0,
          [FilterKeys.EXPIRED]: result.data.expired || 0,
          [FilterKeys.DRAFT]: result.data.draft || 0,
          [FilterKeys.SOLD]: result.data.sold || 0,
        });
      }
    } catch (error) {
      console.error("Error fetching filter counts:", error);
      toast({
        description: "Failed to fetch property statistics",
        variant: "destructive",
      });
    }
  }, [toast]);

  // --- Data Fetching Logic ---
  const fetchProperties = useCallback(
    async (status = null, page = 1, size = pageSize) => {
      setIsLoading(true);
      try {
        const result = await getPropertyByUser(status, page, size);

        if (result.success && result.data) {
          // Extract items array from the paginated response
          const propertyData = result.data.items || [];

          // Set the property data
          setData(propertyData);

          // Update pagination state
          setCurrentPage(result.data.currentPage || page);

          // Refresh filter counts after data changes
          fetchFilterCounts();
        } else {
          console.error("API returned error:", result);
          toast({
            description: result.message || "Failed to fetch properties",
            variant: "destructive",
          });
        }
      } catch (error) {
        console.error("Error fetching properties:", error);
        toast({
          description: "An unexpected error occurred",
          variant: "destructive",
        });
      } finally {
        setIsLoading(false);
      }
    },
    [pageSize, toast, fetchFilterCounts]
  );

  // Fetch filter counts on initial load
  useEffect(() => {
    fetchFilterCounts();
  }, [fetchFilterCounts]);

  // Initial data load and filter changes
  useEffect(() => {
    // On first render, use initialData if available
    if (initialData && Array.isArray(initialData) && initialData.length > 0 && activeFilter === FilterKeys.ALL) {
      setData(initialData);
      return;
    }

    const statusParam = activeFilter !== FilterKeys.ALL ? activeFilter : null;
    fetchProperties(statusParam, 1, pageSize);
  }, [activeFilter, fetchProperties, pageSize, initialData]);

  // --- Filtering Logic for Search ---
  const filteredData = useMemo(() => {
    // Ensure data is an array before filtering
    if (!Array.isArray(data)) {
      console.error("Data is not an array:", data);
      return [];
    }

    return data.filter((property) => {
      // Only apply search filter client-side
      return property && property.name && property.name.toLowerCase().includes(searchTerm.toLowerCase());
    });
  }, [data, searchTerm]);

  // --- Handlers ---
  const handleEdit = useCallback(
    (propertyId) => {
      router.push(`/user/bds/${propertyId}`);
    },
    [router]
  );

  const handleDelete = useCallback(
    async (propertyId) => {
      showAlert({
        title: t("deleteConfirmTitle"),
        message: t("deleteConfirmMessage"),
        onConfirm: async () => {
          setLoadingId(propertyId);
          try {
            const result = await deletePropertyById(propertyId);
            if (result.success) {
              // Clear the selected ID
              setSelectedIds((prev) => {
                const newSet = new Set(prev);
                newSet.delete(propertyId);
                return newSet;
              });

              // Show success message
              toast({
                description: t("deleteSuccessToast"),
                className: "bg-teal-600 text-white",
              });

              // Refresh data with current filter
              fetchProperties(activeFilter !== FilterKeys.ALL ? activeFilter : null, 1, pageSize);
            } else {
              toast({
                description: result.message || t("deleteErrorToast"),
                variant: "destructive",
              });
            }
          } catch (error) {
            toast({
              description: t("deleteErrorToast"),
              variant: "destructive",
            });
          } finally {
            setLoadingId(null);
          }
        },
      });
    },
    [showAlert, toast, t, setSelectedIds, fetchProperties, activeFilter, pageSize]
  );

  const handleSendToReviewRequest = useCallback(
    async (propertyId) => {
      setLoadingId(propertyId);
      try {
        const remainingTimes = await verifyPropertyRemainingTimes(propertyId);
        if (remainingTimes.success) {
          showAlert({
            title: t("verifyConfirmTitle"),
            message: t("verifyConfirmMessage", { remainingTimes: remainingTimes.data }),
            onConfirm: async () => {
              try {
                setLoadingId(propertyId);
                const formData = new FormData();
                formData.append("propertyId", propertyId);
                formData.append("status", PropertyStatus.PENDING_APPROVAL);

                const result = await updatePropertyStatus(formData);
                if (result.success) {
                  toast({
                    description: t("verifySuccessToast"),
                    className: "bg-teal-600 text-white",
                  });

                  // Refresh data with current filter
                  fetchProperties(activeFilter !== FilterKeys.ALL ? activeFilter : null, 1, pageSize);
                } else {
                  toast({
                    description: result?.message || t("verifyErrorToast"),
                    variant: "destructive",
                  });
                }
              } catch (error) {
                console.error("Error sending verification request:", error);
                toast({
                  description: t("verifyGenericErrorToast"),
                  variant: "destructive",
                });
              } finally {
                setLoadingId(null);
              }
            },
            hasCancel: true,
            onCancel: () => setLoadingId(null),
          });
        } else {
          setLoadingId(null);
          toast({
            description: remainingTimes?.message || t("verifyCheckErrorToast"),
            className: "bg-red-600 text-white",
          });
        }
      } catch (error) {
        setLoadingId(null);
        console.error("Error checking remaining verification times:", error);
        toast({
          description: t("verifyGenericErrorToast"),
          className: "bg-red-600 text-white",
        });
      }
    },
    [showAlert, toast, t, fetchProperties, activeFilter, pageSize]
  );

  const handleShowContacts = useCallback((propertyId) => {
    setSelectedPropertyId(propertyId);
    setIsContactModalOpen(true);
  }, []);

  const handleShowHistory = useCallback((propertyId) => {
    setSelectedPropertyId(propertyId);
    setIsHistoryModalOpen(true);
  }, []);

  // Checkbox handlers
  const handleCheckboxChange = useCallback((propertyId, checked) => {
    setSelectedIds((prev) => {
      const newSet = new Set(prev);
      if (checked) {
        newSet.add(propertyId);
      } else {
        newSet.delete(propertyId);
      }
      return newSet;
    });
  }, []);

  const handleSelectAllChange = useCallback(
    (checked) => {
      if (checked) {
        setSelectedIds(new Set(filteredData.map((p) => p.id)));
      } else {
        setSelectedIds(new Set());
      }
    },
    [filteredData]
  );

  // Bulk action handlers
  const handleBulkHighlight = useCallback(() => {
    if (selectedIds.size === 0) return;

    showAlert({
      title: t("bulkHighlightConfirmTitle", { count: selectedIds.size }),
      message: t("bulkHighlightConfirmMessage"),
      onConfirm: async () => {
        setLoadingId("bulk-highlight");
        try {
          const result = await bulkUpdatePropertyHighlight(Array.from(selectedIds), true);

          if (result.success) {
            toast({
              description: t("bulkHighlightSuccessToast", { count: selectedIds.size }),
              className: "bg-teal-600 text-white",
            });
            // Refresh data with current filter
            fetchProperties(activeFilter !== FilterKeys.ALL ? activeFilter : null, 1, pageSize);
          } else {
            toast({
              description: result.message || t("bulkHighlightErrorToast"),
              variant: "destructive",
            });
          }
        } catch (error) {
          console.error("Error highlighting properties:", error);
          toast({
            description: t("bulkHighlightGenericErrorToast"),
            variant: "destructive",
          });
        } finally {
          setLoadingId(null);
        }
      },
    });
  }, [selectedIds, showAlert, toast, fetchProperties, activeFilter, pageSize, t]);

  const handleBulkRenew = useCallback(() => {
    if (selectedIds.size === 0) return;

    // For now, just show a message since renew functionality is not implemented in the API
    toast({
      description: t("bulkRenewFeatureInDevelopment"),
      variant: "default",
    });

    // When API is available, implement similar to handleBulkHighlight
    // using the appropriate API endpoint
  }, [selectedIds, toast, t]);

  const handleBulkDelete = useCallback(() => {
    if (selectedIds.size === 0) return;

    showAlert({
      title: t("bulkDeleteConfirmTitle", { count: selectedIds.size }),
      message: t("bulkDeleteConfirmMessage"),
      onConfirm: async () => {
        setLoadingId("bulk-delete");
        try {
          const result = await bulkDeleteProperties(Array.from(selectedIds));

          if (result.success) {
            // Clear the selected IDs
            setSelectedIds(new Set());

            toast({
              description: t("bulkDeleteSuccessToast", { count: selectedIds.size }),
              className: "bg-teal-600 text-white",
            });

            // Refresh data with current filter
            fetchProperties(activeFilter !== FilterKeys.ALL ? activeFilter : null, 1, pageSize);
          } else {
            toast({
              description: result.message || t("bulkDeleteErrorToast"),
              variant: "destructive",
            });
          }
        } catch (error) {
          console.error("Error deleting properties:", error);
          toast({
            description: t("bulkDeleteGenericErrorToast"),
            variant: "destructive",
          });
        } finally {
          setLoadingId(null);
        }
      },
    });
  }, [selectedIds, showAlert, toast, fetchProperties, activeFilter, pageSize, setSelectedIds, t]);

  const showNoData = !isLoading && (!data || data.length === 0);

  // Determine if "Select All" should be checked
  const isAllSelected = filteredData.length > 0 && selectedIds.size === filteredData.length;
  // Determine indeterminate state for "Select All"
  const isIndeterminate = selectedIds.size > 0 && selectedIds.size < filteredData.length;

  // Determine NoData states
  // Empty database: When user has no properties at all (based on total count)
  const isEmptyDatabase = !isLoading && filterCounts[FilterKeys.ALL] === 0;
  // Empty search results: When user has properties but current filter/search returns no results
  const isEmptySearchResults = !isLoading && filterCounts[FilterKeys.ALL] > 0 && (!filteredData || filteredData.length === 0);
  // Has results: When filteredData has items
  const hasResults = !isLoading && filteredData && filteredData.length > 0;

  // --- Render ---
  return (
    <>
      <AlertPopup />
      
      {/* Search Input - Always visible, disabled during loading */}
      <div className="mb-4 flex flex-col sm:flex-row gap-4">
        <div className="relative flex-grow">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
          <Input
            type="search"
            placeholder={t("searchPlaceholder")}
            className="pl-8 w-full"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            disabled={isLoading}
          />
        </div>  
      </div>

      {/* Filter Buttons - Always visible, disabled during loading */}
      <div className="mb-4 flex flex-wrap gap-2 border-b relative">
        <Button
          variant={activeFilter === FilterKeys.ALL ? "solid" : "ghost"}
          size="sm"
          rounded="none"
          onClick={() => setActiveFilter(FilterKeys.ALL)}
          disabled={isLoading}
        >
          {t("all")} ({filterCounts[FilterKeys.ALL]})
        </Button>
        <Button
          variant={activeFilter === FilterKeys.APPROVED ? "solid" : "ghost"}
          size="sm"
          rounded="none"
          onClick={() => setActiveFilter(FilterKeys.APPROVED)}
          disabled={isLoading}
        >
          {t("approved")} ({filterCounts[FilterKeys.APPROVED]})
        </Button>
        <Button
          variant={activeFilter === FilterKeys.PENDING_APPROVAL ? "solid" : "ghost"}
          size="sm"
          rounded="none"
          onClick={() => setActiveFilter(FilterKeys.PENDING_APPROVAL)}
          disabled={isLoading}
        >
          {t("pendingApproval")} ({filterCounts[FilterKeys.PENDING_APPROVAL]})
        </Button>
        <Button
          variant={activeFilter === FilterKeys.REJECTED_BY_ADMIN ? "solid" : "ghost"}
          size="sm"
          rounded="none"
          onClick={() => setActiveFilter(FilterKeys.REJECTED_BY_ADMIN)}
          disabled={isLoading}
        >
          {t("rejectedByAdmin")} ({filterCounts[FilterKeys.REJECTED_BY_ADMIN]})
        </Button>
        <Button
          variant={activeFilter === FilterKeys.REJECTED_DUE_TO_UNPAID ? "solid" : "ghost"}
          size="sm"
          rounded="none"
          onClick={() => setActiveFilter(FilterKeys.REJECTED_DUE_TO_UNPAID)}
          disabled={isLoading}
        >
          {t("rejectedDueToUnpaid")} ({filterCounts[FilterKeys.REJECTED_DUE_TO_UNPAID]})
        </Button>
        <Button
          variant={activeFilter === FilterKeys.WAITING_PAYMENT ? "solid" : "ghost"}
          size="sm"
          rounded="none"
          onClick={() => setActiveFilter(FilterKeys.WAITING_PAYMENT)}
          disabled={isLoading}
        >
          {t("waitingPayment")} ({filterCounts[FilterKeys.WAITING_PAYMENT]})
        </Button>
        <Button
          variant={activeFilter === FilterKeys.EXPIRED ? "solid" : "ghost"}
          size="sm"
          rounded="none"
          onClick={() => setActiveFilter(FilterKeys.EXPIRED)}
          disabled={isLoading}
        >
          {t("expired")} ({filterCounts[FilterKeys.EXPIRED]})
        </Button>
        <Button
          variant={activeFilter === FilterKeys.DRAFT ? "solid" : "ghost"}
          size="sm"
          rounded="none"
          onClick={() => setActiveFilter(FilterKeys.DRAFT)}
          disabled={isLoading}
        >
          {t("draft")} ({filterCounts[FilterKeys.DRAFT]})
        </Button>
        <Button
          variant={activeFilter === FilterKeys.SOLD ? "solid" : "ghost"}
          size="sm"
          rounded="none"
          onClick={() => setActiveFilter(FilterKeys.SOLD)}
          disabled={isLoading}
        >
          {t("sold")} ({filterCounts[FilterKeys.SOLD]})
        </Button>
      </div>

      {/* Bulk Actions - Always visible, disabled during loading */}
      <div className="mb-4 flex items-center gap-4">
        <Checkbox
          id="select-all"
          checked={isAllSelected}
          onCheckedChange={handleSelectAllChange}
          aria-label="Select all properties on this page"
          data-state={isIndeterminate ? "indeterminate" : isAllSelected ? "checked" : "unchecked"}
          disabled={isLoading}
        />
        <label htmlFor="select-all" className={`text-sm font-medium ${isLoading ? 'text-gray-400' : ''}`}>
          {t("selectAll")}
        </label>
        {selectedIds.size > 0 && (
          <div className="flex gap-2 ml-auto">
            <Button
              size="sm"
              variant="outline"
              onClick={handleBulkHighlight}
              disabled={isLoading || loadingId === "bulk-delete" || loadingId === "bulk-highlight"}
            >
              <Zap className="h-4 w-4 mr-1" /> {tCommon("highlight_status")} ({selectedIds.size})
            </Button>
            <Button 
              size="sm" 
              variant="outline" 
              onClick={handleBulkRenew} 
              disabled={isLoading || loadingId === "bulk-delete" || loadingId === "bulk-highlight"}
            >
              <RefreshCcw className="h-4 w-4 mr-1" /> {t("renew")} ({selectedIds.size})
            </Button>
            <Button
              size="sm"
              variant="destructive"
              onClick={handleBulkDelete}
              disabled={isLoading || loadingId === "bulk-delete" || loadingId === "bulk-highlight"}
            >
              {loadingId === "bulk-delete" || loadingId === "bulk-highlight" ? (
                <LoaderCircle className="animate-spin h-4 w-4 mr-1" />
              ) : (
                <Trash2 className="h-4 w-4 mr-1" />
              )}
              {t("delete")} ({selectedIds.size})
            </Button>
          </div>
        )}
      </div>

      {/* Content Area - Conditional rendering based on loading and data states */}
      {isLoading ? (
        <LoadingSpinner />
      ) : isEmptyDatabase ? (
        <NoData
          hasCreateButton={true}
          createMessage={t("noProperty")}
          createPageRoute="/user/bds/new"
          createButtonTitle={t("createProperty")}
        />
      ) : isEmptySearchResults ? (
        <NoData message={t("noResults")} />
      ) : hasResults ? (
        <>
          <div className="grid gap-4">
            {filteredData.map((property) => (
              <PropertyCard
                key={property.id}
                property={property}
                onEdit={handleEdit}
                onDelete={handleDelete}
                onSendToReview={handleSendToReviewRequest}
                onShowContacts={handleShowContacts}
                onShowHistory={handleShowHistory}
                loadingId={loadingId}
                isSelected={selectedIds.has(property.id)}
                onCheckboxChange={handleCheckboxChange}
              />
            ))}
          </div>

          {/* Pagination Controls */}
          {data.length > pageSize && (
            <div className="mt-6 flex justify-center items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => fetchProperties(activeFilter !== FilterKeys.ALL ? activeFilter : null, Math.max(1, currentPage - 1), pageSize)}
                disabled={currentPage <= 1 || isLoading}
              >
                {t("previous")}
              </Button>
              <span className="text-sm">
                {t("page")} {currentPage}
              </span>
              <Button
                variant="outline"
                size="sm"
                onClick={() => fetchProperties(activeFilter !== FilterKeys.ALL ? activeFilter : null, currentPage + 1, pageSize)}
                disabled={filteredData.length < pageSize || isLoading}
              >
                {t("next")}
              </Button>
            </div>
          )}
        </>
      ) : null}

      {isContactModalOpen && (
        <ContactRequestModal propertyId={selectedPropertyId} open={isContactModalOpen} onClose={() => setIsContactModalOpen(false)} />
      )}

      {isHistoryModalOpen && <HistoryModal propertyId={selectedPropertyId} open={isHistoryModalOpen} onClose={() => setIsHistoryModalOpen(false)} />}
    </>
  );
}

export default memo(PropertyList);
