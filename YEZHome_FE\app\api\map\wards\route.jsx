export async function GET(request) {
  try {
    const API_URL = process.env.API_URL;
    const { searchParams } = new URL(request.url);
    const districtId = searchParams.get('districtId');
    
    if (!districtId) {
      return Response.json(
        { error: 'District ID is required' },
        { status: 400 }
      );
    }

    const response = await fetch(`${API_URL}/api/Address/districts/${districtId}/wards`, {
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch wards: ${response.status}`);
    }

    const data = await response.json();
    return Response.json(data);
  } catch (error) {
    console.error('Error fetching wards:', error);
    return Response.json(
      { error: 'Failed to fetch wards' },
      { status: 500 }
    );
  }
}
