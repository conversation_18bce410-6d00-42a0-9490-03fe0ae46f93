---
description: 
globs: 
alwaysApply: false
---
# Backend Architecture Rules

## Project Structure
- Follow Clean Architecture: Domain → Application → Infrastructure → API
- Keep Domain layer independent of other layers
- Use DTOs for data transfer between layers
- Implement Repository pattern with Unit of Work
- Use AutoMapper for object mapping

## Naming Conventions
- Controllers: Pascal<PERSON>ase with "Controller" suffix (e.g., `PropertyController`)
- Services: PascalCase with "Service" suffix (e.g., `AuthService`)
- DTOs: PascalCase with "Dto" suffix (e.g., `CreatePropertyDto`)
- Entities: PascalCase (e.g., `AppUser`, `Property`)
- Interfaces: PascalCase with "I" prefix (e.g., `IAuthService`)
- Methods: PascalCase (e.g., `GetUserByIdAsync`)
- Properties: PascalCase (e.g., `FullName`, `Email`)
- Constants: PascalCase (e.g., `DefaultMemberRank`)

## DTO Validation
- Use Data Annotations for all input DTOs
- Required fields: `[Required]` attribute
- String length: `[StringLength(max)]` or `[MaxLength(max)]`
- Email validation: `[EmailAddress]`
- Regular expressions: `[RegularExpression(pattern, errorMessage)]`
- Range validation: `[Range(min, max)]`

## Entity Validation
- Use `required` keyword for non-nullable properties
- Implement `IValidatableObject` for complex validation
- Use FluentValidation for complex business rules
- Validate at both DTO and Entity levels

## Example:
```csharp
public class CreatePropertyDto
{
    [Required]
    [StringLength(100)]
    public string Name { get; set; }
    
    [Required]
    [Range(0, double.MaxValue)]
    public decimal Price { get; set; }
    
    [EmailAddress]
    public string? ContactEmail { get; set; }
}
```

# Error Handling Rules

## Exception Handling
- Use custom exception types for business logic
- Implement global exception middleware
- Return consistent error responses
- Log all exceptions with context
- Use ProblemDetails for HTTP error responses

## Logging Standards
- Use structured logging with ILogger
- Include correlation IDs for request tracking
- Log security events separately
- Use appropriate log levels (Debug, Info, Warning, Error)
- Include user context in logs (userId, email, IP)

## Example:
```csharp
// In BaseController
protected void LogUserAction(ILogger logger, string action, object? parameters = null)
{
    var userId = GetUserId();
    var userEmail = GetUserEmail();
    var ipAddress = GetClientIpAddress();
    
    logger.LogInformation("User action: {Action} by User {UserId} ({UserEmail}) from IP {IpAddress}",
        action, userId, userEmail, ipAddress);
}
```

# Security Rules

## Authentication
- Use JWT tokens with proper expiration
- Implement refresh token mechanism
- Hash passwords with salt using SHA256
- Validate tokens on every request
- Use secure cookie settings

## Authorization
- Implement role-based authorization
- Use custom authorization policies
- Validate user permissions at service level
- Check user account status (IsActive)

## Input Validation
- Sanitize all user inputs
- Use parameterized queries
- Validate file uploads
- Implement rate limiting
- Use HTTPS in production

# Performance Rules

## Database
- Use async/await for all database operations
- Implement proper indexing
- Use Include() for eager loading
- Avoid N+1 query problems
- Use pagination for large datasets

## Caching
- Implement caching for frequently accessed data
- Use distributed caching for scalability
- Cache user sessions and permissions
- Implement cache invalidation strategies

## Background Services
- Use hosted services for background tasks
- Implement proper error handling in background jobs

- Use cancellation tokens for graceful shutdown