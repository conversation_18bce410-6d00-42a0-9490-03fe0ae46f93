---
--- 1. <PERSON><PERSON><PERSON><PERSON> bảng "Permission"
---
-- <PERSON><PERSON><PERSON> này sẽ lưu trữ định nghĩa của từng quyền hạn (e.g., 'approve_listing', 'view_accounting').

CREATE TABLE IF NOT EXISTS public."Permission"
(
    "Id" uuid NOT NULL DEFAULT uuid_generate_v4(),
    "Code" character varying(50) COLLATE pg_catalog."default" NOT NULL,
    "PermissionName" character varying(100) COLLATE pg_catalog."default" NOT NULL,
    "Description" text COLLATE pg_catalog."default",
    CONSTRAINT "Permission_pkey" PRIMARY KEY ("Id"),
    CONSTRAINT "Permission_Code_key" UNIQUE ("Code"),
    CONSTRAINT "Permission_PermissionName_key" UNIQUE ("PermissionName")
)

TABLESPACE pg_default;

ALTER TABLE IF EXISTS public."Permission"
    OWNER to postgres;

COMMENT ON TABLE public."Permission"
    IS 'Stores definitions of specific permissions within the system.';

COMMENT ON COLUMN public."Permission"."Code"
    IS 'Unique code for the permission (e.g., "LISTING_APPROVE", "VIEW_ACCOUNTING").';

COMMENT ON COLUMN public."Permission"."PermissionName"
    IS 'Human-readable name for the permission.';

COMMENT ON COLUMN public."Permission"."Description"
    IS 'Detailed description of what the permission allows.';


---
--- 2. Thêm bảng "RolePermission"
---
-- Bảng trung gian này sẽ liên kết các vai trò ("AdminRole") với các quyền ("Permission"),
-- tạo ra mối quan hệ nhiều-nhiều.

CREATE TABLE IF NOT EXISTS public."RolePermission"
(
    "Id" uuid NOT NULL DEFAULT uuid_generate_v4(),
    "RoleID" uuid NOT NULL,
    "PermissionID" uuid NOT NULL,
    CONSTRAINT "RolePermission_pkey" PRIMARY KEY ("Id"),
    CONSTRAINT "RolePermission_Unique_RoleID_PermissionID" UNIQUE ("RoleID", "PermissionID"), -- Đảm bảo mỗi cặp vai trò-quyền là duy nhất
    CONSTRAINT "RolePermission_RoleID_fkey" FOREIGN KEY ("RoleID")
        REFERENCES public."AdminRole" ("Id") MATCH SIMPLE
        ON UPDATE NO ACTION
        ON DELETE CASCADE, -- Khi vai trò bị xóa, các liên kết quyền của nó cũng bị xóa
    CONSTRAINT "RolePermission_PermissionID_fkey" FOREIGN KEY ("PermissionID")
        REFERENCES public."Permission" ("Id") MATCH SIMPLE
        ON UPDATE NO ACTION
        ON DELETE CASCADE -- Khi quyền bị xóa, các liên kết vai trò của nó cũng bị xóa
)

TABLESPACE pg_default;

ALTER TABLE IF EXISTS public."RolePermission"
    OWNER to postgres;

COMMENT ON TABLE public."RolePermission"
    IS 'Links AdminRoles to specific Permissions, defining what each role can do.';

COMMENT ON COLUMN public."RolePermission"."RoleID"
    IS 'Foreign key to the AdminRole table.';

COMMENT ON COLUMN public."RolePermission"."PermissionID"
    IS 'Foreign key to the Permission table.';
