﻿using RealEstate.Application.DTO;
using RealEstate.Application.DTO.Notification;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace RealEstate.Application.Interfaces
{
    public interface INotificationService
    {
        // Basic CRUD operations
        Task<NotificationDto> GetNotificationByIdAsync(Guid id);
        Task<bool> MarkAsReadAsync(Guid id, Guid userId);
        Task<bool> DeleteNotificationAsync(Guid id, Guid userId);
        
        // Enhanced operations
        Task<PagedResultDto<NotificationDto>> GetNotificationsForUserAsync(
            Guid userId, 
            DateTime? fromDate = null, 
            DateTime? toDate = null, 
            int page = 1, 
            int pageSize = 10);
            
        Task<PagedResultDto<NotificationDto>> GetNotificationsByTypeAsync(
            string type, 
            DateTime? fromDate = null, 
            DateTime? toDate = null, 
            int page = 1, 
            int pageSize = 10);
            
        Task<PagedResultDto<NotificationDto>> GetNotificationsByTypeAndUserAsync(
            string type, 
            Guid userId, 
            DateTime? fromDate = null, 
            DateTime? toDate = null, 
            int page = 1, 
            int pageSize = 10);
            
        Task<PagedResultDto<NotificationDto>> GetAllNotificationsForUserAsync(
            Guid userId, 
            DateTime? fromDate = null, 
            DateTime? toDate = null, 
            int page = 1, 
            int pageSize = 10);
            
        Task MarkAllAsReadAsync(Guid userId);
        Task<int> GetUnreadNotificationCountAsync(Guid userId);
        
        // Creates notification
        Task<NotificationDto> CreateNotificationAsync(CreateNotificationDto notificationDto);
        
        // Legacy method
        Task<IEnumerable<NotificationDto>> GetAllNotificationsAsync(Guid? userId);
    }
}
