-- Step 1: Rename the foreign key constraint that references AgentId
-- This is crucial to avoid issues when renaming the column itself.
ALTER TABLE IF EXISTS public."ContactRequest"
RENAME CONSTRAINT "ContactRequest_AgentId_fkey" TO "ContactRequest_PropertyOwnerId_fkey";

-- Step 2: Rename the column AgentId to PropertyOwnerId
ALTER TABLE IF EXISTS public."ContactRequest"
RENAME COLUMN "AgentId" TO "PropertyOwnerId";

-- Step 3: Rename the associated index for the old AgentId column
-- This ensures your indexes remain consistent with the new column name.
ALTER INDEX IF EXISTS public."IDX_ContactRequest_Agent"
RENAME TO "IDX_ContactRequest_PropertyOwner";

-- Optional: If you want to drop and recreate the foreign key to ensure correct naming,
-- though renaming the constraint usually suffices.
-- If you choose to drop and recreate, ensure you do it in this order:
-- DROP CONSTRAINT "ContactRequest_AgentId_fkey"; (if not renamed in step 1)
-- ALTER TABLE public."ContactRequest" RENAME COLUMN "AgentId" TO "PropertyOwnerId";
-- ADD CONSTRAINT "ContactRequest_PropertyOwnerId_fkey" FOREIGN KEY ("PropertyOwnerId")
--     REFERENCES public."AppUser" ("Id") MATCH SIMPLE
--     ON UPDATE NO ACTION
--     ON DELETE CASCADE;

-- Optional: You might want to update the comment if you use them for documentation
COMMENT ON COLUMN public."ContactRequest"."PropertyOwnerId"
    IS 'ID of the property owner or agent associated with the request';