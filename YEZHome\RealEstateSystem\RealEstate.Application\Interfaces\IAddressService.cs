﻿using RealEstate.Application.DTO;

namespace RealEstate.Application.Interfaces
{
    public interface IAddressService
    {
        Task<IEnumerable<CityDto>> GetCitiesAsync();
        Task<IEnumerable<DistrictDto>> GetDistrictByCityAsync(int cityId);
        Task<IEnumerable<WardDto>> GetWardsByDistrictAsync(int districtId);
        Task<IEnumerable<StreetDto>> GetStreetByWardAsync(int wardId);
        Task<IEnumerable<ProjectDto>> GetProjectsByWardStreetAsync(int wardId, int streetId);
    }
}
