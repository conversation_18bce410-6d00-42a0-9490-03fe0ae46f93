﻿using System.ComponentModel.DataAnnotations;

namespace RealEstate.Application.DTO
{
    public class CreateBlogPostDto
    {
        [Required]
        public Guid AuthorID { get; set; }

        [Required]
        [StringLength(100)]
        public string Title { get; set; }

        [Required]
        public string Content { get; set; }

        public string FeaturedImage { get; set; }
        public string Tags { get; set; }
        public string Status { get; set; }
        public DateTime? PublishedAt { get; set; }
    }
}
