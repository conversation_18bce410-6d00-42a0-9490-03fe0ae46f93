 
Okay, I will now generate the technical design document for the multi-language feature using `next-intl`.

I will create the file `document/multi-language/multi-language.md` and populate it with the design details.

```markdown:document/multi-language/multi-language.md
# Technical Design Document: Multi-Language Support

## 1. Overview

This document outlines the technical design for implementing multi-language support (English and Vietnamese) in the YEZHome frontend application. The goal is to allow users to view the user interface text in their preferred language, selected either via URL path or a language switcher component.

## 2. Requirements

### 2.1 Functional Requirements

*   **FR1:** The application must support Vietnamese (`vi`) and English (`en`) languages for all UI text elements (labels, buttons, static text, etc.).
*   **FR2:** The active language must be determined by a locale prefix in the URL path (e.g., `/en/dashboard`, `/vi/dashboard`).
*   **FR3:** The application must redirect users to a default locale path (e.g., `/en`) if no locale is specified in the URL. The default locale will be English.
*   **FR4:** A language switcher dropdown component must be available (e.g., in the Navbar) allowing users to manually select their preferred language. default is Vietnamese
*   **FR5:** Selecting a language from the dropdown must update the URL locale prefix and refresh the UI with the corresponding translations.

### 2.2 Non-Functional Requirements

*   **NFR1:** Translations must be stored in JSON files within the project repository (`locales/en.json`, `locales/vi.json`).
*   **NFR2:** The implementation should leverage the `next-intl` library for internationalization (i18n) due to its strong integration with the Next.js App Router.
*   **NFR3:** Only static UI text needs translation. Dynamic content fetched from the API is out of scope for this phase.
*   **NFR4:** Date, number, and currency formatting localization are out of scope for this initial implementation.

## 3. Technical Design

### 3.1. Data Model Changes

*   No database schema changes are required. Translations will be managed in static JSON files.

### 3.2. UI Changes

*   **Language Switcher Component:**
    *   A new reusable component, `LanguageSwitcher.jsx`, will be created in `components/ui/`.
    *   This component will display a dropdown menu (using `shadcn/ui` DropdownMenu) showing available languages ("English", "Tiếng Việt").
    *   On selection, it will use `next-intl`'s navigation utilities (`useRouter`, `usePathname`) to redirect the user to the same page but with the selected locale prefix in the URL.
*   **Navbar Integration:**
    *   The `LanguageSwitcher` component will be integrated into the `components/layout/Navbar.jsx`.
*   **Component Text:**
    *   All components containing hardcoded UI text (e.g., `components/layout/NoData.jsx`, `components/ui/SearchFilter.jsx`, `app/page.jsx`, etc.) will be refactored.
    *   Hardcoded strings will be replaced with calls to the `useTranslations` hook provided by `next-intl` (e.g., `const t = useTranslations('ComponentName'); t('buttonLabel')`).

### 3.3. Logic Flow

*   **Initialization & Routing:**
    *   `next-intl` middleware (`src/middleware.js` or `middleware.js`) will handle incoming requests.
    *   It will detect the locale from the URL prefix. If missing, it redirects to the default locale (`vi`).
    *   It ensures all routing automatically includes the locale prefix.
*   **Translation Loading:**
    *   A `[locale]` dynamic segment will be added to the main layout (`app/[locale]/layout.jsx`).
    *   A `NextIntlClientProvider` will wrap the application layout, receiving messages (translations) fetched server-side based on the current locale.
    *   A `src/i18n.js` (or similar) file will configure `next-intl`, specifying locales and how to load translation messages (using dynamic imports for the JSON files).
*   **Language Switching:**
    1.  User clicks the `LanguageSwitcher` dropdown.
    2.  User selects a new language.
    3.  The `onClick` handler in `LanguageSwitcher` uses `next-intl`'s `useRouter` to navigate to the current `pathname` but with the new locale prefix.
    4.  The middleware intercepts, Next.js re-renders the page with the new `[locale]` parameter.
    5.  The `NextIntlClientProvider` receives the updated messages for the new locale.
    6.  Components re-render using the `useTranslations` hook, displaying text in the newly selected language.

```mermaid
sequenceDiagram
    participant User
    participant Browser
    participant LanguageSwitcher
    participant next-intl Middleware
    participant Next.js Router
    participant Page/Layout Components
    participant Translation Files (JSON)

    User->>Browser: Accesses /dashboard
    Browser->>next-intl Middleware: Request /dashboard
    next-intl Middleware->>Browser: Redirect to /en/dashboard (default locale)
    Browser->>next-intl Middleware: Request /en/dashboard
    next-intl Middleware->>Next.js Router: Process /en/dashboard
    Next.js Router->>Translation Files (JSON): Load en.json
    Next.js Router->>Page/Layout Components: Render with 'en' locale and translations
    Page/Layout Components->>Browser: Display page in English
    Browser->>User: Show English page

    User->>LanguageSwitcher: Click and select 'Vietnamese'
    LanguageSwitcher->>Next.js Router: Navigate to /vi/dashboard
    Browser->>next-intl Middleware: Request /vi/dashboard
    next-intl Middleware->>Next.js Router: Process /vi/dashboard
    Next.js Router->>Translation Files (JSON): Load vi.json
    Next.js Router->>Page/Layout Components: Render with 'vi' locale and translations
    Page/Layout Components->>Browser: Display page in Vietnamese
    Browser->>User: Show Vietnamese page
```

### 3.4. Dependencies

*   Add the `next-intl` package: `npm install next-intl`

### 3.5. Project Structure Changes

*   Create `locales/en.json`
*   Create `locales/vi.json`
*   Create `src/i18n.js` (or `i18n.js` in root) for `next-intl` configuration.
*   Create or modify `middleware.js` to include `next-intl` middleware.
*   Modify `app/layout.jsx` to `app/[locale]/layout.jsx`.
*   Create `components/ui/LanguageSwitcher.jsx`.

### 3.6. Security Considerations

*   Translation files (`.json`) contain only static UI text and are sourced locally, minimizing risks like XSS injection through translations. Ensure no user-generated content is directly used as translation keys.

### 3.7. Performance Considerations

*   `next-intl` is designed for performance, typically loading only the necessary locale data for the current page.
*   Initial bundle size might slightly increase due to the library and configuration.
*   Loading translation files dynamically per request is efficient.

## 4. Testing Plan

*   **Unit Tests:**
    *   Test the `LanguageSwitcher` component to ensure it renders correctly and triggers navigation with the correct locale parameter upon selection.
*   **Integration Tests:**
    *   Verify that the `next-intl` middleware correctly redirects requests without a locale to the default locale (`/en`).
    *   Test that accessing a URL with a specific locale prefix (e.g., `/vi/some-page`) loads the correct translations.
    *   Test the language switching flow end-to-end: clicking the switcher, navigating, and verifying UI text updates.
*   **User Acceptance Tests (UAT):**
    *   Manually navigate through all key pages of the application in both English and Vietnamese.
    *   Verify all UI text elements are correctly translated.
    *   Confirm the language switcher works as expected across different pages.
    *   Check that URLs correctly reflect the selected language.

## 5. Open Questions

*   None at this time based on the provided requirements.

## 6. Alternatives Considered

*   **`react-i18next`:** A popular and mature i18n library for React. However, `next-intl` provides more seamless integration specifically tailored for the Next.js App Router, including middleware and server-side message loading, making it the preferred choice for this project.

```
I have created the technical design document at `document/multi-language/multi-language.md`. It details the plan for implementing multi-language support using `next-intl`, including requirements, UI changes, logic flow, dependencies, and testing strategy.

Please review the document. Let me know if you'd like me to proceed with the implementation based on this plan, or if you have any modifications.
