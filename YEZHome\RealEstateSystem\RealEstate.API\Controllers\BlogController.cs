﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using RealEstate.Application.DTO;
using RealEstate.Application.Interfaces;
using RealEstate.Application.Services;
using RealEstate.Domain.CustomModel;
using RealEstate.Domain.Entities;

namespace RealEstate.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class BlogController : BaseController
    {
        private readonly IBlogService _blogPostService;
        private readonly ILogger<BlogController> _logger;

        public BlogController(IBlogService blogPostService, ILogger<BlogController> logger)
        {
            _blogPostService = blogPostService;
            _logger = logger;
        }

        [AllowAnonymous]
        [HttpGet("slug/{slug}")]
        public async Task<ActionResult<BlogPostDto>> GetBlogPostBySlug(string slug)
        {
            try
            {
                _logger.LogInformation("Retrieving blog post by slug: {Slug} from IP {IpAddress}", slug, GetClientIpAddress());

                var blogPost = await _blogPostService.GetBlogBySlugAsync(slug);
                if (blogPost == null)
                {
                    _logger.LogWarning("Blog post not found for slug: {Slug}", slug);
                    return NotFound(new { Message = "Blog post not found" });
                }

                _logger.LogInformation("Successfully retrieved blog post {BlogPostId} with slug: {Slug}", blogPost.Id, slug);
                return Ok(blogPost);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving blog post by slug: {Slug}", slug);
                return StatusCode(500, new { Message = "An error occurred while retrieving the blog post. Please try again later." });
            }
        }

        [HttpGet]
        [AllowAnonymous]
        public async Task<ActionResult<IEnumerable<BlogPostDto>>> GetAllBlogPost()
        {
            try
            {
                _logger.LogInformation("Retrieving all blog posts from IP {IpAddress}", GetClientIpAddress());

                var result = await _blogPostService.GetAllBlogAsync();

                _logger.LogInformation("Successfully retrieved {Count} blog posts", result?.Count() ?? 0);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving all blog posts");
                return StatusCode(500, new { Message = "An error occurred while retrieving blog posts. Please try again later." });
            }
        }

        [HttpGet("blog-posts")]
        [AllowAnonymous]
        public async Task<IActionResult> GetBlogPosts([FromQuery] PagingRequest request, [FromQuery] string? title)
        {
            try
            {
                _logger.LogInformation("Retrieving blog posts with paging - Page: {Page}, PageSize: {PageSize}, Title: {Title} from IP {IpAddress}",
                    request.PageNumber, request.PageSize, title, GetClientIpAddress());

                var result = await _blogPostService.GetBlogAsync(request, title ?? string.Empty);

                _logger.LogInformation("Successfully retrieved paged blog posts");
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving paged blog posts with title filter: {Title}", title);
                return StatusCode(500, new { Message = "An error occurred while retrieving blog posts. Please try again later." });
            }
        }
    }
}
