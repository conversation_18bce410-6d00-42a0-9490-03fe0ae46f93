/*--------------------------------------
URL: /Geocode

Method: GET

The Goong Geocoding API does three things:
1. Forward geocoding converts location text into geographic coordinates, turning 91 Trung <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>à <PERSON> into 21.0137443130001,105.798346108
2. Reverse geocoding turns geographic coordinates into place names, turning 21.0137443130001,105.798346108 into 91 Trung <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Hà Nội.
3. Get place detail by place_id
--------------------------------------*/

export async function GET(req) {
  const { searchParams } = new URL(req.url);
  const place_id = searchParams.get("place_id");
  const address = searchParams.get("address");
  const latlng = searchParams.get("latlng");

  if (!place_id && !address && !latlng) {
    return Response.json({ error: "Missing data to get place details" }, { status: 400 });
  }

  try {

    let apiURL = `https://rsapi.goong.io/geocode?api_key=${process.env.GOONG_GEO_API_KEY}`;

    if (place_id) {
      apiURL += `&place_id=${encodeURIComponent(place_id)}`;
    }
    if (address) {
      apiURL += `&address=${encodeURIComponent(address)}`;
    }
    if (latlng) {
      apiURL += `&latlng=${encodeURIComponent(latlng)}`;
    }

    const response = await fetch(apiURL);

    if (!response.ok) {
      throw new Error("Failed to fetch place details");
    }

    const data = await response.json();
    return Response.json(data, { status: 200 });
  } catch (error) {
    return Response.json({ error: error.message }, { status: 500 });
  }
}