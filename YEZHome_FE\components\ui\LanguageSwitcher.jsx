'use client';

import * as React from "react";
import { useLocale } from "next-intl";
import { createNavigation } from "next-intl/navigation";
import { Globe } from "lucide-react";

import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { routing } from "@/i18n/routing";

export function LanguageSwitcher() {
  const { usePathname, useRouter } = createNavigation(routing);
  const pathname = usePathname();
  const locale = useLocale();
  const router = useRouter();

  const changeLocale = (newLocale) => {
    // Use replace to avoid adding a new entry to the history stack
    router.replace(pathname, { locale: newLocale });
  };

  // Get the current language name
  const getCurrentLanguageName = () => {
    return locale === 'en' ? 'En' : 'Vi';
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="outline" className="font-montserrat text-sm font-medium hover:text-teal-600 transition-colors p-2">
          <Globe className="w-4 h-4"/>{getCurrentLanguageName()}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuItem
          onClick={() => changeLocale('en')}
          disabled={locale === 'en'}
        >
          English
        </DropdownMenuItem>
        <DropdownMenuItem
          onClick={() => changeLocale('vi')}
          disabled={locale === 'vi'}
        >
          Tiếng Việt
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
} 