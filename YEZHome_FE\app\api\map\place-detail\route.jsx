export async function GET(req) {
  const { searchParams } = new URL(req.url);
  const place_id = searchParams.get("place_id");

  if (!place_id) {
    return Response.json({ error: "Missing place_id" }, { status: 400 });
  }

  try {
    const response = await fetch(
      `https://rsapi.goong.io/Place/Detail?api_key=${process.env.GOONG_GEO_API_KEY}&place_id=${place_id}`
    );

    if (!response.ok) {
      throw new Error("Failed to fetch place details");
    }

    const data = await response.json();
    return Response.json(data, { status: 200 });
  } catch (error) {
    return Response.json({ error: error.message }, { status: 500 });
  }
}