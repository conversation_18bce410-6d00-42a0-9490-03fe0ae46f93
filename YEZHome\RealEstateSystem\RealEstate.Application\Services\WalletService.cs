using AutoMapper;
using Microsoft.EntityFrameworkCore;
using OfficeOpenXml;
using OfficeOpenXml.Style;
using RealEstate.Application.DTO;
using RealEstate.Application.Interfaces;
using RealEstate.Domain.Entities;
using RealEstate.Domain.Interfaces;
using System.Drawing;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using RealEstate.Domain.Common;
using System.Globalization;
using RealEstate.Application.DTO.Wallet;

namespace RealEstate.Application.Services
{
    public class WalletService : IWalletService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly IMapper _mapper;

        public WalletService(IUnitOfWork unitOfWork, IMapper mapper)
        {
            _unitOfWork = unitOfWork;
            _mapper = mapper;
        }

        public async Task<WalletBalanceDto> GetWalletBalanceAsync(Guid userId)
        {
            var user = await _unitOfWork.AppUsers.GetByIdAsync(userId);
            if (user == null)
            {
                throw new KeyNotFoundException($"User with ID {userId} not found");
            }

            var wallet = await _unitOfWork.Wallets.GetQueryable()
                .FirstOrDefaultAsync(w => w.UserId == userId);

            if (wallet == null)
            {
                // Create wallet if it doesn't exist
                wallet = new Wallet
                {
                    UserId = userId,
                    Balance = 0,
                    User = user
                };

                await _unitOfWork.Wallets.AddAsync(wallet);
                await _unitOfWork.SaveChangesAsync();
            }

            return new WalletBalanceDto
            {
                Balance = wallet.Balance,
                TotalSpent = user.TotalSpent
            };
        }

        public async Task<WalletTransactionDto> TopUpWalletAsync(Guid userId, TopUpWalletDto request)
        {
            if (request.Amount <= 0)
            {
                throw new ArgumentException("Top-up amount must be greater than zero");
            }

            var user = await _unitOfWork.AppUsers.GetByIdAsync(userId);
            if (user == null)
            {
                throw new KeyNotFoundException($"User not found");
            }

            var wallet = await _unitOfWork.Wallets.GetQueryable()
                .FirstOrDefaultAsync(w => w.UserId == userId);

            if (wallet == null)
            {
                // Create wallet if it doesn't exist
                wallet = new Wallet
                {
                    UserId = userId,
                    Balance = 0,
                    User = user
                };

                await _unitOfWork.Wallets.AddAsync(wallet);
            }

            var transDate = DateTime.UtcNow;
            var transaction = new WalletTransaction
            {
                UserId = userId,
                Amount = request.Amount,
                Type = EnumValues.TransactionType.TOP_UP.ToString(),
                Status = EnumValues.TransactionStatus.PENDING.ToString(),
                PaymentMethod = request.PaymentMethod,
                Description = $"Top-up via {request.PaymentMethod}: {request.Amount.ToString("#,#", CultureInfo.CreateSpecificCulture("vi-VN"))} at {transDate.ToString("dd/MM/yyyy HH:mm:ss")}.",
                CreatedAt = transDate
            };

            await _unitOfWork.WalletTransactions.AddAsync(transaction);
            await _unitOfWork.SaveChangesAsync();

            return _mapper.Map<WalletTransactionDto>(transaction);
        }

        public async Task<WalletTransactionDto> SpendFromWalletAsync(Guid userId, SpendWalletDto request)
        {
            if (request.Amount <= 0)
            {
                throw new ArgumentException("Payment amount must be greater than zero");
            }

            var user = await _unitOfWork.AppUsers.GetByIdAsync(userId);
            if (user == null)
            {
                throw new KeyNotFoundException($"User not found");
            }

            var wallet = await _unitOfWork.Wallets.GetQueryable()
                .FirstOrDefaultAsync(w => w.UserId == userId);

            if (wallet == null || wallet.Balance < request.Amount)
            {
                throw new InvalidOperationException("Insufficient funds in wallet");
            }

            if (request.PaymentType != EnumValues.TransactionType.PAYMENT_POST.ToString() && request.PaymentType != EnumValues.TransactionType.PAYMENT_HIGHLIGHT.ToString())
            {
                throw new InvalidOperationException("Payment type not correct");
            }

            // Create transaction record (initially in pending state)
            var transaction = new WalletTransaction
            {
                UserId = userId,
                Amount = request.Amount,
                Type = request.PaymentType,
                Status = EnumValues.TransactionStatus.PENDING.ToString(),
                Description = request.Description ?? string.Empty,
                CreatedAt = DateTime.UtcNow
                // Don't set User navigation property to avoid duplicate key error
            };

            await _unitOfWork.WalletTransactions.AddAsync(transaction);
            await _unitOfWork.SaveChangesAsync();

            return _mapper.Map<WalletTransactionDto>(transaction);
        }

        public async Task<IEnumerable<WalletTransactionDto>> GetWalletTransactionsAsync(Guid userId, int page = 1, int pageSize = 20)
        {
            var transactions = await _unitOfWork.WalletTransactions.GetQueryable()
                .Where(t => t.UserId == userId)
                .OrderByDescending(t => t.CreatedAt)
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();

            return _mapper.Map<IEnumerable<WalletTransactionDto>>(transactions);
        }

        public async Task<WalletTransactionDto> GetTransactionByIdAsync(Guid transactionId, Guid userId)
        {
            var transaction = await _unitOfWork.WalletTransactions.GetQueryable()
                .FirstOrDefaultAsync(t => t.Id == transactionId && t.UserId == userId);

            if (transaction == null)
            {
                return null;
            }

            return _mapper.Map<WalletTransactionDto>(transaction);
        }

        public async Task<WalletTransactionDto> ProcessTransactionAsync(Guid transactionId, ProcessTransactionDto request, Guid processedByUserId)
        {
            var transaction = await _unitOfWork.WalletTransactions.GetByIdAsync(transactionId);
            if (transaction == null)
            {
                throw new KeyNotFoundException($"Transaction with ID {transactionId} not found");
            }

            if (transaction.Status != "pending")
            {
                throw new InvalidOperationException($"Transaction is already {transaction.Status} and cannot be processed");
            }

            var user = await _unitOfWork.AppUsers.GetByIdAsync(transaction.UserId);
            if (user == null)
            {
                throw new KeyNotFoundException($"User with ID {transaction.UserId} not found");
            }

            var wallet = await _unitOfWork.Wallets.GetQueryable()
                .FirstOrDefaultAsync(w => w.UserId == transaction.UserId);

            if (wallet == null)
            {
                throw new InvalidOperationException("User wallet not found");
            }

            switch (request.Action.ToLower())
            {
                case "complete":
                    // Process the transaction based on its type
                    if (transaction.Type == "deposit")
                    {
                        // Add funds to wallet
                        wallet.Balance += transaction.Amount;
                    }
                    else if (transaction.Type == "spend")
                    {
                        // Check if there are sufficient funds
                        if (wallet.Balance < transaction.Amount)
                        {
                            throw new InvalidOperationException("Insufficient funds in wallet");
                        }

                        // Deduct funds from wallet
                        wallet.Balance -= transaction.Amount;

                        // Update user's total spent
                        user.TotalSpent += transaction.Amount;

                        // Check if user ranking needs to be updated
                        await UpdateUserRankingAsync(user);
                    }

                    transaction.Status = "completed";
                    transaction.ProcessedAt = DateTime.UtcNow;
                    break;

                case "fail":
                    transaction.Status = "failed";
                    transaction.FailureReason = request.FailureReason;
                    transaction.ProcessedAt = DateTime.UtcNow;
                    break;

                case "cancel":
                    transaction.Status = "cancelled";
                    transaction.ProcessedAt = DateTime.UtcNow;
                    break;

                default:
                    throw new ArgumentException($"Invalid action: {request.Action}");
            }

            _unitOfWork.WalletTransactions.Update(transaction);
            await _unitOfWork.SaveChangesAsync();

            return _mapper.Map<WalletTransactionDto>(transaction);
        }

        public async Task<IEnumerable<WalletTransactionDto>> GetPendingTransactionsAsync(int page = 1, int pageSize = 20)
        {
            var transactions = await _unitOfWork.WalletTransactions.GetQueryable()
                .Where(t => t.Status == "pending")
                .OrderBy(t => t.CreatedAt)
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();

            return _mapper.Map<IEnumerable<WalletTransactionDto>>(transactions);
        }

        public async Task<IEnumerable<WalletTransactionDto>> GetUserPendingTransactionsAsync(Guid userId, int page = 1, int pageSize = 20)
        {
            var transactions = await _unitOfWork.WalletTransactions.GetQueryable()
                .Where(t => t.UserId == userId && t.Status == "pending")
                .OrderBy(t => t.CreatedAt)
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();

            return _mapper.Map<IEnumerable<WalletTransactionDto>>(transactions);
        }

        public async Task<TransactionSearchResultDto> SearchTransactionsAsync(TransactionSearchCriteriaDto criteria, Guid? userId = null)
        {
            var query = _unitOfWork.WalletTransactions.GetQueryable();

            // Apply user filter if provided
            if (userId.HasValue)
            {
                query = query.Where(t => t.UserId == userId.Value);
            }

            // Apply date filters
            if (criteria.StartDate.HasValue)
            {
                var startDate = criteria.StartDate.Value.Date;
                query = query.Where(t => t.CreatedAt >= startDate);
            }

            if (criteria.EndDate.HasValue)
            {
                var endDate = criteria.EndDate.Value.Date.AddDays(1).AddSeconds(-1); // End of the day
                query = query.Where(t => t.CreatedAt <= endDate);
            }

            // Apply status filter
            if (!string.IsNullOrEmpty(criteria.Status))
            {
                query = query.Where(t => t.Status == criteria.Status);
            }

            // Apply payment method filter
            if (!string.IsNullOrEmpty(criteria.PaymentMethod))
            {
                query = query.Where(t => t.PaymentMethod == criteria.PaymentMethod);
            }

            // Apply transaction type filter
            if (!string.IsNullOrEmpty(criteria.Type))
            {
                query = query.Where(t => t.Type == criteria.Type);
            }

            // Apply amount range filters
            if (criteria.MinAmount.HasValue)
            {
                query = query.Where(t => t.Amount >= criteria.MinAmount.Value);
            }

            if (criteria.MaxAmount.HasValue)
            {
                query = query.Where(t => t.Amount <= criteria.MaxAmount.Value);
            }

            // Apply search term filter (search in description or transaction reference)
            if (!string.IsNullOrEmpty(criteria.SearchTerm))
            {
                var searchTerm = criteria.SearchTerm.ToLower();
                query = query.Where(t =>
                    (t.Description != null && t.Description.ToLower().Contains(searchTerm)) ||
                    (t.TransactionReference != null && t.TransactionReference.ToLower().Contains(searchTerm)));
            }

            // Get total count for pagination
            var totalCount = await query.CountAsync();

            // Calculate total amount
            var totalAmount = await query.SumAsync(t => t.Amount);

            // Apply pagination
            var transactions = await query
                .OrderByDescending(t => t.CreatedAt)
                .Skip((criteria.Page - 1) * criteria.PageSize)
                .Take(criteria.PageSize)
                .ToListAsync();

            // Calculate total pages
            var totalPages = (int)Math.Ceiling(totalCount / (double)criteria.PageSize);

            return new TransactionSearchResultDto
            {
                Transactions = _mapper.Map<IEnumerable<WalletTransactionDto>>(transactions),
                TotalCount = totalCount,
                TotalPages = totalPages,
                CurrentPage = criteria.Page,
                TotalAmount = totalAmount
            };
        }

        public async Task<byte[]> ExportTransactionsToExcelAsync(TransactionSearchCriteriaDto criteria, Guid? userId = null)
        {
            // First, get the search results (without pagination to export all matching records)
            var searchCriteria = new TransactionSearchCriteriaDto
            {
                StartDate = criteria.StartDate,
                EndDate = criteria.EndDate,
                Status = criteria.Status,
                PaymentMethod = criteria.PaymentMethod,
                Type = criteria.Type,
                MinAmount = criteria.MinAmount,
                MaxAmount = criteria.MaxAmount,
                SearchTerm = criteria.SearchTerm,
                Page = 1,
                PageSize = int.MaxValue // Get all records matching the criteria
            };

            var query = _unitOfWork.WalletTransactions.GetQueryable();

            // Apply user filter if provided
            if (userId.HasValue)
            {
                query = query.Where(t => t.UserId == userId.Value);
            }

            // Apply date filters
            if (criteria.StartDate.HasValue)
            {
                var startDate = criteria.StartDate.Value.Date;
                query = query.Where(t => t.CreatedAt >= startDate);
            }

            if (criteria.EndDate.HasValue)
            {
                var endDate = criteria.EndDate.Value.Date.AddDays(1).AddSeconds(-1); // End of the day
                query = query.Where(t => t.CreatedAt <= endDate);
            }

            // Apply status filter
            if (!string.IsNullOrEmpty(criteria.Status))
            {
                query = query.Where(t => t.Status == criteria.Status);
            }

            // Apply payment method filter
            if (!string.IsNullOrEmpty(criteria.PaymentMethod))
            {
                query = query.Where(t => t.PaymentMethod == criteria.PaymentMethod);
            }

            // Apply transaction type filter
            if (!string.IsNullOrEmpty(criteria.Type))
            {
                query = query.Where(t => t.Type == criteria.Type);
            }

            // Apply amount range filters
            if (criteria.MinAmount.HasValue)
            {
                query = query.Where(t => t.Amount >= criteria.MinAmount.Value);
            }

            if (criteria.MaxAmount.HasValue)
            {
                query = query.Where(t => t.Amount <= criteria.MaxAmount.Value);
            }

            // Apply search term filter (search in description or transaction reference)
            if (!string.IsNullOrEmpty(criteria.SearchTerm))
            {
                var searchTerm = criteria.SearchTerm.ToLower();
                query = query.Where(t =>
                    (t.Description != null && t.Description.ToLower().Contains(searchTerm)) ||
                    (t.TransactionReference != null && t.TransactionReference.ToLower().Contains(searchTerm)));
            }

            // Get transactions with user information
            var transactions = await query
                .OrderByDescending(t => t.CreatedAt)
                .Include(t => t.User)
                .ToListAsync();

            // Create Excel package
            ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
            using var package = new ExcelPackage();

            // Add a worksheet
            var worksheet = package.Workbook.Worksheets.Add("Transactions");

            // Add headers
            worksheet.Cells[1, 1].Value = "Transaction ID";
            worksheet.Cells[1, 2].Value = "User";
            worksheet.Cells[1, 3].Value = "Type";
            worksheet.Cells[1, 4].Value = "Amount";
            worksheet.Cells[1, 5].Value = "Status";
            worksheet.Cells[1, 6].Value = "Payment Method";
            worksheet.Cells[1, 7].Value = "Reference";
            worksheet.Cells[1, 8].Value = "Description";
            worksheet.Cells[1, 9].Value = "Created At";
            worksheet.Cells[1, 10].Value = "Processed At";

            // Style the header row
            using (var range = worksheet.Cells[1, 1, 1, 10])
            {
                range.Style.Font.Bold = true;
                range.Style.Fill.PatternType = ExcelFillStyle.Solid;
                range.Style.Fill.BackgroundColor.SetColor(Color.LightGray);
                range.Style.Border.Bottom.Style = ExcelBorderStyle.Medium;
            }

            // Add data rows
            for (int i = 0; i < transactions.Count; i++)
            {
                var transaction = transactions[i];
                int row = i + 2; // Start from row 2 (after header)

                worksheet.Cells[row, 1].Value = transaction.Id.ToString();
                worksheet.Cells[row, 2].Value = transaction.User?.FullName ?? "Unknown";
                worksheet.Cells[row, 3].Value = transaction.Type;
                worksheet.Cells[row, 4].Value = transaction.Amount;
                worksheet.Cells[row, 5].Value = transaction.Status;
                worksheet.Cells[row, 6].Value = transaction.PaymentMethod;
                worksheet.Cells[row, 7].Value = transaction.TransactionReference;
                worksheet.Cells[row, 8].Value = transaction.Description;
                worksheet.Cells[row, 9].Value = transaction.CreatedAt;
                worksheet.Cells[row, 10].Value = transaction.ProcessedAt;

                // Format the amount column
                worksheet.Cells[row, 4].Style.Numberformat.Format = "#,##0.00";

                // Format the date columns
                worksheet.Cells[row, 9].Style.Numberformat.Format = "yyyy-mm-dd hh:mm:ss";
                worksheet.Cells[row, 10].Style.Numberformat.Format = "yyyy-mm-dd hh:mm:ss";

                // Highlight rows based on transaction type
                if (transaction.Type == "deposit")
                {
                    worksheet.Cells[row, 3].Style.Font.Color.SetColor(Color.Green);
                    worksheet.Cells[row, 4].Style.Font.Color.SetColor(Color.Green);
                }
                else if (transaction.Type == "spend")
                {
                    worksheet.Cells[row, 3].Style.Font.Color.SetColor(Color.Red);
                    worksheet.Cells[row, 4].Style.Font.Color.SetColor(Color.Red);
                }

                // Highlight rows based on status
                if (transaction.Status == "completed")
                {
                    worksheet.Cells[row, 5].Style.Font.Color.SetColor(Color.Green);
                }
                else if (transaction.Status == "failed")
                {
                    worksheet.Cells[row, 5].Style.Font.Color.SetColor(Color.Red);
                }
                else if (transaction.Status == "pending")
                {
                    worksheet.Cells[row, 5].Style.Font.Color.SetColor(Color.Orange);
                }
            }

            // Add a summary row
            int summaryRow = transactions.Count + 3;
            worksheet.Cells[summaryRow, 1].Value = "Total:";
            worksheet.Cells[summaryRow, 4].Formula = $"SUM(D2:D{transactions.Count + 1})";
            worksheet.Cells[summaryRow, 4].Style.Font.Bold = true;
            worksheet.Cells[summaryRow, 4].Style.Numberformat.Format = "#,##0.00";

            // Auto-fit columns
            worksheet.Cells.AutoFitColumns();

            // Return the Excel package as a byte array
            return package.GetAsByteArray();
        }

        private async Task UpdateUserRankingAsync(AppUser user)
        {
            // Get all member rankings ordered by minimum spent
            var rankings = await _unitOfWork.MemberRankings.GetQueryable()
                .OrderByDescending(r => r.MinSpent)
                .ToListAsync();

            // Find the highest rank the user qualifies for
            var newRank = rankings
                .Where(r => r.MinSpent.HasValue && r.MinSpent <= user.TotalSpent)
                .OrderByDescending(r => r.MinSpent)
                .FirstOrDefault();

            if (newRank != null && newRank.RankName != user.MemberRank)
            {
                // Update the user's rank
                user.MemberRank = newRank.RankName;
            }
        }
    }
}