using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace RealEstate.Domain.Entities
{
    public class PropertySpendingLog : BaseEntity
    {
        [Required]
        public Guid PropertyId { get; set; }
        
        [Required]
        public Guid UserId { get; set; }
        
        [Required]
        [Column(TypeName = "numeric(20,2)")]
        public decimal Amount { get; set; }
        
        [Required]
        [StringLength(50)]
        public string SpendingType { get; set; } // "extension", "highlight", etc.
        
        public Guid? TransactionId { get; set; }
        
        [Required]
        public DateTime SpentAt { get; set; } = DateTime.UtcNow;
        
        public string? Details { get; set; } // JSON string
        
        // Navigation properties
        [ForeignKey("PropertyId")]
        public Property? Property { get; set; }
        
        [ForeignKey("UserId")]
        public AppUser? User { get; set; }
        
        [ForeignKey("TransactionId")]
        public WalletTransaction? Transaction { get; set; }
    }
}
