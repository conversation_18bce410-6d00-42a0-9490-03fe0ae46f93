import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Mail, Phone, MapPin, Shield, Search } from "lucide-react"
import { getTranslations } from "next-intl/server"

export default async function AboutUs() {
  const t = await getTranslations('AboutPage')

  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <section className="relative overflow-hidden bg-gradient-to-b from-rose-50 to-white px-6 py-24">
        <div className="mx-auto max-w-5xl text-center">
          <h1 className="mb-6 text-4xl font-bold tracking-tight text-gray-900 md:text-5xl">{t('heroTitle')}</h1>
          <p className="mx-auto max-w-3xl text-lg text-gray-600">
            {t('heroSubtitle')}
          </p>
        </div>
      </section>

      {/* Main Content */}
      <section className="px-6 py-16">
        <div className="mx-auto max-w-5xl space-y-16">
          {/* Mission */}
          <div className="grid gap-12 lg:grid-cols-2 lg:items-center">
            <div className="space-y-6">
              <h2 className="text-2xl font-semibold text-gray-900">{t('missionTitle')}</h2>
              <p className="text-gray-600 whitespace-pre-line">
                {t('missionText')}
              </p>
            </div>
            <div className="aspect-video overflow-hidden rounded-xl bg-gray-100">
              <img
                src="/placeholder.svg?height=300&width=500"
                alt={t('missionTitle')}
                className="h-full w-full object-cover"
              />
            </div>
          </div>

          {/* Features */}
          <div className="grid gap-8 sm:grid-cols-2 lg:grid-cols-3">
            <Card>
              <CardContent className="pt-6">
                <div className="mb-4 inline-flex rounded-lg bg-rose-100 p-3 text-rose-600">
                  <MapPin className="h-6 w-6" />
                </div>
                <h3 className="mb-2 font-semibold">{t('feature1Title')}</h3>
                <p className="text-sm text-gray-600">
                  {t('feature1Desc')}
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="pt-6">
                <div className="mb-4 inline-flex rounded-lg bg-rose-100 p-3 text-rose-600">
                  <Shield className="h-6 w-6" />
                </div>
                <h3 className="mb-2 font-semibold">{t('feature2Title')}</h3>
                <p className="text-sm text-gray-600">
                  {t('feature2Desc')}
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="pt-6">
                <div className="mb-4 inline-flex rounded-lg bg-rose-100 p-3 text-rose-600">
                  <Search className="h-6 w-6" />
                </div>
                <h3 className="mb-2 font-semibold">{t('feature3Title')}</h3>
                <p className="text-sm text-gray-600">
                  {t('feature3Desc')}
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Dashboard Preview */}
          <div className="overflow-hidden rounded-xl bg-gray-900 p-6 lg:p-8">
            <div className="mb-6">
              <h2 className="text-xl font-semibold text-white">{t('dashboardSectionTitle')}</h2>
              <p className="mt-2 text-gray-400">
                {t('dashboardSectionDesc')}
              </p>
            </div>
            <div className="aspect-[16/9] overflow-hidden rounded-lg bg-gray-800">
              <img
                src="/placeholder.svg?height=400&width=800"
                alt={t('dashboardSectionTitle')}
                className="h-full w-full object-cover"
              />
            </div>
          </div>

          {/* About Company */}
          <div className="rounded-xl bg-gray-50 p-8">
            <h2 className="mb-4 text-xl font-semibold text-gray-900">{t('companySectionTitle')}</h2>
            <p className="text-gray-600">
              {t('companySectionDesc')}
            </p>
          </div>

          {/* Contact */}
          <div className="text-center">
            <h2 className="mb-6 text-2xl font-semibold text-gray-900">{t('contactSectionTitle')}</h2>
            <div className="flex flex-wrap justify-center gap-4">
              <Button variant="outline" className="gap-2">
                <Mail className="h-4 w-4" />
                {t('contactEmail')}
              </Button>
              <Button variant="outline" className="gap-2">
                <Phone className="h-4 w-4" />
                {t('contactPhone')}
              </Button>
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}

