using RealEstate.Domain.Interfaces;

namespace RealEstate.Domain.Entities
{
    public class City
    {
        public int Id { get; set; }
        public string Name { get; set; }
        public string Slug { get; set; }
        public string Type { get; set; }
        public string NameWithType { get; set; }
        
        public ICollection<District> Districts { get; set; } = new List<District>();
    }
} 