-- Create PropertyViewLog table to track property views
CREATE TABLE IF NOT EXISTS "PropertyViewLog" (
    "Id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    "PropertyId" UUID NOT NULL REFERENCES "Property"("Id") ON DELETE CASCADE,
    "ViewerId" UUID REFERENCES "AppUser"("Id"),
    "ViewerIP" VARCHAR(50),
    "ViewedAt" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "UserAgent" TEXT,
    "ReferrerUrl" TEXT,
    "CreatedAt" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "UpdatedAt" TIMESTAMP
);

-- Create indexes for PropertyViewLog
CREATE INDEX "IDX_PropertyViewLog_PropertyId" ON "PropertyViewLog"("PropertyId");
CREATE INDEX "IDX_PropertyViewLog_ViewerId" ON "PropertyViewLog"("ViewerId");
CREATE INDEX "IDX_PropertyViewLog_ViewedAt" ON "PropertyViewLog"("ViewedAt");

-- Create PropertySpendingLog table to track property-related spending
CREATE TABLE IF NOT EXISTS "PropertySpendingLog" (
    "Id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    "PropertyId" UUID NOT NULL REFERENCES "Property"("Id") ON DELETE CASCADE,
    "UserId" UUID NOT NULL REFERENCES "AppUser"("Id"),
    "Amount" NUMERIC(20,2) NOT NULL,
    "SpendingType" VARCHAR(50) NOT NULL, -- 'extension', 'highlight', etc.
    "TransactionId" UUID REFERENCES "WalletTransactions"("Id"),
    "SpentAt" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "Details" JSONB, -- For storing additional details like duration, highlight type, etc.
    "CreatedAt" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "UpdatedAt" TIMESTAMP
);

-- Create indexes for PropertySpendingLog
CREATE INDEX "IDX_PropertySpendingLog_PropertyId" ON "PropertySpendingLog"("PropertyId");
CREATE INDEX "IDX_PropertySpendingLog_UserId" ON "PropertySpendingLog"("UserId");
CREATE INDEX "IDX_PropertySpendingLog_SpendingType" ON "PropertySpendingLog"("SpendingType");
CREATE INDEX "IDX_PropertySpendingLog_SpentAt" ON "PropertySpendingLog"("SpentAt");

-- Create PropertyEngagementSummary table for caching aggregated data
CREATE TABLE IF NOT EXISTS "PropertyEngagementSummary" (
    "Id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    "PropertyId" UUID NOT NULL REFERENCES "Property"("Id") ON DELETE CASCADE,
    "TotalViews" INTEGER NOT NULL DEFAULT 0,
    "TotalFavorites" INTEGER NOT NULL DEFAULT 0,
    "TotalSpent" NUMERIC(20,2) NOT NULL DEFAULT 0,
    "ExtensionSpent" NUMERIC(20,2) NOT NULL DEFAULT 0,
    "HighlightSpent" NUMERIC(20,2) NOT NULL DEFAULT 0,
    "LastUpdatedAt" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "CreatedAt" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "UpdatedAt" TIMESTAMP
);

-- Create unique index on PropertyId to ensure one summary per property
CREATE UNIQUE INDEX "IDX_PropertyEngagementSummary_PropertyId" ON "PropertyEngagementSummary"("PropertyId");

-- Add comments for better documentation
COMMENT ON TABLE "PropertyViewLog" IS 'Logs each view of a property listing';
COMMENT ON TABLE "PropertySpendingLog" IS 'Logs spending related to property listings (extensions, highlights, etc.)';
COMMENT ON TABLE "PropertyEngagementSummary" IS 'Cached summary of property engagement metrics for faster reporting';

COMMENT ON COLUMN "PropertySpendingLog"."SpendingType" IS 'Type of spending: extension, highlight, etc.';
COMMENT ON COLUMN "PropertySpendingLog"."Details" IS 'JSON with additional details about the spending';
