"use client";
import React, { createContext, useState, useContext, useCallback } from 'react';

// Create a context for the alert popup
const AlertContext = createContext();

// AlertProvider component to wrap around your application
export const AlertProvider = ({ children }) => {
  const [alert, setAlert] = useState(null);

  // Function to trigger an alert
  const showAlert = useCallback(({ title = "Alert", message, errorType, hasCancel, onCancel, onConfirm }) => {
    setAlert({ title, errorType, message, hasCancel, onCancel, onConfirm });
  }, []);

  // Function to close the alert
  const closeAlert = () => setAlert(null);

  return (
    <AlertContext.Provider value={{ alert, showAlert, closeAlert }}>
      {children}
    </AlertContext.Provider>
  );
};

// Custom hook to use the alert functionality
export const useAlert = () => {
  return useContext(AlertContext);
};
