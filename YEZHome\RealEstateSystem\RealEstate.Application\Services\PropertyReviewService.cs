﻿using AutoMapper;
using RealEstate.Application.DTO;
using RealEstate.Application.Interfaces;
using RealEstate.Domain.Entities;
using RealEstate.Domain.Interfaces;

namespace RealEstate.Application.Services
{
    public class PropertyReviewService : IPropertyReviewService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly IMapper _mapper;

        public PropertyReviewService(IUnitOfWork unitOfWork, IMapper mapper)
        {
            _unitOfWork = unitOfWork;
            _mapper = mapper;
        }

        public async Task<IEnumerable<PropertyReviewDto>> GetAllReviewsAsync()
        {
            var properties = await _unitOfWork.PropertyReviews.GetAllAsync(false);
            return _mapper.Map<IEnumerable<PropertyReviewDto>>(properties);
        }

        public async Task<PropertyReviewDto> GetReviewByIdAsync(Guid id)
        {
            var review = await _unitOfWork.PropertyReviews.GetByIdAsync(id, false);
            return _mapper.Map<PropertyReviewDto>(review);
        }

        public async Task<IEnumerable<PropertyReviewDto>> GetReviewsByPropertyIdAsync(Guid propertyId)
        {
            var reviews = await _unitOfWork.PropertyReviews.FindAsync(r => r.PropertyID == propertyId, false);
            return _mapper.Map<IEnumerable<PropertyReviewDto>>(reviews);
        }

        public async Task<IEnumerable<PropertyReviewDto>> GetReviewsByBuyerIdAsync(Guid buyerId)
        {
            var reviews = await _unitOfWork.PropertyReviews.FindAsync(r => r.BuyerID == buyerId, false);
            return _mapper.Map<IEnumerable<PropertyReviewDto>>(reviews);
        }

        public async Task<PropertyReviewDto> CreateReviewAsync(CreatePropertyReviewDto reviewDto, Guid userId)
        {
            var review = _mapper.Map<PropertyReview>(reviewDto);

            review.BuyerID = userId;
            review.CreatedBy = userId;

            await _unitOfWork.PropertyReviews.AddAsync(review);
            await _unitOfWork.SaveChangesAsync();
            return _mapper.Map<PropertyReviewDto>(review);
        }

        public async Task<bool> DeleteReviewAsync(Guid id, Guid userId)
        {
            var review = await _unitOfWork.PropertyReviews.GetByIdAsync(id);
            if (review == null) return false;
            _unitOfWork.PropertyReviews.Remove(review);
            await _unitOfWork.SaveChangesAsync();
            return true;
        }

        public async Task<bool> UpdateReviewAsync(Guid id, CreatePropertyReviewDto reviewDto, Guid userId)
        {
            var review = await _unitOfWork.PropertyReviews.GetByIdAsync(id);
            if (review == null) return false;
            _mapper.Map(reviewDto, review);

            review.UpdatedBy = userId;
            _unitOfWork.PropertyReviews.Update(review);
            await _unitOfWork.SaveChangesAsync();
            return true;
        }
    }
}
