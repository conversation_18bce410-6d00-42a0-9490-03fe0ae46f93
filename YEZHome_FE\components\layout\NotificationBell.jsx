"use client";

import { memo, useState, useEffect, use<PERSON><PERSON>back, useMemo } from "react";
import { Bell, ExternalLink } from "lucide-react";
import { <PERSON>et, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ooter, SheetTrigger } from "@/components/ui/sheet";
import { Link } from "@/i18n/navigation";
import { useTranslations, useLocale } from "next-intl";
import { getLatestNotifications, getUnreadCount, markAsRead } from "@/app/actions/server/notification";
import { getNotificationTypes, getNotificationActionText, isActionableNotification } from "@/lib/constants/notificationTypes";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { NOTIFICATION_TYPE } from "@/lib/enum";
import { formatNotificationTime } from "@/lib/utils";
import { LoadingSpinner } from "@/components/ui/loading-spinner";

// Global variables to ensure only one instance runs
// This is outside the component to ensure it's truly global
let globalTimeoutId = null;
let isInitialFetchDone = false;

// NotificationBell component
const NotificationBell = memo(() => {
  const tNotification = useTranslations("UserNotificationsPage");
  const locale = useLocale();
  const [notifications, setNotifications] = useState({});
  const [unreadCount, setUnreadCount] = useState(0);
  const [loading, setLoading] = useState(false);
  const [open, setOpen] = useState(false);
  const [lastFetchTime, setLastFetchTime] = useState(0);
  const [latestTimestamp, setLatestTimestamp] = useState(null);
  const [activeTab, setActiveTab] = useState(NOTIFICATION_TYPE.SYSTEM);

  // Define notification types with tabs
  const NOTIFICATION_TYPES_TAB = useMemo(() => {
    return getNotificationTypes(tNotification).map((type) => ({
      ...type,
      icon: <type.icon className="h-4 w-4" />,
    }));
  }, [tNotification]);

  // Initialize notification state
  useEffect(() => {
    const initialNotifications = {};
    NOTIFICATION_TYPES_TAB.forEach((type) => {
      initialNotifications[type.id] = [];
    });
    setNotifications(initialNotifications);
  }, [NOTIFICATION_TYPES_TAB]);

  // Fetch only the unread count - using useCallback to ensure stable reference
  const fetchUnreadCount = useCallback(async () => {
    // Prevent duplicate fetches within a short time period (500ms)
    const now = Date.now();
    if (now - lastFetchTime < 500) {
      return;
    }

    setLastFetchTime(now);
    try {
      const countResponse = await getUnreadCount();
      if (countResponse.success) {
        // Force update with a new object reference to ensure re-render
        const newCount = countResponse.data.count || 0;
        setUnreadCount(newCount);
      }
    } catch (error) {
      console.error("Error fetching unread count:", error);
    }
  }, [lastFetchTime]);

  // Fetch all notifications - using useCallback for stable reference
  const fetchAllNotifications = useCallback(async () => {
    // Prevent duplicate fetches within a short time period (500ms)
    const now = Date.now();
    if (now - lastFetchTime < 500) {
      return;
    }

    setLastFetchTime(now);
    setLoading(true);
    try {
      const notifyResponse = await getLatestNotifications(10);
      if (notifyResponse.success && notifyResponse?.data?.items) {
        const items = notifyResponse.data.items;

        // Group notifications by type
        const groupedNotifications = {};
        NOTIFICATION_TYPES_TAB.forEach((type) => {
          groupedNotifications[type.id] = items.filter((item) => item.type === type.id);
        });

        setNotifications(groupedNotifications);

        // Update latest timestamp if we have notifications
        if (items.length > 0) {
          // Find the most recent notification
          const mostRecent = [...items].sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))[0];
          setLatestTimestamp(mostRecent.createdAt);
        }

        // Also update unread count
        const countResponse = await getUnreadCount();
        if (countResponse.success) {
          const newCount = countResponse.data.count || 0;
          setUnreadCount(newCount);
        }
      }
    } catch (error) {
      console.error("Error fetching notifications:", error);
    } finally {
      setLoading(false);
    }
  }, [lastFetchTime, NOTIFICATION_TYPES_TAB]);

  // Fetch only new notifications since the latest timestamp - using useCallback
  const fetchNewestNotifications = useCallback(async () => {
    if (!latestTimestamp) {
      // If we don't have a latest timestamp, fetch all notifications
      return fetchAllNotifications();
    }

    // Prevent duplicate fetches within a short time period (500ms)
    const now = Date.now();
    if (now - lastFetchTime < 500) {
      return;
    }

    setLastFetchTime(now);
    try {
      // For this implementation, we'll still use getLatestNotifications
      // In a real implementation, you would modify your API to accept a since parameter
      const notifyResponse = await getLatestNotifications(10);
      if (notifyResponse.success && notifyResponse?.data?.items) {
        const items = notifyResponse.data.items;

        // Filter to only get notifications newer than our latest timestamp
        const latestDate = new Date(latestTimestamp);
        const newNotifications = items.filter((notification) => new Date(notification.createdAt) > latestDate);

        // If we have new notifications, update the state
        if (newNotifications.length > 0) {
          // Find the new most recent notification
          const mostRecent = [...newNotifications].sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))[0];

          // Update latest timestamp
          setLatestTimestamp(mostRecent.createdAt);

          // Group new notifications by type
          setNotifications((prev) => {
            const updatedNotifications = { ...prev };

            // Process each notification type
            NOTIFICATION_TYPES_TAB.forEach((type) => {
              const typeNewNotifications = newNotifications.filter((n) => n.type === type.id);
              const existingIds = new Set(prev[type.id]?.map((n) => n.id) || []);
              const uniqueNewNotifications = typeNewNotifications.filter((n) => !existingIds.has(n.id));

              // Combine and sort by date (newest first)
              updatedNotifications[type.id] = [...uniqueNewNotifications, ...(prev[type.id] || [])]
                .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
                .slice(0, 10); // Keep only the 10 most recent
            });

            return updatedNotifications;
          });
        }

        // Always update unread count
        const countResponse = await getUnreadCount();
        if (countResponse.success) {
          const newCount = countResponse.data.count || 0;
          setUnreadCount(newCount);
        }
      }
    } catch (error) {
      console.error("Error fetching newest notifications:", error);
    }
  }, [latestTimestamp, lastFetchTime, fetchAllNotifications, NOTIFICATION_TYPES_TAB]);

  // This effect runs once on mount and sets up the initial fetch
  useEffect(() => {
    // Only do the initial fetch if it hasn't been done yet
    if (!isInitialFetchDone) {
      fetchUnreadCount(); // Only fetch the count initially
      isInitialFetchDone = true;
    }

    // Set up a single timeout instead of an interval
    const setupNextFetch = () => {
      // Only set up a new timeout if there isn't one already
      if (globalTimeoutId === null) {
        // Set a new timeout
        globalTimeoutId = setTimeout(() => {
          // If popover is open, fetch newest notifications
          // Otherwise, just fetch the unread count
          if (open) {
            fetchNewestNotifications();
          } else {
            fetchUnreadCount();
          }

          // Clear the global timeout ID before setting up the next one
          globalTimeoutId = null;

          // Set up the next timeout after this one completes
          setupNextFetch();
        }, 120000);
      }
    };

    // Only start the chain of timeouts if there isn't one already
    if (globalTimeoutId === null) {
      setupNextFetch();
    }

    // Clean up on unmount
    return () => {
      // We don't clear the global timeout here because we want it to continue
      // even if this component instance unmounts
    };
  }, [open, fetchUnreadCount, fetchNewestNotifications]);

  // Effect to handle popover open/close
  useEffect(() => {
    if (open) {
      // When popover opens, fetch all notifications
      fetchAllNotifications();
    }
  }, [open, fetchAllNotifications]);

  // Force a refresh of the unread count every minute when the popover is closed
  useEffect(() => {
    let intervalId = null;

    if (!open) {
      intervalId = setInterval(() => {
        fetchUnreadCount();
      }, 60000); // Check every minute when closed
    }

    return () => {
      if (intervalId) {
        clearInterval(intervalId);
      }
    };
  }, [open, fetchUnreadCount]);

  const handleMarkAsRead = async (id, e) => {
    e.preventDefault();
    e.stopPropagation();
    try {
      const response = await markAsRead({ ids: [id] });
      if (response.success) {
        setNotifications((prev) => {
          const updatedNotifications = { ...prev };

          // Update read status in all tabs
          Object.keys(updatedNotifications).forEach((type) => {
            updatedNotifications[type] = updatedNotifications[type].map((n) => (n.id === id ? { ...n, read: true } : n));
          });

          return updatedNotifications;
        });
        setUnreadCount((prev) => Math.max(0, prev - 1));
      }
    } catch (error) {
      console.error("Error marking notification as read:", error);
    }
  };

  const renderLoading = () => {
    return <LoadingSpinner size="sm" text={tNotification("loadingNotifications")} className="h-20" />;
  };

  const renderNotificationList = (notificationList) => {
    if (!notificationList || notificationList.length === 0) {
      return renderNoNotification();
    }

    return notificationList.map((notification) => {
      const isActionable = isActionableNotification(notification);
      return (
        <Link
          key={notification.id}
          href="/user/notifications" // Link to the main notifications page
          className={`block border-b last:border-0 p-3 hover:bg-gray-50 ${!notification.read ? "bg-blue-50" : ""} ${
            isActionable ? "border-l-2 border-l-teal-500" : ""
          }`}
          onClick={() => {
            if (!notification.read) handleMarkAsRead(notification.id, { preventDefault: () => {}, stopPropagation: () => {} });
          }}
        >
          <div className="flex gap-3">
            <div className={`flex-shrink-0 mt-0.5 p-1.5 rounded-full`}>{getNotificationIcon(notification.type)}</div>
            <div className="flex-1 overflow-hidden">
              <h5 className="text-sm font-medium text-gray-900">{notification.title}</h5>
              <p className="text-xs text-gray-600 mt-1 line-clamp-2">{notification.message}</p>
              <p className="text-xs text-gray-500 mt-1">{formatNotificationTime(notification.createdAt, tNotification, locale)}</p>

              {isActionable && (
                <div className="flex items-center gap-1 text-xs text-teal-600 mt-1 hover:underline">
                  <ExternalLink className="h-3 w-3" />
                  <span>{getNotificationActionText(notification, tNotification)}</span>
                </div>
              )}
            </div>
          </div>
        </Link>
      );
    });
  };

  const renderNoNotification = () => {
    return (
      <div className="flex flex-col items-center justify-center p-6 text-center">
        <Bell className="h-8 w-8 text-gray-300 mb-2" />
        <p className="text-sm text-gray-500">{tNotification("noNotificationsMessage")}</p>
      </div>
    );
  };

  const getNotificationIcon = (type) => {
    const notificationType = NOTIFICATION_TYPES_TAB.find((type) => type.id === type);
    return notificationType ? notificationType.icon : <Bell className="h-4 w-4" />;
  };

  const handleTabChange = (value) => {
    setActiveTab(value);
  };

  // Count unread notifications by type
  const getUnreadCountByType = (type) => {
    return notifications[type]?.filter((n) => !n.isRead).length || 0;
  };

  return (
    <Sheet open={open} onOpenChange={setOpen}>
      <SheetTrigger>
        <div className="relative cursor-pointer">
          <Bell className="h-6 w-6 hover:text-coral-600" />
          {unreadCount > 0 && (
            <span className="absolute -top-2 -right-2 bg-coral-600 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
              {unreadCount > 99 ? "99+" : unreadCount}
            </span>
          )}
        </div>
      </SheetTrigger>
      <SheetContent>
        <SheetHeader>
          <SheetTitle className="font-medium leading-none">{tNotification("title")}</SheetTitle>
        </SheetHeader>
        <div className="grid flex-1 auto-rows-min">
          <Tabs value={activeTab} onValueChange={handleTabChange} className="w-full">
            <TabsList className="grid grid-cols-4 w-full">
              {NOTIFICATION_TYPES_TAB.map((type) => (
                <TabsTrigger key={type.id} value={type.id} className="flex items-center gap-1 relative">
                  <span className="sm:hidden">{type.icon}</span>
                  <span className="hidden sm:inline text-xs">{type.label}</span>
                  {getUnreadCountByType(type.id) > 0 && (
                    <span className="absolute -top-1 -right-1 bg-coral-600 text-white text-[10px] rounded-full h-4 w-4 flex items-center justify-center">
                      {getUnreadCountByType(type.id)}
                    </span>
                  )}
                </TabsTrigger>
              ))}
            </TabsList>

            <div className="overflow-auto max-h-[75vh]">
              {NOTIFICATION_TYPES_TAB.map((type) => (
                <TabsContent key={type.id} value={type.id}>
                  {loading && notifications[type.id]?.length === 0 && renderLoading()}
                  {!loading && renderNotificationList(notifications[type.id])}
                </TabsContent>
              ))}
            </div>
          </Tabs>
        </div>
        <SheetFooter>
          <Link href="/user/notifications" className="text-sm text-coral-600 hover:underline flex items-center justify-center gap-1 mt-3 px-4">
            {tNotification("notificationViewAll")}
            <ExternalLink className="h-3.5 w-3.5" />
          </Link>
        </SheetFooter>
      </SheetContent>
    </Sheet>
  );
});

NotificationBell.displayName = "NotificationBell";

export default NotificationBell;
